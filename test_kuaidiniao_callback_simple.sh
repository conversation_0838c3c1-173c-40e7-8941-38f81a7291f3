#!/bin/bash

# 简化的快递鸟外部回调转发测试
# 直接使用管理员API更新订单状态为已取消，验证外部回调转发

echo "🧪 快递鸟外部回调转发测试（简化版）"
echo "================================"

# 配置
API_BASE="http://localhost:8081"
TOKEN="****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"

# 1. 查找一个快递鸟的assigned订单
echo "🔍 1. 查找快递鸟的assigned订单..."
KUAIDINIAO_ORDER_INFO=$(psql "*************************************************/go_kuaidi" -t -c "SELECT id, order_no FROM order_records WHERE provider = 'kuaidiniao' AND status = 'assigned' ORDER BY created_at DESC LIMIT 1;")

if [ -z "$KUAIDINIAO_ORDER_INFO" ]; then
  echo "❌ 没有找到快递鸟的assigned订单，无法测试"
  exit 1
fi

# 解析订单信息
ORDER_ID=$(echo "$KUAIDINIAO_ORDER_INFO" | awk '{print $1}' | xargs)
ORDER_NO=$(echo "$KUAIDINIAO_ORDER_INFO" | awk '{print $2}' | xargs)

echo "✅ 找到快递鸟订单:"
echo "   订单ID: $ORDER_ID"
echo "   订单号: $ORDER_NO"

# 2. 记录回调转发前的状态
echo ""
echo "🔍 2. 记录回调转发前的状态..."
BEFORE_CALLBACK_COUNT=$(psql "*************************************************/callback_receiver" -t -c "SELECT COUNT(*) FROM callback_raw_data WHERE raw_body LIKE '%$ORDER_NO%';" 2>/dev/null | xargs || echo "0")

echo "📋 回调转发前记录数: $BEFORE_CALLBACK_COUNT"

# 3. 使用管理员API更新订单状态为已取消
echo ""
echo "🚀 3. 使用管理员API更新订单状态为已取消..."
UPDATE_RESPONSE=$(curl -s -X PUT "${API_BASE}/api/v1/admin/orders/${ORDER_ID}/status" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d "{
    \"new_status\": \"cancelled\",
    \"reason\": \"测试快递鸟外部回调转发逻辑\"
  }")

echo "📤 更新响应:"
echo "$UPDATE_RESPONSE" | jq .

# 检查更新是否成功
UPDATE_SUCCESS=$(echo "$UPDATE_RESPONSE" | jq -r '.success // false')
if [ "$UPDATE_SUCCESS" != "true" ]; then
  echo "❌ 订单状态更新失败，无法继续测试"
  echo "错误信息: $(echo "$UPDATE_RESPONSE" | jq -r '.message // "未知错误"')"
  exit 1
fi

echo "✅ 订单状态更新成功"

# 4. 等待一段时间让回调处理完成
echo ""
echo "⏳ 4. 等待回调处理完成..."
sleep 10

# 5. 检查订单状态是否更新
echo ""
echo "🔍 5. 检查订单状态是否更新..."
FINAL_STATUS=$(psql "*************************************************/go_kuaidi" -t -c "SELECT status FROM order_records WHERE id = $ORDER_ID;" | xargs)

echo "📋 最终状态: $FINAL_STATUS"

# 6. 检查是否有新的回调转发记录
echo ""
echo "🔍 6. 检查是否有新的回调转发记录..."
AFTER_CALLBACK_COUNT=$(psql "*************************************************/callback_receiver" -t -c "SELECT COUNT(*) FROM callback_raw_data WHERE raw_body LIKE '%$ORDER_NO%';" 2>/dev/null | xargs || echo "0")

echo "📋 回调转发后记录数: $AFTER_CALLBACK_COUNT"

# 7. 检查快递鸟取消回调
echo ""
echo "🔍 7. 检查快递鸟取消回调..."
KUAIDINIAO_CANCEL_COUNT=$(psql "*************************************************/callback_receiver" -t -c "SELECT COUNT(*) FROM callback_raw_data WHERE provider = 'kuaidiniao' AND raw_body LIKE '%$ORDER_NO%' AND raw_body LIKE '%203%';" 2>/dev/null | xargs || echo "0")

echo "📋 快递鸟取消回调记录数: $KUAIDINIAO_CANCEL_COUNT"

# 8. 检查服务日志
echo ""
echo "🔍 8. 检查服务日志中的外部回调转发记录..."

# 查找最新的日志文件
LATEST_LOG=$(ls -t logs/go-kuaidi-local-*.log 2>/dev/null | head -n 1)
if [ -n "$LATEST_LOG" ]; then
  echo "📋 检查日志文件: $LATEST_LOG"
  
  # 查找与该订单相关的回调转发日志
  CALLBACK_LOGS=$(grep -n "$ORDER_NO\|sendInternalCallback\|快递鸟直接取消\|内部回调" "$LATEST_LOG" 2>/dev/null | tail -n 10)
  
  if [ -n "$CALLBACK_LOGS" ]; then
    echo "📋 找到相关回调日志:"
    echo "$CALLBACK_LOGS"
  else
    echo "❌ 没有找到相关的回调转发日志"
  fi
else
  echo "❌ 没有找到日志文件"
fi

# 9. 结果验证
echo ""
echo "🎯 9. 测试结果验证..."
echo "================================"

SUCCESS=true

if [ "$FINAL_STATUS" = "cancelled" ]; then
  echo "✅ 订单状态正确: 已更新为已取消状态"
else
  echo "❌ 订单状态错误: 期望 'cancelled'，实际 '$FINAL_STATUS'"
  SUCCESS=false
fi

if [ "$AFTER_CALLBACK_COUNT" -gt "$BEFORE_CALLBACK_COUNT" ]; then
  echo "✅ 回调转发记录增加: 从 $BEFORE_CALLBACK_COUNT 增加到 $AFTER_CALLBACK_COUNT"
else
  echo "⚠️  回调转发记录未增加: 前 $BEFORE_CALLBACK_COUNT，后 $AFTER_CALLBACK_COUNT"
fi

if [ "$KUAIDINIAO_CANCEL_COUNT" -gt "0" ]; then
  echo "✅ 快递鸟取消回调已生成: $KUAIDINIAO_CANCEL_COUNT 条记录"
else
  echo "❌ 快递鸟取消回调未生成"
  SUCCESS=false
fi

if [ -n "$CALLBACK_LOGS" ]; then
  echo "✅ 找到回调转发相关日志"
else
  echo "⚠️  没有找到回调转发日志（可能需要检查实现）"
fi

# 10. 总结
echo ""
echo "📊 测试总结:"
echo "================================"
echo "订单ID: $ORDER_ID"
echo "订单号: $ORDER_NO"
echo "最终状态: $FINAL_STATUS"
echo "回调前记录数: $BEFORE_CALLBACK_COUNT"
echo "回调后记录数: $AFTER_CALLBACK_COUNT"
echo "快递鸟取消回调数: $KUAIDINIAO_CANCEL_COUNT"

if [ "$SUCCESS" = true ]; then
  echo ""
  echo "🎉 测试基本通过！快递鸟外部回调转发功能正常"
  echo "   ✅ 订单已更新为已取消状态"
  echo "   ✅ 快递鸟取消回调已生成"
  echo "   📋 外部回调转发逻辑已触发"
else
  echo ""
  echo "❌ 测试失败！需要检查实现"
fi

echo ""
echo "💡 注意事项："
echo "   - 这个测试通过管理员API更新订单状态来触发回调转发"
echo "   - 实际的快递鸟取消订单API会调用triggerCancellationCallback方法"
echo "   - 检查服务日志以确认sendInternalCallback方法是否被调用"
