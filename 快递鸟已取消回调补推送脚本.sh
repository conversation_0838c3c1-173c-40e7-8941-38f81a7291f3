#!/bin/bash

# 批量发送快递鸟已取消回调的脚本

# 订单信息数组 (customer_order_no:order_no:tracking_no)
orders=(
    "9147929_157:GK20250731000001666:KDN2507311310000565"
    "9151426_157:GK20250731000002508:KDN2507311510001466"
    "9151569_157:GK20250731000002585:KDN2507311510001955"
    "9151672_157:GK20250731000002651:KDN2507311610000190"
    "9152234_157:GK20250731000002590:KDN2507311510002009"
    "9152684_157:GK20250731000002963:KDN2507311610001812"
    "9152771_157:GK20250731000002993:KDN2507311710000011"
    "9153507_157:GK20250731000003021:KDN2507311710000263"
    "9154425_157:GK20250731000003195:KDN2507311710001429"
    "9154744_157:GK20250731000003400:KDN2507311810000617"
    "9154947_157:GK20250731000003363:KDN2507311810000424"
    "9155155_157:GK20250731000003484:KDN2507311810001187"
    "9155168_157:GK20250731000003491:KDN2507311810001227"
    "9155313_157:GK20250731000003503:KDN2507311810001274"
    "9155351_157:GK20250731000003519:KDN2507311810001414"
    "9155552_157:GK20250731000003570:KDN2507311910000075"
    "9155574_157:GK20250731000003579:KDN2507311910000146"
    "9156555_157:GK20250731000003826:KDN2507312010000389"
    "9156637_157:GK20250731000003781:KDN2507312010000103"
    "9156643_157:GK20250731000003784:KDN2507312010000147"
    "9156760_157:GK20250731000003846:KDN2507312010000508"
    "9156806_157:GK20250731000003877:KDN2507312010000737"
)

echo "🚀 开始批量发送快递鸟已取消回调..."
echo "总共需要发送 ${#orders[@]} 个订单的回调"

success_count=0
failed_count=0

for order_info in "${orders[@]}"; do
    # 解析订单信息
    IFS=':' read -r customer_order_no order_no tracking_no <<< "$order_info"
    
    echo "📦 发送订单 $customer_order_no ($order_no) 的已取消回调..."
    
    # 构造回调数据 (State=203表示已取消)
    callback_data=$(cat <<EOF
{
    "PushTime": "$(date '+%Y-%m-%d %H:%M:%S')",
    "EBusinessID": "1778716",
    "Data": [{
        "ShipperCode": "STO",
        "State": "203",
        "CreateTime": "$(date '+%Y-%m-%d %H:%M:%S')",
        "KDNOrderCode": "$tracking_no",
        "OrderCode": "$order_no",
        "Reason": "订单已取消",
        "OperateType": 1,
        "CallRequestType": "1801"
    }],
    "Count": 1
}
EOF
    )
    
    # URL编码回调数据
    encoded_data=$(echo "$callback_data" | jq -c . | python3 -c "import sys, urllib.parse; print(urllib.parse.quote(sys.stdin.read().strip()))")
    
    # 发送回调请求
    response=$(curl -s -X POST http://localhost:8081/api/v1/callbacks/kuaidiniao \
        -H "Content-Type: application/x-www-form-urlencoded; charset=UTF-8" \
        -H "User-Agent: KuaidiNiao-Internal-Callback/1.0" \
        -d "RequestData=$encoded_data&DataSign=ZTA2YzhjZDRiYWEyZWVlNzkzNjI5ZDQ0NDEwYjg3YmE%3D&RequestType=103")
    
    # 检查响应
    if echo "$response" | grep -q '"Success":true'; then
        echo "✅ 订单 $customer_order_no 回调发送成功"
        ((success_count++))
    else
        echo "❌ 订单 $customer_order_no 回调发送失败: $response"
        ((failed_count++))
    fi
    
    # 避免请求过快，稍微延迟
    sleep 0.5
done

echo ""
echo "📊 批量发送完成统计："
echo "✅ 成功: $success_count"
echo "❌ 失败: $failed_count"
echo "📦 总计: ${#orders[@]}"
