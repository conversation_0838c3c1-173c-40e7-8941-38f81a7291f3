#!/bin/bash

# 重新推送订单状态更新脚本
# 用于处理快递100订单状态没有正确更新的问题

set -e

echo "🔄 重新推送订单状态更新脚本"
echo "================================"

# 数据库连接信息
DB_HOST="*************"
DB_PORT="5432"
DB_USER="postgres"
DB_PASSWORD="gjx6ngf4"
CALLBACK_DB="callback_receiver"
MAIN_DB="go_kuaidi"

# 服务器信息
API_BASE_URL="http://localhost:8081"

# 问题订单列表
ORDERS=(
    "DPK202580385165"
    "DPK202580340237"
    "DPK202579994323"
    "DPK202579941633"
    "DPK202579931694"
    "DPK202579913553"
    "DPK202579712495"
    "DPK202572917023"
    "DPK202572911053"
    "DPK202572900214"
    "DPK202572776229"
    "DPK202571899463"
    "DPK202571736764"
)

echo "📊 1. 分析问题订单状态..."
echo ""

for tracking_no in "${ORDERS[@]}"; do
    echo "🔍 分析订单: $tracking_no"
    
    # 查询主数据库中的订单状态
    ORDER_INFO=$(PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $MAIN_DB -t -c "
    SELECT customer_order_no, order_no, provider, status, updated_at 
    FROM order_records 
    WHERE tracking_no = '$tracking_no';
    " | sed 's/^ *//' | grep -v '^$')
    
    if [ -z "$ORDER_INFO" ]; then
        echo "   ❌ 主数据库中没有找到订单"
        continue
    fi
    
    echo "   📋 主数据库订单信息: $ORDER_INFO"
    
    # 查询最新的回调状态
    LATEST_CALLBACK=$(PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $CALLBACK_DB -t -c "
    SELECT id, received_at, raw_body 
    FROM callback_raw_data 
    WHERE provider = 'kuaidi100' 
      AND raw_body LIKE '%$tracking_no%' 
      AND processed = true
    ORDER BY received_at DESC 
    LIMIT 1;
    " | sed 's/^ *//' | grep -v '^$')
    
    if [ -n "$LATEST_CALLBACK" ]; then
        echo "   📤 找到最新回调: $(echo "$LATEST_CALLBACK" | cut -d'|' -f2)"
        
        # 提取回调ID
        CALLBACK_ID=$(echo "$LATEST_CALLBACK" | cut -d'|' -f1 | tr -d ' ')
        echo "   🔄 准备重新推送回调ID: $CALLBACK_ID"
        
        # 重新推送回调
        echo "   📤 重新推送回调..."
        
        # 获取完整的回调数据
        CALLBACK_DATA=$(PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $CALLBACK_DB -t -c "
        SELECT raw_body 
        FROM callback_raw_data 
        WHERE id = '$CALLBACK_ID';
        " | sed 's/^ *//' | grep -v '^$')
        
        if [ -n "$CALLBACK_DATA" ]; then
            # 发送回调到系统
            RESPONSE=$(curl -s -X POST "$API_BASE_URL/api/v1/callbacks/kuaidi100" \
                -H "Content-Type: application/x-www-form-urlencoded; charset=UTF-8" \
                -H "User-Agent: Kuaidi100-Repush-Script/1.0" \
                -H "X-Repush-Callback: true" \
                -d "$CALLBACK_DATA")
            
            # 检查响应
            if echo "$RESPONSE" | grep -q '"success":true'; then
                echo "   ✅ 回调重新推送成功"
                
                # 等待一下让系统处理
                sleep 2
                
                # 检查订单状态是否更新
                NEW_STATUS=$(PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $MAIN_DB -t -c "
                SELECT status 
                FROM order_records 
                WHERE tracking_no = '$tracking_no';
                " | tr -d ' ')
                
                echo "   📊 重新推送后状态: $NEW_STATUS"
            else
                echo "   ❌ 回调重新推送失败: $RESPONSE"
            fi
        else
            echo "   ❌ 无法获取回调数据"
        fi
    else
        echo "   ⚠️  没有找到相关回调"
    fi
    
    echo ""
    sleep 1
done

echo "📊 2. 最终状态检查..."
echo ""

for tracking_no in "${ORDERS[@]}"; do
    FINAL_STATUS=$(PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $MAIN_DB -t -c "
    SELECT customer_order_no || ' | ' || status || ' | ' || updated_at
    FROM order_records 
    WHERE tracking_no = '$tracking_no';
    " | sed 's/^ *//' | grep -v '^$')
    
    if [ -n "$FINAL_STATUS" ]; then
        echo "📦 $tracking_no: $FINAL_STATUS"
    fi
done

echo ""
echo "🏁 重新推送完成！"
