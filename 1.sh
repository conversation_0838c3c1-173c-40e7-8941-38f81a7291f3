   #!/bin/bash

   echo "卸载 Cursor 编辑器..."

   # 关闭 Cursor
   osascript -e 'quit app "Cursor"'

   # 删除主程序
   rm -rf /Applications/Cursor.app

   # 删除用户配置和缓存
   rm -rf ~/Library/Application\ Support/Cursor
   rm -rf ~/Library/Caches/dev.cursor.Cursor
   rm -rf ~/Library/Preferences/dev.cursor.Cursor.plist
   rm -rf ~/Library/Saved\ Application\ State/dev.cursor.Cursor.savedState
   rm -rf ~/Library/Logs/Cursor

   echo "Cursor 已卸载并清理干净。"