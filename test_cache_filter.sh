#!/bin/bash

echo "🎯 测试缓存明细筛选功能"
echo "========================"

echo ""
echo "📊 1. 测试API返回数据中的成功/失败记录分布:"

# 测试API返回的数据分布
curl -s "http://localhost:8081/api/v1/weight-cache/details?provider=kuaidi100&express_code=STO&page=1&page_size=20" | python3 -c "
import json
import sys
try:
    data = json.load(sys.stdin)
    if data.get('success'):
        records = data.get('data', {}).get('records', [])
        total_records = len(records)
        success_records = len([r for r in records if (r.get('failed_queries', 0) == 0)])
        failed_records = len([r for r in records if (r.get('failed_queries', 0) > 0)])
        
        print(f'✅ API返回数据统计:')
        print(f'   - 总记录数: {total_records}')
        print(f'   - 成功缓存: {success_records} 条')
        print(f'   - 失败记录: {failed_records} 条')
        
        if failed_records > 0:
            print(f'\\n🚨 失败记录示例:')
            for i, record in enumerate([r for r in records if r.get('failed_queries', 0) > 0][:3], 1):
                print(f'   {i}. 路线: {record.get(\"route\", \"N/A\")}')
                print(f'      失败次数: {record.get(\"failed_queries\", 0)}')
                print(f'      总查询次数: {record.get(\"total_queries\", 0)}')
                failure_reasons = record.get('failure_reasons', [])
                if failure_reasons:
                    print(f'      失败原因: {failure_reasons[0].get(\"error_message\", \"N/A\")[:50]}...')
                print()
        
        if success_records > 0:
            print(f'✅ 成功缓存示例:')
            for i, record in enumerate([r for r in records if r.get('failed_queries', 0) == 0][:3], 1):
                print(f'   {i}. 路线: {record.get(\"route\", \"N/A\")}')
                print(f'      价格: ¥{record.get(\"price\", \"N/A\")}')
                print(f'      缓存命中: {record.get(\"cache_hit_count\", 0)} 次')
                print()
    else:
        print(f'❌ API调用失败: {data.get(\"message\", \"未知错误\")}')
except Exception as e:
    print(f'❌ 解析JSON失败: {e}')
"

echo ""
echo "📊 2. 测试有失败记录的特定路线:"

# 测试有失败记录的特定路线
curl -s "http://localhost:8081/api/v1/weight-cache/details?provider=kuaidi100&express_code=STO&from_province=湖南省&to_province=陕西省&weight_kg=2&page=1&page_size=5" | python3 -c "
import json
import sys
try:
    data = json.load(sys.stdin)
    if data.get('success'):
        records = data.get('data', {}).get('records', [])
        print(f'✅ 特定路线查询结果: {len(records)} 条记录')
        
        for record in records:
            print(f'📍 路线: {record.get(\"route\", \"N/A\")}')
            print(f'❌ 失败次数: {record.get(\"failed_queries\", 0)}')
            print(f'📊 总查询次数: {record.get(\"total_queries\", 0)}')
            
            failure_reasons = record.get('failure_reasons', [])
            if failure_reasons:
                print('🚨 失败原因:')
                for i, reason in enumerate(failure_reasons, 1):
                    print(f'   {i}. {reason.get(\"error_message\", \"N/A\")}')
                    print(f'      出现次数: {reason.get(\"count\", 0)}')
            else:
                print('✅ 无失败记录')
            print('---')
    else:
        print(f'❌ API调用失败: {data.get(\"message\", \"未知错误\")}')
except Exception as e:
    print(f'❌ 解析JSON失败: {e}')
"

echo ""
echo "🎯 3. 前端筛选功能测试指南:"
echo "   1. 打开浏览器访问: http://localhost:3008/weight-cache"
echo "   2. 登录系统（用户名: admin, 密码: 1104030777+.aA..@）"
echo "   3. 找到快递100供应商的申通快递(STO)，点击'缓存明细'"
echo "   4. 查看筛选统计信息是否正确显示"
echo "   5. 测试筛选功能:"
echo "      - 选择'全部记录'：显示所有缓存记录"
echo "      - 选择'成功缓存'：只显示failed_queries=0的记录"
echo "      - 选择'失败记录'：只显示failed_queries>0的记录"
echo "   6. 结合搜索功能测试筛选效果"
echo ""
echo "✅ 筛选功能开发完成！"
