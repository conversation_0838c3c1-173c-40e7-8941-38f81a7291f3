# 🗑️ 未使用函数分析报告

## 📊 总体统计
- **未使用函数总数**: 114个
- **涉及文件数**: 约30个文件
- **主要分布**: API处理器、适配器、服务层

## 📁 按文件分类的未使用函数

### 🔧 API处理器层 (api/handler/)

#### api/handler/common.go
- `validateSignature` (行33) - 签名验证函数
- `isDevelopmentEnvironment` (行135) - 开发环境检测函数

#### api/handler/unified_gateway_handler.go
- `createFailedOrderRecord` (行3100) - 创建失败订单记录
- `createFailedOrderRecordSync` (行3155) - 同步创建失败订单记录
- `buildFailureInfo` (行3198) - 构建失败信息
- `buildOrderRequestFromParams` (行3223) - 从参数构建订单请求
- `categorizeFailureReason` (行3306) - 分类失败原因
- `getFailureStageFromError` (行3353) - 从错误获取失败阶段
- `extractErrorCode` (行3384) - 提取错误代码
- `canRetryFromError` (行3412) - 判断错误是否可重试

### 🛡️ 中间件层 (api/middleware/)

#### api/middleware/enhanced_signature_middleware.go
- `validateGatewayTimestamp` (行599) - 验证网关时间戳
- `checkTimestampDuplicate` (行657) - 检查时间戳重复

#### api/middleware/performance_middleware.go
- `compressionStats` (行40) - 压缩统计字段

#### api/middleware/signature_middleware.go
- `isDevHost` (行333) - 开发主机检测
- `buildBodyWithoutSignField` (行419) - 构建无签名字段的请求体

### 🔌 适配器层 (internal/adapter/)

#### internal/adapter/cainiao.go (菜鸟适配器)
**签名和时间相关**:
- `generateSignatureFromInterfaceParams` (行294) - 从接口参数生成签名
- `getCurrentTimestamp` (行379) - 获取当前时间戳
- `isWithinServiceHours` (行390) - 检查是否在服务时间内

**物流查询相关**:
- `queryLogisticsDetail` (行1079) - 查询物流详情
- `calculatePickupTime` (行1236) - 计算取件时间
- `getAreaId` (行1260) - 获取区域ID
- `getItemVersion` (行1267) - 获取商品版本
- `getExpectGotTime` (行1296) - 获取预期取件时间
- `getExpectGotEndTime` (行1305) - 获取预期取件结束时间
- `buildExternalOrderList` (行1314) - 构建外部订单列表

**时间槽转换相关**:
- `convertToSimpleTimeSlots` (行1454) - 转换为简单时间槽
- `convertTimeSlots` (行1489) - 转换时间槽
- `convertToTrackInfo` (行1523) - 转换为跟踪信息
- `parseTrackTime` (行1572) - 解析跟踪时间

**状态映射相关**:
- `mapLogisticsStatus` (行1595) - 映射物流状态
- `getStatusCode` (行1650) - 获取状态代码
- `mapOverallLogisticsStatus` (行1691) - 映射整体物流状态
- `getLogisticsStatusDesc` (行1713) - 获取物流状态描述
- `isDelivered` (行1733) - 判断是否已送达

**其他工具函数**:
- `getValidAddress` (行1812) - 获取有效地址
- `getCainiaoOrderIdByTrackingNo` (行2249) - 通过跟踪号获取菜鸟订单ID
- `callAPIWithArrayParams` (行2325) - 使用数组参数调用API

#### internal/adapter/cainiao_callback.go
- `getOrderStatusDesc` (行363) - 获取订单状态描述

#### internal/adapter/kuaidi100.go (快递100适配器)
- `queryAllExpressPrice` (行347) - 查询所有快递价格
- `callPollAPI` (行1064) - 调用轮询API
- `convertPayMethod` (行1352) - 转换支付方式
- `calculateChargedWeight` (行1473) - 计算计费重量
- `calculateChargedWeightFromVolumeCm3` (行1490) - 从体积计算计费重量

#### internal/adapter/kuaidiniao.go (快递鸟适配器)
- `formatPickupTime` (行516) - 格式化取件时间
- `parsePickupTimeInfo` (行603) - 解析取件时间信息

#### internal/adapter/yida.go (易达适配器)
- `calculateChargedWeight` (行1367) - 计算计费重量
- `calculateChargedWeightFromVolumeCm3` (行1384) - 从体积计算计费重量

#### internal/adapter/yuntong.go (云通适配器)
- `calculateChargedWeight` (行1313) - 计算计费重量
- `calculateChargedWeightFromVolumeCm3` (行1330) - 从体积计算计费重量

#### 工单适配器 (internal/adapter/workorder/)
- `convertUnifiedTypeToKuaidi100Type` (kuaidi100_adapter.go:537) - 转换统一类型到快递100类型
- `convertUnifiedTypeToYidaType` (yida_adapter.go:457) - 转换统一类型到易达类型
- `convertUnifiedTypeToYuntongType` (yuntong_adapter.go:632) - 转换统一类型到云通类型

### 🏗️ 配置和基础设施层

#### internal/config/
- `getMapStringInterface` (adapter.go:196) - 获取映射字符串接口
- `loadProviderConfig` (provider_config.go:62) - 加载供应商配置

#### internal/database/
- `setCache` (query_optimizer.go:244) - 设置缓存
- `evictOldestCache` (query_optimizer.go:269) - 驱逐最旧缓存

#### internal/express/
- `getInterfaceTypeFromCache` (cache_service.go:769) - 从缓存获取接口类型
- `shouldUseUnifiedInterface` (cache_service.go:778) - 是否应使用统一接口

### 🔧 服务层 (internal/service/)

#### 管理订单服务 (admin_order_service.go)
- `stringToPointer` (行418) - 字符串转指针
- `stringToTimePointer` (行426) - 字符串转时间指针
- `isOrderStatusError` (行1396) - 判断订单状态错误
- `contains` (行1416) - 包含检查
- `containsInMiddle` (行1425) - 中间包含检查
- `mapKuaidi100StatusToLocal` (行1437) - 映射快递100状态到本地
- `parseTime` (行1709) - 解析时间

#### 余额服务 (balance_service.go)
- `chargeWithSpecificType` (行1886) - 使用特定类型收费
- `refundWithSpecificType` (行1987) - 使用特定类型退款

#### 计费服务 (billing_service.go)
- `processAdditionalCharge` (行579) - 处理额外收费
- `processAdditionalChargeWithPolicy` (行694) - 使用策略处理额外收费
- `generateBillingAdjustmentHash` (行817) - 生成计费调整哈希
- `checkBillingAdjustmentIdempotencyFallback` (行825) - 检查计费调整幂等性回退
- `insertBillingAdjustmentAuditFallback` (行861) - 插入计费调整审计回退
- `createBillingDifferenceTransaction` (行889) - 创建计费差异事务

## 🎯 清理建议

### 高优先级 (立即删除)
1. **重复的计费重量计算函数** - 各适配器中的 `calculateChargedWeight` 系列函数
2. **未使用的工单类型转换函数** - workorder适配器中的转换函数
3. **废弃的缓存相关函数** - 查询优化器和缓存服务中的函数

### 中优先级 (评估后删除)
1. **菜鸟适配器中的大量未使用函数** - 可能是历史遗留或备用功能
2. **统一网关处理器中的失败处理函数** - 可能是未完成的功能
3. **中间件中的验证函数** - 可能是备用的安全检查

### 低优先级 (保留观察)
1. **工具函数** - 如时间解析、字符串转换等
2. **开发环境检测函数** - 可能在特定场景下使用

## 📋 下一步行动
1. 创建代码清理任务列表
2. 按优先级逐步删除未使用函数
3. 运行测试确保删除不影响功能
4. 提交代码清理的Git记录
