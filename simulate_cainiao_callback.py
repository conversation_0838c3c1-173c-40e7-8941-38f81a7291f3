#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import urllib.parse
import json
import time

def simulate_cainiao_callback():
    """模拟菜鸟回调重推"""
    
    # 原始回调数据（从数据库中获取）
    raw_data = "data_digest=93SzC22rtqWQJqb%2BlRNXnw%3D%3D&from_code=tdtradebusiness&msg_type=GUOGUO_PUSH_ORDER_EVENT&partner_code=20df7df0fa15494c801b249a8b798879&msg_id=18115562440149520&logistics_interface=%7B%22externalOrder%22%3A%7B%22externalBizIdList%22%3A%5B%22GK20250718000000003%22%5D%2C%22orderId%22%3A%2218115562440149520%22%2C%22orderStatusDesc%22%3A%22%E5%BE%85%E5%8F%96%E4%BB%B6%22%2C%22orderStatusCode%22%3A%2220%22%7D%2C%22orderEvent%22%3A%7B%22eventDesc%22%3A%22%E5%AF%B9%E6%80%BB%E7%94%A9%E5%8D%95%E7%9C%9F%E5%AE%9E%E5%BF%AB%E9%80%92%E5%91%98%E4%BF%A1%E6%81%AF%E5%9B%9E%E4%BC%A0%22%2C%22eventData%22%3A%7B%22itemId%22%3A%223000000080%22%2C%22courierName%22%3A%22%E8%92%8B%E7%90%A6%22%2C%22courierCompany%22%3A%22%E7%94%B3%E9%80%9A%22%2C%22lpCode%22%3A%22LP00748489808104%22%2C%22courierMobile%22%3A%2213316941765%22%7D%2C%22eventType%22%3A%22OUT_ORDER_COURIER_UPDATE%22%7D%7D"
    
    # 解析URL编码的表单数据
    parsed_data = urllib.parse.parse_qs(raw_data)
    
    # 提取各个字段
    data_digest = parsed_data.get('data_digest', [''])[0]
    from_code = parsed_data.get('from_code', [''])[0]
    msg_type = parsed_data.get('msg_type', [''])[0]
    partner_code = parsed_data.get('partner_code', [''])[0]
    msg_id = parsed_data.get('msg_id', [''])[0]
    logistics_interface = parsed_data.get('logistics_interface', [''])[0]
    
    # 解码logistics_interface JSON数据
    try:
        decoded_logistics = urllib.parse.unquote(logistics_interface)
        logistics_json = json.loads(decoded_logistics)
        print("📋 解析后的菜鸟回调数据:")
        print(json.dumps(logistics_json, ensure_ascii=False, indent=2))
    except Exception as e:
        print(f"❌ 解析logistics_interface失败: {e}")
        return
    
    # 构造回调请求数据
    callback_data = {
        'data_digest': data_digest,
        'from_code': from_code,
        'msg_type': msg_type,
        'partner_code': partner_code,
        'msg_id': msg_id,
        'logistics_interface': logistics_interface
    }
    
    # 回调接口地址
    callback_url = "http://localhost:8081/api/v1/callback/cainiao"
    
    print(f"\n🚀 开始模拟菜鸟回调重推...")
    print(f"📍 回调地址: {callback_url}")
    print(f"📦 订单号: {logistics_json.get('externalOrder', {}).get('externalBizIdList', [''])[0]}")
    print(f"📱 快递员: {logistics_json.get('orderEvent', {}).get('eventData', {}).get('courierName', '')}")
    print(f"📞 电话: {logistics_json.get('orderEvent', {}).get('eventData', {}).get('courierMobile', '')}")
    print(f"🏢 快递公司: {logistics_json.get('orderEvent', {}).get('eventData', {}).get('courierCompany', '')}")
    print(f"🎫 取件码: {logistics_json.get('orderEvent', {}).get('eventData', {}).get('gotCode', '无')}")
    print(f"📍 网点代码: {logistics_json.get('orderEvent', {}).get('eventData', {}).get('lpCode', '')}")
    print(f"⚡ 事件类型: {logistics_json.get('orderEvent', {}).get('eventType', '')}")
    
    # 发送HTTP POST请求
    try:
        headers = {
            'Content-Type': 'application/x-www-form-urlencoded',
            'User-Agent': 'CainiaoCallback/1.0'
        }
        
        print(f"\n📡 发送回调请求...")
        start_time = time.time()
        
        response = requests.post(
            callback_url,
            data=callback_data,
            headers=headers,
            timeout=30
        )
        
        end_time = time.time()
        response_time = round((end_time - start_time) * 1000, 2)
        
        print(f"✅ 回调请求完成:")
        print(f"   响应状态码: {response.status_code}")
        print(f"   响应时间: {response_time}ms")
        print(f"   响应内容: {response.text}")
        
        if response.status_code == 200:
            print("🎉 回调处理成功！")
        else:
            print(f"⚠️ 回调处理异常，状态码: {response.status_code}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 回调请求失败: {e}")
        
    except Exception as e:
        print(f"❌ 处理异常: {e}")

if __name__ == "__main__":
    simulate_cainiao_callback() 