#!/bin/bash

# 命令行重推快递100订单状态脚本

set -e

echo "🔄 命令行重推快递100订单状态"
echo "================================"

# 数据库连接信息
DB_HOST="*************"
DB_PORT="5432"
DB_USER="postgres"
DB_PASSWORD="gjx6ngf4"
CALLBACK_DB="callback_receiver"
MAIN_DB="go_kuaidi"

# API配置
API_BASE_URL="http://localhost:8081"

# 问题订单列表
ORDERS=(
    "DPK202580385165"
    "DPK202580340237"
    "DPK202579994323"
    "DPK202579941633"
    "DPK202579931694"
    "DPK202579913553"
    "DPK202579712495"
    "DPK202572917023"
    "DPK202572911053"
    "DPK202572900214"
    "DPK202572776229"
    "DPK202571899463"
    "DPK202571736764"
)

success_count=0
failed_count=0

echo "📊 开始处理 ${#ORDERS[@]} 个订单..."
echo ""

for i in "${!ORDERS[@]}"; do
    tracking_no="${ORDERS[$i]}"
    current_index=$((i + 1))
    
    echo "🔍 处理订单 [$current_index/${#ORDERS[@]}]: $tracking_no"
    
    # 1. 检查主数据库中的当前状态
    echo "   📊 检查当前状态..."
    CURRENT_INFO=$(PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $MAIN_DB -t -c "
    SELECT customer_order_no || '|' || status || '|' || updated_at
    FROM order_records 
    WHERE tracking_no = '$tracking_no';
    " 2>/dev/null | tr -d ' ' | grep -v '^$' | head -1)
    
    if [ -z "$CURRENT_INFO" ]; then
        echo "   ❌ 主数据库中没有找到订单"
        ((failed_count++))
        continue
    fi
    
    CUSTOMER_ORDER_NO=$(echo "$CURRENT_INFO" | cut -d'|' -f1)
    CURRENT_STATUS=$(echo "$CURRENT_INFO" | cut -d'|' -f2)
    CURRENT_UPDATED_AT=$(echo "$CURRENT_INFO" | cut -d'|' -f3)
    
    echo "   📋 当前状态: $CUSTOMER_ORDER_NO | $CURRENT_STATUS | $CURRENT_UPDATED_AT"
    
    # 2. 获取最新的回调数据
    echo "   📤 获取最新回调数据..."
    CALLBACK_DATA=$(PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $CALLBACK_DB -t -c "
    SELECT raw_body 
    FROM callback_raw_data 
    WHERE provider = 'kuaidi100' 
      AND raw_body LIKE '%$tracking_no%' 
      AND processed = true
    ORDER BY received_at DESC 
    LIMIT 1;
    " 2>/dev/null | tr -d '\n' | sed 's/^ *//' | grep -v '^$')
    
    if [ -z "$CALLBACK_DATA" ]; then
        echo "   ❌ 没有找到相关回调数据"
        ((failed_count++))
        continue
    fi
    
    echo "   ✅ 找到回调数据"
    
    # 3. 重新推送回调
    echo "   🔄 重新推送回调..."
    
    RESPONSE=$(curl -s -w "HTTP_CODE:%{http_code}" -X POST "$API_BASE_URL/api/v1/callbacks/kuaidi100" \
        -H "Content-Type: application/x-www-form-urlencoded; charset=UTF-8" \
        -H "User-Agent: Command-Repush-Script/1.0" \
        -H "X-Repush-Callback: true" \
        -H "X-Repush-Order: $tracking_no" \
        -d "$CALLBACK_DATA" 2>/dev/null || echo "CURL_ERROR")
    
    if [ "$RESPONSE" = "CURL_ERROR" ]; then
        echo "   ❌ 网络请求失败"
        ((failed_count++))
        continue
    fi
    
    # 提取HTTP状态码
    HTTP_CODE=$(echo "$RESPONSE" | grep -o "HTTP_CODE:[0-9]*" | cut -d: -f2)
    RESPONSE_BODY=$(echo "$RESPONSE" | sed 's/HTTP_CODE:[0-9]*$//')
    
    if [ "$HTTP_CODE" = "200" ]; then
        echo "   ✅ 回调发送成功"
        
        # 等待系统处理
        echo "   ⏳ 等待3秒让系统处理..."
        sleep 3
        
        # 检查状态是否更新
        echo "   📊 检查更新后状态..."
        NEW_INFO=$(PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $MAIN_DB -t -c "
        SELECT status || '|' || updated_at
        FROM order_records 
        WHERE tracking_no = '$tracking_no';
        " 2>/dev/null | tr -d ' ' | grep -v '^$' | head -1)
        
        if [ -n "$NEW_INFO" ]; then
            NEW_STATUS=$(echo "$NEW_INFO" | cut -d'|' -f1)
            NEW_UPDATED_AT=$(echo "$NEW_INFO" | cut -d'|' -f2)
            
            echo "   📊 重新推送后状态: $NEW_STATUS | $NEW_UPDATED_AT"
            
            if [ "$NEW_STATUS" != "$CURRENT_STATUS" ] || [ "$NEW_UPDATED_AT" != "$CURRENT_UPDATED_AT" ]; then
                echo "   🎉 状态更新成功: $CURRENT_STATUS -> $NEW_STATUS"
                ((success_count++))
            else
                echo "   ⚠️  状态未更新，可能需要手动检查"
                ((failed_count++))
            fi
        else
            echo "   ❌ 无法查询更新后状态"
            ((failed_count++))
        fi
    else
        echo "   ❌ 回调发送失败，HTTP状态码: $HTTP_CODE"
        echo "   📋 响应内容: $RESPONSE_BODY"
        ((failed_count++))
    fi
    
    echo ""
    sleep 1
done

echo "📊 处理结果统计:"
echo "   ✅ 成功处理: $success_count 个订单"
echo "   ❌ 处理失败: $failed_count 个订单"
echo "   📦 总计订单: ${#ORDERS[@]} 个"

echo ""
echo "📋 最终状态检查:"
echo ""

for tracking_no in "${ORDERS[@]}"; do
    FINAL_INFO=$(PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $MAIN_DB -t -c "
    SELECT customer_order_no || ' | ' || status || ' | ' || updated_at
    FROM order_records 
    WHERE tracking_no = '$tracking_no';
    " 2>/dev/null | sed 's/^ *//' | grep -v '^$' | head -1)
    
    if [ -n "$FINAL_INFO" ]; then
        echo "📦 $tracking_no: $FINAL_INFO"
    else
        echo "❌ $tracking_no: 未找到订单"
    fi
done

echo ""
echo "🏁 命令行重推完成！"
