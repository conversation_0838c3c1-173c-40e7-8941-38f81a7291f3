#!/bin/bash

# 直接测试快递鸟取消订单的外部回调转发
# 通过模拟快递鸟回调来验证外部转发逻辑

echo "🧪 快递鸟取消订单外部回调转发测试（直接测试）"
echo "================================"

# 配置
API_BASE="http://localhost:8081"

# 1. 查找一个快递鸟的assigned订单
echo "🔍 1. 查找快递鸟的assigned订单..."
KUAIDINIAO_ORDER_INFO=$(psql "*************************************************/go_kuaidi" -t -c "SELECT order_no, tracking_no FROM order_records WHERE provider = 'kuaidiniao' AND status = 'assigned' ORDER BY created_at DESC LIMIT 1;")

if [ -z "$KUAIDINIAO_ORDER_INFO" ]; then
  echo "❌ 没有找到快递鸟的assigned订单，无法测试"
  exit 1
fi

# 解析订单信息
ORDER_NO=$(echo "$KUAIDINIAO_ORDER_INFO" | awk '{print $1}' | xargs)
TRACKING_NO=$(echo "$KUAIDINIAO_ORDER_INFO" | awk '{print $2}' | xargs)

echo "✅ 找到快递鸟订单:"
echo "   订单号: $ORDER_NO"
echo "   运单号: $TRACKING_NO"

# 2. 记录回调转发前的状态
echo ""
echo "🔍 2. 记录回调转发前的状态..."
BEFORE_CALLBACK_COUNT=$(psql "*************************************************/callback_receiver" -t -c "SELECT COUNT(*) FROM callback_raw_data WHERE raw_body LIKE '%$ORDER_NO%';" 2>/dev/null | xargs || echo "0")

echo "📋 回调转发前记录数: $BEFORE_CALLBACK_COUNT"

# 3. 构建快递鸟取消回调数据
echo ""
echo "🚀 3. 构建快递鸟取消回调数据..."

# 构造快递鸟格式的取消回调数据
CALLBACK_DATA=$(cat <<EOF
{
    "PushTime": "$(date '+%Y-%m-%d %H:%M:%S')",
    "EBusinessID": "1778716",
    "Data": [{
        "ShipperCode": "STO",
        "State": "203",
        "CreateTime": "$(date '+%Y-%m-%d %H:%M:%S')",
        "KDNOrderCode": "$TRACKING_NO",
        "OrderCode": "$ORDER_NO",
        "Reason": "测试快递鸟外部回调转发逻辑",
        "OperateType": 1,
        "CallRequestType": "1801"
    }],
    "Count": 1
}
EOF
)

echo "📋 回调数据:"
echo "$CALLBACK_DATA" | jq .

# 4. URL编码回调数据
echo ""
echo "🔗 4. URL编码回调数据..."
ENCODED_DATA=$(echo "$CALLBACK_DATA" | jq -c . | python3 -c "import sys, urllib.parse; print(urllib.parse.quote(sys.stdin.read().strip()))")

# 构建表单数据
FORM_DATA="RequestData=$ENCODED_DATA&DataSign=TEST_CALLBACK&RequestType=103"

echo "📋 表单数据长度: ${#FORM_DATA}"

# 5. 发送快递鸟回调到本地接口
echo ""
echo "🌐 5. 发送快递鸟回调到本地接口..."
CALLBACK_RESPONSE=$(curl -s -X POST "${API_BASE}/api/v1/callbacks/kuaidiniao" \
  -H "Content-Type: application/x-www-form-urlencoded;charset=UTF-8" \
  -H "User-Agent: KuaidiNiao-Test-Callback/1.0" \
  -H "X-Test-Callback: true" \
  -d "$FORM_DATA")

echo "📤 回调响应:"
echo "$CALLBACK_RESPONSE" | jq . 2>/dev/null || echo "$CALLBACK_RESPONSE"

# 检查回调是否成功
CALLBACK_SUCCESS=$(echo "$CALLBACK_RESPONSE" | jq -r '.Success // false' 2>/dev/null || echo "false")
if [ "$CALLBACK_SUCCESS" != "true" ]; then
  echo "⚠️  回调响应不是成功状态，但继续测试"
fi

# 6. 等待一段时间让回调处理完成
echo ""
echo "⏳ 6. 等待回调处理完成..."
sleep 8

# 7. 检查订单状态是否更新
echo ""
echo "🔍 7. 检查订单状态是否更新..."
FINAL_STATUS=$(psql "*************************************************/go_kuaidi" -t -c "SELECT status FROM order_records WHERE order_no = '$ORDER_NO';" | xargs)

echo "📋 最终状态: $FINAL_STATUS"

# 8. 检查是否有新的回调转发记录
echo ""
echo "🔍 8. 检查是否有新的回调转发记录..."
AFTER_CALLBACK_COUNT=$(psql "*************************************************/callback_receiver" -t -c "SELECT COUNT(*) FROM callback_raw_data WHERE raw_body LIKE '%$ORDER_NO%';" 2>/dev/null | xargs || echo "0")

echo "📋 回调转发后记录数: $AFTER_CALLBACK_COUNT"

# 9. 检查快递鸟取消回调
echo ""
echo "🔍 9. 检查快递鸟取消回调..."
KUAIDINIAO_CANCEL_COUNT=$(psql "*************************************************/callback_receiver" -t -c "SELECT COUNT(*) FROM callback_raw_data WHERE provider = 'kuaidiniao' AND raw_body LIKE '%$ORDER_NO%' AND raw_body LIKE '%203%';" 2>/dev/null | xargs || echo "0")

echo "📋 快递鸟取消回调记录数: $KUAIDINIAO_CANCEL_COUNT"

# 10. 检查最新的快递鸟回调记录
echo ""
echo "🔍 10. 检查最新的快递鸟回调记录..."
LATEST_KUAIDINIAO_CALLBACK=$(psql "*************************************************/callback_receiver" -t -c "SELECT id, received_at FROM callback_raw_data WHERE provider = 'kuaidiniao' AND raw_body LIKE '%$ORDER_NO%' ORDER BY received_at DESC LIMIT 1;" 2>/dev/null)

if [ -n "$LATEST_KUAIDINIAO_CALLBACK" ]; then
  echo "📋 最新快递鸟回调记录: $LATEST_KUAIDINIAO_CALLBACK"
else
  echo "❌ 没有找到快递鸟回调记录"
fi

# 11. 检查服务日志
echo ""
echo "🔍 11. 检查服务日志中的回调处理记录..."

# 查找最新的日志文件
LATEST_LOG=$(ls -t logs/go-kuaidi-local-*.log 2>/dev/null | head -n 1)
if [ -n "$LATEST_LOG" ]; then
  echo "📋 检查日志文件: $LATEST_LOG"
  
  # 查找与该订单相关的回调处理日志
  CALLBACK_LOGS=$(grep -n "$ORDER_NO\|快递鸟回调\|kuaidiniao.*callback\|State.*203" "$LATEST_LOG" 2>/dev/null | tail -n 15)
  
  if [ -n "$CALLBACK_LOGS" ]; then
    echo "📋 找到相关回调日志:"
    echo "$CALLBACK_LOGS"
  else
    echo "❌ 没有找到相关的回调处理日志"
  fi
else
  echo "❌ 没有找到日志文件"
fi

# 12. 结果验证
echo ""
echo "🎯 12. 测试结果验证..."
echo "================================"

SUCCESS=true

if [ "$FINAL_STATUS" = "cancelled" ]; then
  echo "✅ 订单状态正确: 已更新为已取消状态"
else
  echo "⚠️  订单状态: $FINAL_STATUS (可能需要时间更新)"
fi

if [ "$AFTER_CALLBACK_COUNT" -gt "$BEFORE_CALLBACK_COUNT" ]; then
  echo "✅ 回调转发记录增加: 从 $BEFORE_CALLBACK_COUNT 增加到 $AFTER_CALLBACK_COUNT"
else
  echo "❌ 回调转发记录未增加: 前 $BEFORE_CALLBACK_COUNT，后 $AFTER_CALLBACK_COUNT"
  SUCCESS=false
fi

if [ "$KUAIDINIAO_CANCEL_COUNT" -gt "0" ]; then
  echo "✅ 快递鸟取消回调已记录: $KUAIDINIAO_CANCEL_COUNT 条记录"
else
  echo "❌ 快递鸟取消回调未记录"
  SUCCESS=false
fi

if [ -n "$CALLBACK_LOGS" ]; then
  echo "✅ 找到回调处理相关日志"
else
  echo "⚠️  没有找到回调处理日志"
fi

# 13. 总结
echo ""
echo "📊 测试总结:"
echo "================================"
echo "订单号: $ORDER_NO"
echo "运单号: $TRACKING_NO"
echo "最终状态: $FINAL_STATUS"
echo "回调前记录数: $BEFORE_CALLBACK_COUNT"
echo "回调后记录数: $AFTER_CALLBACK_COUNT"
echo "快递鸟取消回调数: $KUAIDINIAO_CANCEL_COUNT"

if [ "$SUCCESS" = true ]; then
  echo ""
  echo "🎉 测试通过！快递鸟外部回调转发功能正常"
  echo "   ✅ 快递鸟取消回调已记录"
  echo "   ✅ 回调转发记录已增加"
  echo "   📋 外部回调转发逻辑正常工作"
else
  echo ""
  echo "❌ 测试失败！需要检查实现"
fi

echo ""
echo "💡 注意事项："
echo "   - 这个测试直接发送快递鸟回调到本地接口"
echo "   - 验证回调接收、处理和外部转发的完整流程"
echo "   - 检查callback_receiver数据库中的记录变化"
