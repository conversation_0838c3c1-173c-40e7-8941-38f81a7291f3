#!/bin/bash

echo "🔧 测试前端响应格式修复"
echo "========================="

echo ""
echo "📊 1. 测试后端API响应格式:"

# 测试后端API响应格式
curl -s "http://localhost:8081/api/v1/weight-cache/details?provider=kuaidi100&express_code=STO&page=1&page_size=3" | python3 -c "
import json
import sys
try:
    data = json.load(sys.stdin)
    print('✅ 后端API响应结构:')
    if data.get('success'):
        data_section = data.get('data', {})
        print(f'   - success: {data.get(\"success\")}')
        print(f'   - data.records: {len(data_section.get(\"records\", []))} 条记录')
        print(f'   - data.total: {data_section.get(\"total\", 0)}')
        print(f'   - data.page: {data_section.get(\"page\", 0)}')
        print(f'   - data.page_size: {data_section.get(\"page_size\", 0)}')
        
        # 检查第一条记录的结构
        records = data_section.get('records', [])
        if records:
            first_record = records[0]
            print(f'\\n📋 第一条记录字段:')
            print(f'   - route: {first_record.get(\"route\", \"N/A\")}')
            print(f'   - price: {first_record.get(\"price\", \"N/A\")}')
            print(f'   - failed_queries: {first_record.get(\"failed_queries\", 0)}')
            print(f'   - total_queries: {first_record.get(\"total_queries\", 0)}')
            print(f'   - failure_reasons: {len(first_record.get(\"failure_reasons\", []))} 条')
            
            # 显示失败原因
            failure_reasons = first_record.get('failure_reasons', [])
            if failure_reasons:
                print(f'\\n🚨 失败原因详情:')
                for i, reason in enumerate(failure_reasons, 1):
                    print(f'   {i}. {reason.get(\"error_message\", \"N/A\")[:60]}...')
                    print(f'      次数: {reason.get(\"count\", 0)}, 来源: {reason.get(\"source\", \"N/A\")}')
        else:
            print('   ⚠️  没有记录数据')
    else:
        print(f'❌ API调用失败: {data.get(\"message\", \"未知错误\")}')
except Exception as e:
    print(f'❌ 解析JSON失败: {e}')
"

echo ""
echo "🎯 2. 前端测试指南:"
echo "   1. 打开浏览器访问: http://localhost:3008/weight-cache"
echo "   2. 登录系统（用户名: admin, 密码: 1104030777+.aA..@）"
echo "   3. 找到快递100供应商的申通快递(STO)，点击'缓存明细'"
echo "   4. 检查是否能看到缓存记录列表"
echo "   5. 查看表格中的'失败统计'和'失败原因'列是否正确显示"
echo ""
echo "✅ 如果前端能正常显示数据，说明响应格式修复成功！"
