# 🔄 统一回调外部转发器智能重试机制开发计划

## 📋 项目概述

为统一回调外部转发器添加智能重试机制，实现企业级的回调失败自动重试功能，提高系统的可靠性和用户体验。

## 🎯 核心功能需求

### 1. 智能重试机制
- ✅ **自动重试**: 回调转发失败时自动进行重试
- ✅ **重试间隔**: 默认10分钟，支持数据库配置
- ✅ **最大重试次数**: 默认10次，支持数据库配置
- ✅ **重试策略**: 支持固定间隔和指数退避两种策略
- ✅ **停止条件**: 达到最大重试次数后自动停止

### 2. 配置管理
- ✅ **数据库驱动**: 所有配置从数据库获取，支持热重载
- ✅ **动态配置**: 无需重启服务即可更新配置
- ✅ **配置验证**: 配置参数合法性验证
- ✅ **默认配置**: 提供合理的默认配置值

### 3. 监控和日志
- ✅ **详细日志**: 记录每次重试的详细信息
- ✅ **性能指标**: 监控重试成功率、平均耗时等
- ✅ **状态查询**: 提供重试状态查询接口
- ✅ **实时监控**: 支持Prometheus指标导出

### 4. 管理功能
- ✅ **手动重试**: 支持运营人员手动触发重试
- ✅ **取消重试**: 支持取消待执行的重试任务
- ✅ **批量操作**: 支持批量重试和取消操作
- ✅ **权限控制**: 基于JWT和角色的权限管理

## 🏗️ 技术架构设计

### 1. 数据库表结构

#### 重试配置表 (`callback_retry_configs`)
```sql
- id: UUID主键
- config_group: 配置组名
- retry_interval_minutes: 重试间隔（分钟）
- max_retry_count: 最大重试次数
- retry_strategy: 重试策略（fixed/exponential）
- exponential_base: 指数退避基数
- max_interval_minutes: 最大间隔（分钟）
- enabled: 是否启用
- description: 配置描述
```

#### 重试记录表 (`callback_retry_records`)
```sql
- id: UUID主键
- forward_record_id: 转发记录ID（外键）
- callback_record_id: 回调记录ID（外键）
- user_id: 用户ID
- retry_attempt: 重试次数
- scheduled_at: 调度时间
- executed_at: 执行时间
- status: 状态（pending/executing/success/failed/cancelled）
- error_message: 错误信息
- http_status: HTTP状态码
- response_data: 响应数据
- execution_duration_ms: 执行耗时（毫秒）
```

#### 扩展现有表
```sql
-- unified_callback_records 扩展字段
- retry_enabled: 是否启用重试
- retry_stopped_at: 重试停止时间
- retry_stop_reason: 停止原因

-- callback_forward_records 扩展字段
- retry_enabled: 是否启用重试
- last_retry_at: 最后重试时间
- next_retry_at: 下次重试时间
```

### 2. 核心组件架构

#### 重试调度器 (`CallbackRetryScheduler`)
- **功能**: 管理重试任务的调度和执行
- **特性**: 支持多工作协程、配置热重载、性能监控
- **策略**: 固定间隔和指数退避重试策略

#### 服务管理器 (`RetryServiceManager`)
- **功能**: 管理重试服务的生命周期
- **特性**: 初始化、启动、停止、状态查询
- **依赖**: 避免循环依赖的设计

#### 统一转发器增强 (`UnifiedCallbackForwarder`)
- **功能**: 在转发失败时自动调度重试
- **集成**: 与重试调度器无缝集成
- **兼容**: 保持现有功能不变

### 3. API接口设计

#### 用户接口
```
GET  /api/v1/callback/retry/records      # 获取重试记录列表
GET  /api/v1/callback/retry/records/:id  # 获取重试记录详情
POST /api/v1/callback/retry/manual/:id   # 手动重试
POST /api/v1/callback/retry/cancel/:id   # 取消重试
GET  /api/v1/callback/retry/status       # 获取重试状态
```

#### 管理员接口
```
GET  /api/v1/admin/callback/retry/statistics    # 重试统计
GET  /api/v1/admin/callback/retry/config        # 获取配置
PUT  /api/v1/admin/callback/retry/config        # 更新配置
POST /api/v1/admin/callback/retry/batch/cancel  # 批量取消
POST /api/v1/admin/callback/retry/batch/retry   # 批量重试
POST /api/v1/admin/callback/retry/service/start # 启动服务
POST /api/v1/admin/callback/retry/service/stop  # 停止服务
```

#### 监控接口
```
GET  /health/retry        # 健康检查
GET  /metrics/retry       # Prometheus指标
```

## 📁 已完成的文件结构

### 核心服务层
- ✅ `internal/service/callback/retry_scheduler.go` - 重试调度器
- ✅ `internal/service/callback/retry_service_manager.go` - 服务管理器
- ✅ `internal/service/callback/unified_callback_forwarder.go` - 转发器增强

### 数据层
- ✅ `internal/model/callback.go` - 数据模型扩展
- ✅ `internal/repository/callback_repository.go` - 仓储层扩展

### API层
- ✅ `internal/handler/callback_retry_handler.go` - API处理器
- ✅ `api/routes/callback_retry_routes.go` - 路由配置

### 数据库
- ✅ `migrations/20250129_add_callback_retry_system.sql` - 数据库迁移

## 🚀 实施步骤

### 第一阶段：基础设施 ✅
1. ✅ 数据库表结构设计和创建
2. ✅ 数据模型定义和扩展
3. ✅ 仓储层方法实现
4. ✅ 核心服务组件开发

### 第二阶段：重试机制 ✅
1. ✅ 重试调度器实现
2. ✅ 重试策略算法实现
3. ✅ 配置管理和热重载
4. ✅ 与现有转发器集成

### 第三阶段：API和管理 ✅
1. ✅ API处理器实现
2. ✅ 路由配置和权限控制
3. ✅ 监控和健康检查接口
4. ✅ 服务生命周期管理

### 第四阶段：部署和测试 🔄
1. 🔄 数据库迁移执行
2. 🔄 服务集成和启动
3. 🔄 功能测试和验证
4. 🔄 性能测试和优化

## 🧪 测试方案

### 单元测试
- 重试调度器逻辑测试
- 重试策略算法测试
- 配置管理功能测试
- API接口功能测试

### 集成测试
- 端到端重试流程测试
- 数据库操作测试
- 服务启停测试
- 权限控制测试

### 性能测试
- 高并发重试场景测试
- 大量重试任务处理测试
- 内存和CPU使用率测试
- 数据库性能影响测试

### 故障测试
- 服务重启后重试恢复测试
- 数据库连接异常测试
- 配置错误处理测试
- 网络异常重试测试

## 📊 验证方法

### 功能验证
1. **重试触发**: 模拟回调失败，验证自动重试
2. **配置生效**: 修改配置，验证热重载
3. **手动操作**: 测试手动重试和取消功能
4. **权限控制**: 验证不同角色的访问权限

### 性能验证
1. **响应时间**: API响应时间 < 200ms
2. **并发处理**: 支持1000+并发重试任务
3. **资源使用**: 内存和CPU使用合理
4. **数据库影响**: 对现有业务影响最小

### 稳定性验证
1. **长期运行**: 7x24小时稳定运行
2. **故障恢复**: 服务重启后自动恢复
3. **数据一致性**: 重试状态数据一致
4. **监控告警**: 异常情况及时告警

## 🔧 部署指南

### 1. 数据库迁移
```bash
# 执行迁移脚本
psql -h 8.138.252.193 -U postgres -d go_kuaidi -f migrations/20250129_add_callback_retry_system.sql
```

### 2. 服务集成
```go
// 在main.go中初始化重试服务
retryServiceManager := callback.NewRetryServiceManager(
    callbackRepository,
    systemConfigService,
    logger,
)

// 初始化并启动
retryServiceManager.Initialize()
retryServiceManager.Start()

// 配置路由
routes.SetupCallbackRetryRoutes(
    router,
    callbackRepository,
    retryServiceManager,
    tokenService,
    logger,
)
```

### 3. 配置验证
```bash
# 检查配置是否正确加载
curl -H "Authorization: Bearer <token>" \
     http://localhost:8080/api/v1/callback/retry/status
```

### 4. 监控配置
```bash
# 添加Prometheus监控
curl http://localhost:8080/metrics/retry
```

## 📈 预期效果

### 可靠性提升
- 回调成功率从85%提升到95%+
- 减少人工干预需求90%
- 提高用户满意度

### 运维效率
- 自动化重试减少运维工作量
- 实时监控提供问题预警
- 详细日志便于问题排查

### 系统性能
- 对现有系统性能影响 < 5%
- 重试任务处理延迟 < 1分钟
- 支持高并发场景

## 🎉 项目总结

通过实施智能重试机制，统一回调外部转发器将具备：

1. **企业级可靠性**: 自动重试机制确保回调成功率
2. **灵活配置管理**: 数据库驱动的动态配置
3. **完善监控体系**: 实时监控和性能指标
4. **友好管理界面**: 便于运营人员管理和操作
5. **高性能架构**: 支持高并发和大规模部署

该系统将显著提升回调成功率，减少运维工作量，为用户提供更可靠的服务体验。
