#!/bin/bash

# 查询快递鸟203状态回调脚本

echo "🔍 查询callback_receiver数据库中的快递鸟203状态回调"
echo "=================================================="

# 数据库连接信息
DB_HOST="*************"
DB_PORT="5432"
DB_USER="postgres"
DB_NAME="callback_receiver"
DB_PASSWORD="gjx6ngf4"

echo "📊 1. 统计快递鸟回调总数..."
TOTAL_CALLBACKS=$(PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -t -c "SELECT COUNT(*) FROM callback_raw_data WHERE provider = 'kuaidiniao';" | tr -d ' ')
echo "   快递鸟回调总数: $TOTAL_CALLBACKS"

echo ""
echo "📋 2. 查询包含状态码203的回调..."
STATE_203_COUNT=$(PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -t -c "SELECT COUNT(*) FROM callback_raw_data WHERE provider = 'kuaidiniao' AND raw_body LIKE '%\"State\":\"203\"%';" | tr -d ' ')
echo "   包含State:203的回调数量: $STATE_203_COUNT"

echo ""
echo "📋 3. 查询包含任何203的回调..."
ANY_203_COUNT=$(PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -t -c "SELECT COUNT(*) FROM callback_raw_data WHERE provider = 'kuaidiniao' AND raw_body LIKE '%203%';" | tr -d ' ')
echo "   包含任何203的回调数量: $ANY_203_COUNT"

echo ""
echo "📋 4. 查询包含'已取消'的回调..."
CANCELLED_COUNT=$(PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -t -c "SELECT COUNT(*) FROM callback_raw_data WHERE provider = 'kuaidiniao' AND raw_body LIKE '%已取消%';" | tr -d ' ')
echo "   包含'已取消'的回调数量: $CANCELLED_COUNT"

echo ""
echo "📋 5. 查看最近的快递鸟回调样本..."
echo ""
PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "
SELECT 
    id, 
    received_at, 
    LEFT(raw_body, 150) as sample_body 
FROM callback_raw_data 
WHERE provider = 'kuaidiniao' 
ORDER BY received_at DESC 
LIMIT 3;
"

echo ""
echo "📋 6. 查询不同状态码的分布..."
echo ""
PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "
SELECT 
    CASE 
        WHEN raw_body LIKE '%\"State\":\"0\"%' THEN 'State:0 (暂无物流信息)'
        WHEN raw_body LIKE '%\"State\":\"1\"%' THEN 'State:1 (已揽收)'
        WHEN raw_body LIKE '%\"State\":\"2\"%' THEN 'State:2 (在途中)'
        WHEN raw_body LIKE '%\"State\":\"3\"%' THEN 'State:3 (已签收)'
        WHEN raw_body LIKE '%\"State\":\"99\"%' THEN 'State:99 (调度失败)'
        WHEN raw_body LIKE '%\"State\":\"203\"%' THEN 'State:203 (已取消)'
        WHEN raw_body LIKE '%\"State\":\"204\"%' THEN 'State:204 (揽收失败)'
        ELSE '其他状态'
    END as status_type,
    COUNT(*) as count
FROM callback_raw_data 
WHERE provider = 'kuaidiniao'
GROUP BY 
    CASE 
        WHEN raw_body LIKE '%\"State\":\"0\"%' THEN 'State:0 (暂无物流信息)'
        WHEN raw_body LIKE '%\"State\":\"1\"%' THEN 'State:1 (已揽收)'
        WHEN raw_body LIKE '%\"State\":\"2\"%' THEN 'State:2 (在途中)'
        WHEN raw_body LIKE '%\"State\":\"3\"%' THEN 'State:3 (已签收)'
        WHEN raw_body LIKE '%\"State\":\"99\"%' THEN 'State:99 (调度失败)'
        WHEN raw_body LIKE '%\"State\":\"203\"%' THEN 'State:203 (已取消)'
        WHEN raw_body LIKE '%\"State\":\"204\"%' THEN 'State:204 (揽收失败)'
        ELSE '其他状态'
    END
ORDER BY count DESC;
"

if [ "$STATE_203_COUNT" -gt 0 ]; then
    echo ""
    echo "📋 7. 查看State:203回调的详细信息..."
    echo ""
    PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "
    SELECT 
        id, 
        received_at, 
        raw_body 
    FROM callback_raw_data 
    WHERE provider = 'kuaidiniao' 
      AND raw_body LIKE '%\"State\":\"203\"%'
    ORDER BY received_at DESC 
    LIMIT 5;
    "
fi

echo ""
echo "🏁 查询完成！"
echo ""
echo "📊 总结:"
echo "   - 快递鸟回调总数: $TOTAL_CALLBACKS"
echo "   - State:203回调数量: $STATE_203_COUNT"
echo "   - 包含任何203的回调: $ANY_203_COUNT"
echo "   - 包含'已取消'的回调: $CANCELLED_COUNT"

if [ "$STATE_203_COUNT" -eq 0 ]; then
    echo ""
    echo "❌ 重要发现: 没有找到快递鸟State:203(已取消)的回调！"
    echo "   这证实了快递鸟不推送已取消状态回调的问题。"
    echo "   这就是为什么有73个订单卡在'cancelling'状态的根本原因。"
fi
