#!/bin/bash

# 简化的回调一致性修复系统部署脚本

set -e

echo "🚀 部署回调一致性修复系统"
echo "================================"

# 检查当前目录
if [ ! -f "go.mod" ]; then
    echo "❌ 请在项目根目录执行此脚本"
    exit 1
fi

echo "📊 1. 编译项目..."
go mod tidy
go build -o bin/go-kuaidi cmd/main.go

if [ $? -eq 0 ]; then
    echo "   ✅ 编译成功"
else
    echo "   ❌ 编译失败"
    exit 1
fi

echo ""
echo "📋 2. 检查数据库连接..."
PGPASSWORD=gjx6ngf4 psql -h 8.138.252.193 -p 5432 -U postgres -d go_kuaidi -c "SELECT 1;" > /dev/null 2>&1

if [ $? -eq 0 ]; then
    echo "   ✅ 数据库连接正常"
else
    echo "   ❌ 数据库连接失败"
    exit 1
fi

echo ""
echo "🔧 3. 创建必要的数据库表结构..."

# 创建回调相关表
PGPASSWORD=gjx6ngf4 psql -h 8.138.252.193 -p 5432 -U postgres -d go_kuaidi << 'EOF'
-- 创建callback_raw_data表（如果不存在）
CREATE TABLE IF NOT EXISTS callback_raw_data (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    provider VARCHAR(50) NOT NULL,
    raw_body TEXT NOT NULL,
    headers JSONB,
    processed BOOLEAN DEFAULT FALSE,
    received_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    processed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX IF NOT EXISTS idx_callback_raw_provider ON callback_raw_data(provider);
CREATE INDEX IF NOT EXISTS idx_callback_raw_processed ON callback_raw_data(processed);
CREATE INDEX IF NOT EXISTS idx_callback_raw_received_at ON callback_raw_data(received_at);

-- 创建unified_callback_records表
CREATE TABLE IF NOT EXISTS unified_callback_records (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    provider VARCHAR(50) NOT NULL,
    tracking_no VARCHAR(100),
    raw_data TEXT NOT NULL,
    standardized_data JSONB,
    internal_status VARCHAR(20) DEFAULT 'pending',
    internal_error TEXT,
    internal_processed_at TIMESTAMP WITH TIME ZONE,
    external_status VARCHAR(20) DEFAULT 'pending',
    external_error TEXT,
    external_processed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX IF NOT EXISTS idx_unified_callback_provider ON unified_callback_records(provider);
CREATE INDEX IF NOT EXISTS idx_unified_callback_tracking_no ON unified_callback_records(tracking_no);
CREATE INDEX IF NOT EXISTS idx_unified_callback_created_at ON unified_callback_records(created_at);
CREATE INDEX IF NOT EXISTS idx_unified_callback_internal_status ON unified_callback_records(internal_status);
EOF

echo "   ✅ 数据库表结构创建完成"

echo ""
echo "📤 4. 停止现有服务..."

# 查找现有进程
EXISTING_PID=$(pgrep -f "go-kuaidi" || echo "")

if [ -n "$EXISTING_PID" ]; then
    echo "   🛑 停止现有进程: $EXISTING_PID"
    kill -TERM $EXISTING_PID
    sleep 3
    
    # 检查是否还在运行
    if pgrep -f "go-kuaidi" > /dev/null; then
        echo "   🔥 强制停止进程"
        pkill -KILL -f "go-kuaidi"
        sleep 2
    fi
    
    echo "   ✅ 现有服务已停止"
else
    echo "   ℹ️  没有发现现有服务"
fi

echo ""
echo "🚀 5. 启动新服务..."

# 创建日志目录
mkdir -p logs

# 启动服务
nohup ./bin/go-kuaidi > logs/app.log 2>&1 &
NEW_PID=$!

echo "   🚀 新服务已启动，PID: $NEW_PID"

# 等待服务启动
echo "   ⏳ 等待服务启动..."
sleep 5

# 检查服务是否正常运行
if ps -p $NEW_PID > /dev/null; then
    echo "   ✅ 服务启动成功"
else
    echo "   ❌ 服务启动失败"
    echo "   📋 查看日志:"
    tail -20 logs/app.log
    exit 1
fi

# 测试健康检查
echo "   🔍 测试健康检查..."
sleep 2

curl -s http://localhost:8081/health > /dev/null 2>&1

if [ $? -eq 0 ]; then
    echo "   ✅ 健康检查通过"
else
    echo "   ⚠️  健康检查失败，但服务可能仍在启动中"
fi

echo ""
echo "📊 6. 部署完成总结"
echo "================================"
echo "✅ 编译成功"
echo "✅ 数据库连接正常"
echo "✅ 数据库表结构就绪"
echo "✅ 服务启动成功 (PID: $NEW_PID)"

echo ""
echo "🔧 新增功能:"
echo "   - 同步回调处理，确保数据一致性"
echo "   - 回调一致性检查和自动修复"
echo "   - 定时任务自动检测问题回调"
echo "   - 管理员API接口管理回调状态"

echo ""
echo "📋 管理员API接口:"
echo "   GET  /api/v1/admin/callbacks/consistency/check?provider=kuaidi100&hours=24"
echo "   POST /api/v1/admin/callbacks/consistency/fix"
echo "   GET  /api/v1/admin/callbacks/statistics?provider=kuaidi100&hours=24"
echo "   POST /api/v1/admin/callbacks/consistency/batch-fix"

echo ""
echo "🏁 部署完成！服务正在运行中..."
echo "   PID: $NEW_PID"
echo "   日志: tail -f logs/app.log"
echo "   健康检查: curl http://localhost:8081/health"
