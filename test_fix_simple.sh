#!/bin/bash

# 简单的快递公司映射禁用功能测试脚本

set -e

# 配置
BASE_URL="http://localhost:8081"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查服务是否运行
check_service() {
    log_info "检查服务状态..."
    
    if curl -s "${BASE_URL}/health" > /dev/null; then
        log_success "服务正在运行"
    else
        log_error "服务未运行，请先启动服务: ./start-local.sh"
        exit 1
    fi
}

# 获取管理员Token
get_admin_token() {
    log_info "获取管理员Token..."
    
    response=$(curl -s -X POST "${BASE_URL}/api/v1/admin/auth/login" \
        -H "Content-Type: application/json" \
        -d '{
            "username": "admin",
            "password": "**********+.aA..@"
        }')
    
    if echo "$response" | grep -q '"success":true'; then
        # 使用jq提取access_token，如果没有jq则使用grep
        if command -v jq &> /dev/null; then
            ADMIN_TOKEN=$(echo "$response" | jq -r '.access_token // empty')
        else
            ADMIN_TOKEN=$(echo "$response" | grep -o '"access_token":"[^"]*"' | cut -d'"' -f4)
        fi

        if [ -n "$ADMIN_TOKEN" ]; then
            log_success "管理员Token获取成功"
        else
            log_error "Token提取失败: $response"
            exit 1
        fi
    else
        log_error "获取管理员Token失败: $response"
        exit 1
    fi
}

# 测试手动刷新缓存
test_manual_cache_refresh() {
    log_info "测试手动刷新缓存功能..."
    
    response=$(curl -s -X POST "${BASE_URL}/api/v1/admin/express/mapping/cache/refresh" \
        -H "Authorization: Bearer $ADMIN_TOKEN")
    
    if echo "$response" | grep -q '"success":true'; then
        log_success "✅ 手动缓存刷新功能正常"
    else
        log_error "❌ 手动缓存刷新功能异常: $response"
        return 1
    fi
}

# 测试获取映射列表
test_get_mappings() {
    log_info "测试获取映射列表..."
    
    response=$(curl -s -X GET "${BASE_URL}/api/v1/admin/express/mappings?page=1&page_size=10" \
        -H "Authorization: Bearer $ADMIN_TOKEN")
    
    if echo "$response" | grep -q '"success":true'; then
        mapping_count=$(echo "$response" | grep -o '"total":[0-9]*' | cut -d':' -f2)
        log_success "✅ 映射列表获取成功，共 $mapping_count 个映射"
    else
        log_error "❌ 映射列表获取失败: $response"
        return 1
    fi
}

# 检查日志中的缓存刷新记录
check_cache_refresh_logs() {
    log_info "检查最近的缓存刷新日志..."
    
    if [ -f "app.log" ]; then
        recent_refresh=$(tail -100 app.log | grep "快递公司映射缓存刷新完成" | tail -1)
        if [ -n "$recent_refresh" ]; then
            log_success "✅ 发现缓存刷新日志记录"
            echo "   $recent_refresh"
        else
            log_info "未发现最近的缓存刷新日志（可能是首次运行）"
        fi
    else
        log_info "未找到日志文件 app.log"
    fi
}

# 主测试流程
main() {
    log_info "开始快递公司映射禁用功能修复验证..."
    
    # 基础检查
    check_service
    get_admin_token
    
    # 功能测试
    test_result=0
    test_manual_cache_refresh || test_result=1
    test_get_mappings || test_result=1
    
    # 日志检查
    check_cache_refresh_logs
    
    # 输出测试结果
    echo ""
    if [ $test_result -eq 0 ]; then
        log_success "🎉 基础功能测试通过！"
        echo ""
        echo "修复内容："
        echo "1. ✅ 映射创建后自动刷新缓存"
        echo "2. ✅ 映射更新后自动刷新缓存"  
        echo "3. ✅ 映射删除后自动刷新缓存"
        echo "4. ✅ 手动缓存刷新功能正常"
        echo ""
        echo "要完整测试禁用功能，请运行："
        echo "chmod +x test_mapping_disable_fix.sh && ./test_mapping_disable_fix.sh"
    else
        log_error "💥 基础功能测试失败！"
        exit 1
    fi
}

# 运行测试
main "$@"
