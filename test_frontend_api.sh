#!/bin/bash

echo "🎯 测试前端API调用"
echo "=================="

echo ""
echo "📊 测试前端是否能正确调用缓存详情API:"

# 模拟前端API调用
curl -s "http://localhost:8081/api/v1/weight-cache/details?provider=kuaidi100&express_code=STO&page=1&page_size=10" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" | python3 -c "
import json
import sys
try:
    data = json.load(sys.stdin)
    if data.get('success'):
        routes = data.get('data', {}).get('records', [])
        print(f'✅ API调用成功，返回 {len(routes)} 条记录')
        
        # 检查是否有失败原因数据
        has_failure_data = False
        for route in routes:
            if route.get('failed_queries', 0) > 0:
                has_failure_data = True
                print(f'📍 路线: {route.get(\"route\", \"N/A\")}')
                print(f'❌ 失败次数: {route.get(\"failed_queries\", 0)}')
                print(f'📊 总查询次数: {route.get(\"total_queries\", 0)}')
                
                failure_reasons = route.get('failure_reasons', [])
                if failure_reasons:
                    print('🚨 失败原因:')
                    for i, reason in enumerate(failure_reasons, 1):
                        print(f'   {i}. {reason.get(\"error_message\", \"N/A\")[:80]}...')
                        print(f'      出现次数: {reason.get(\"count\", 0)}')
                    print()
                break
        
        if not has_failure_data:
            print('ℹ️  当前返回的记录中没有失败数据')
        else:
            print('✅ 失败原因数据结构正确！')
            
    else:
        print(f'❌ API调用失败: {data.get(\"message\", \"未知错误\")}')
except Exception as e:
    print(f'❌ 解析JSON失败: {e}')
"

echo ""
echo "🎯 前端测试指南:"
echo "   1. 打开浏览器访问: http://localhost:3008/weight-cache"
echo "   2. 登录系统（如果需要）"
echo "   3. 找到快递100供应商的申通快递(STO)，点击'缓存明细'"
echo "   4. 查看表格中是否显示了'失败统计'和'失败原因'列"
echo "   5. 寻找有失败记录的路线，查看失败原因是否正确显示"
echo ""
echo "✅ 测试完成！"
