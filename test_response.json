[0;36mGo-Kuaidi 统一网关查价接口完整测试脚本[0m
[0;36m测试两个核心接口：标准查价 + 京东专用查价[0m

[0;36m=================================================[0m
[0;34m[INFO][0m 🚀 开始Go-Kuaidi统一网关查价接口完整测试
[0;36m=================================================[0m
[0;34m[INFO][0m 正在获取JWT Token...
[0;32m[SUCCESS][0m Token获取成功
[0;36m=================================================[0m
[0;34m[INFO][0m 📋 第一部分：标准查价接口测试 (QUERY_PRICE)
[0;34m[INFO][0m 特点：支持所有快递公司（除JD/DBL），使用缓存机制
[0;36m=================================================[0m
[0;35m[TEST][0m 🔵 标准查价接口测试: 简单查价测试
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "success": true,
    "code": 200,
    "message": "成功",
    "data": [
      {
        "express_code": "JT",
        "express_name": "极兔快递",
        "product_code": "标准快递",
        "product_name": "标准快递",
        "price": 4.59,
        "continued_weight_per_kg": 1.5,
        "calc_weight": 1,
        "order_code": "ENHANCED_ORDER_CODE_eyJzdGFuZGFyZF9jb2RlIjoiSlQiLCJvcmlnaW5hbF9jb2RlIjoiSlQiLCJwcm92aWRlciI6Imt1YWlkaTEwMCIsImNoYW5uZWxfaWQiOiJrdWFpZGkxMDBfanRleHByZXNzIiwicHJvZHVjdF9jb2RlIjoi5qCH5YeG5b+r6YCSIiwib3JpZ2luYWxfcmVxdWVzdCI6eyJmcm9tX3Byb3ZpbmNlIjoi5YyX5Lqs5biCIiwiZnJvbV9jaXR5Ijoi5YyX5Lqs5biCIiwiZnJvbV9kaXN0cmljdCI6IiIsInRvX3Byb3ZpbmNlIjoi5LiK5rW35biCIiwidG9fY2l0eSI6IuS4iua1t+W4giIsInRvX2Rpc3RyaWN0IjoiIiwiZXhwcmVzc19jb2RlIjoiIiwic2VuZGVyIjp7InByb3ZpbmNlIjoiIiwiY2l0eSI6IiIsImRpc3RyaWN0IjoiIn0sInJlY2VpdmVyIjp7InByb3ZpbmNlIjoiIiwiY2l0eSI6IiIsImRpc3RyaWN0IjoiIn0sIndlaWdodCI6MSwibGVuZ3RoIjowLCJ3aWR0aCI6MCwiaGVpZ2h0IjowLCJ2b2x1bWUiOjAsInF1YW50aXR5IjowLCJnb29kc19uYW1lIjoi5rWL6K+V54mp5ZOBIiwicGF5X21ldGhvZCI6MH19",
        "expires_at": "0001-01-01T00:00:00Z"
      },
      {
        "express_code": "STO",
        "express_name": "申通快递",
        "product_code": "标准快递",
        "product_name": "标准快递",
        "price": 5,
        "continued_weight_per_kg": 2.3,
        "calc_weight": 1,
        "order_code": "ENHANCED_ORDER_CODE_eyJzdGFuZGFyZF9jb2RlIjoiU1RPIiwib3JpZ2luYWxfY29kZSI6IlNUTyIsInByb3ZpZGVyIjoia3VhaWRpMTAwIiwiY2hhbm5lbF9pZCI6Imt1YWlkaTEwMF9zaGVudG9uZyIsInByb2R1Y3RfY29kZSI6Iuagh+WHhuW/q+mAkiIsIm9yaWdpbmFsX3JlcXVlc3QiOnsiZnJvbV9wcm92aW5jZSI6IuWMl+S6rOW4giIsImZyb21fY2l0eSI6IuWMl+S6rOW4giIsImZyb21fZGlzdHJpY3QiOiIiLCJ0b19wcm92aW5jZSI6IuS4iua1t+W4giIsInRvX2NpdHkiOiLkuIrmtbfluIIiLCJ0b19kaXN0cmljdCI6IiIsImV4cHJlc3NfY29kZSI6IiIsInNlbmRlciI6eyJwcm92aW5jZSI6IiIsImNpdHkiOiIiLCJkaXN0cmljdCI6IiJ9LCJyZWNlaXZlciI6eyJwcm92aW5jZSI6IiIsImNpdHkiOiIiLCJkaXN0cmljdCI6IiJ9LCJ3ZWlnaHQiOjEsImxlbmd0aCI6MCwid2lkdGgiOjAsImhlaWdodCI6MCwidm9sdW1lIjowLCJxdWFudGl0eSI6MCwiZ29vZHNfbmFtZSI6Iua1i+ivleeJqeWTgSIsInBheV9tZXRob2QiOjB9fQ==",
        "expires_at": "0001-01-01T00:00:00Z"
      },
      {
        "express_code": "STO",
        "express_name": "申通快递",
        "product_code": "STBK",
        "product_name": "普快",
        "price": 5.14,
        "continued_weight_per_kg": 2,
        "calc_weight": 1,
        "order_code": "ENHANCED_ORDER_CODE_eyJzdGFuZGFyZF9jb2RlIjoiU1RPIiwib3JpZ2luYWxfY29kZSI6IlNUTyIsInByb3ZpZGVyIjoieXVudG9uZyIsImNoYW5uZWxfaWQiOiLnlLPpgJrmma7mlaMtSEbluLjop4Tot6jnnIHlsI/ku7YoNy43KSIsInByb2R1Y3RfY29kZSI6IlNUQksiLCJvcmlnaW5hbF9yZXF1ZXN0Ijp7ImZyb21fcHJvdmluY2UiOiLljJfkuqzluIIiLCJmcm9tX2NpdHkiOiLljJfkuqzluIIiLCJmcm9tX2Rpc3RyaWN0IjoiIiwidG9fcHJvdmluY2UiOiLkuIrmtbfluIIiLCJ0b19jaXR5Ijoi5LiK5rW35biCIiwidG9fZGlzdHJpY3QiOiIiLCJleHByZXNzX2NvZGUiOiIiLCJzZW5kZXIiOnsicHJvdmluY2UiOiIiLCJjaXR5IjoiIiwiZGlzdHJpY3QiOiIifSwicmVjZWl2ZXIiOnsicHJvdmluY2UiOiIiLCJjaXR5IjoiIiwiZGlzdHJpY3QiOiIifSwid2VpZ2h0IjoxLCJsZW5ndGgiOjAsIndpZHRoIjowLCJoZWlnaHQiOjAsInZvbHVtZSI6MCwicXVhbnRpdHkiOjAsImdvb2RzX25hbWUiOiLmtYvor5Xnianlk4EiLCJwYXlfbWV0aG9kIjowfX0=",
        "expires_at": "0001-01-01T00:00:00Z"
      },
      {
        "express_code": "YTO",
        "express_name": "圆通速递",
        "product_code": "YTBK",
        "product_name": "普快",
        "price": 5.15,
        "continued_weight_per_kg": 2.35,
        "calc_weight": 1,
        "order_code": "ENHANCED_ORDER_CODE_eyJzdGFuZGFyZF9jb2RlIjoiWVRPIiwib3JpZ2luYWxfY29kZSI6IllUTyIsInByb3ZpZGVyIjoieXVudG9uZyIsImNoYW5uZWxfaWQiOiLlnIbpgJrmma7mlaMtSEbluLjop4Tot6jnnIHlsI/ku7YoMDcxNykiLCJwcm9kdWN0X2NvZGUiOiJZVEJLIiwib3JpZ2luYWxfcmVxdWVzdCI6eyJmcm9tX3Byb3ZpbmNlIjoi5YyX5Lqs5biCIiwiZnJvbV9jaXR5Ijoi5YyX5Lqs5biCIiwiZnJvbV9kaXN0cmljdCI6IiIsInRvX3Byb3ZpbmNlIjoi5LiK5rW35biCIiwidG9fY2l0eSI6IuS4iua1t+W4giIsInRvX2Rpc3RyaWN0IjoiIiwiZXhwcmVzc19jb2RlIjoiIiwic2VuZGVyIjp7InByb3ZpbmNlIjoiIiwiY2l0eSI6IiIsImRpc3RyaWN0IjoiIn0sInJlY2VpdmVyIjp7InByb3ZpbmNlIjoiIiwiY2l0eSI6IiIsImRpc3RyaWN0IjoiIn0sIndlaWdodCI6MSwibGVuZ3RoIjowLCJ3aWR0aCI6MCwiaGVpZ2h0IjowLCJ2b2x1bWUiOjAsInF1YW50aXR5IjowLCJnb29kc19uYW1lIjoi5rWL6K+V54mp5ZOBIiwicGF5X21ldGhvZCI6MH19",
        "expires_at": "0001-01-01T00:00:00Z"
      },
      {
        "express_code": "YTO",
        "express_name": "圆通速递",
        "product_code": "YTO_BK",
        "product_name": "圆通速递",
        "price": 5.18,
        "continued_weight_per_kg": 2.4,
        "calc_weight": 1,
        "order_code": "ENHANCED_ORDER_CODE_eyJzdGFuZGFyZF9jb2RlIjoiWVRPIiwib3JpZ2luYWxfY29kZSI6IllUTyIsInByb3ZpZGVyIjoieWlkYSIsImNoYW5uZWxfaWQiOiJ5aWRhXzEwMjY4IiwicHJvZHVjdF9jb2RlIjoiWVRPX0JLIiwib3JpZ2luYWxfcmVxdWVzdCI6eyJmcm9tX3Byb3ZpbmNlIjoi5YyX5Lqs5biCIiwiZnJvbV9jaXR5Ijoi5YyX5Lqs5biCIiwiZnJvbV9kaXN0cmljdCI6IiIsInRvX3Byb3ZpbmNlIjoi5LiK5rW35biCIiwidG9fY2l0eSI6IuS4iua1t+W4giIsInRvX2Rpc3RyaWN0IjoiIiwiZXhwcmVzc19jb2RlIjoiIiwic2VuZGVyIjp7InByb3ZpbmNlIjoiIiwiY2l0eSI6IiIsImRpc3RyaWN0IjoiIn0sInJlY2VpdmVyIjp7InByb3ZpbmNlIjoiIiwiY2l0eSI6IiIsImRpc3RyaWN0IjoiIn0sIndlaWdodCI6MSwibGVuZ3RoIjowLCJ3aWR0aCI6MCwiaGVpZ2h0IjowLCJ2b2x1bWUiOjAsInF1YW50aXR5IjowLCJnb29kc19uYW1lIjoi5rWL6K+V54mp5ZOBIiwicGF5X21ldGhvZCI6MH19",
        "expires_at": "0001-01-01T00:00:00Z"
      },
      {
        "express_code": "YD",
        "express_name": "韵达速递",
        "product_code": "YUND_BK",
        "product_name": "韵达速递",
        "price": 5.3,
        "continued_weight_per_kg": 2.2,
        "calc_weight": 1,
        "order_code": "ENHANCED_ORDER_CODE_eyJzdGFuZGFyZF9jb2RlIjoiWUQiLCJvcmlnaW5hbF9jb2RlIjoiWUQiLCJwcm92aWRlciI6InlpZGEiLCJjaGFubmVsX2lkIjoieWlkYV8xMDI3MiIsInByb2R1Y3RfY29kZSI6IllVTkRfQksiLCJvcmlnaW5hbF9yZXF1ZXN0Ijp7ImZyb21fcHJvdmluY2UiOiLljJfkuqzluIIiLCJmcm9tX2NpdHkiOiLljJfkuqzluIIiLCJmcm9tX2Rpc3RyaWN0IjoiIiwidG9fcHJvdmluY2UiOiLkuIrmtbfluIIiLCJ0b19jaXR5Ijoi5LiK5rW35biCIiwidG9fZGlzdHJpY3QiOiIiLCJleHByZXNzX2NvZGUiOiIiLCJzZW5kZXIiOnsicHJvdmluY2UiOiIiLCJjaXR5IjoiIiwiZGlzdHJpY3QiOiIifSwicmVjZWl2ZXIiOnsicHJvdmluY2UiOiIiLCJjaXR5IjoiIiwiZGlzdHJpY3QiOiIifSwid2VpZ2h0IjoxLCJsZW5ndGgiOjAsIndpZHRoIjowLCJoZWlnaHQiOjAsInZvbHVtZSI6MCwicXVhbnRpdHkiOjAsImdvb2RzX25hbWUiOiLmtYvor5Xnianlk4EiLCJwYXlfbWV0aG9kIjowfX0=",
        "expires_at": "0001-01-01T00:00:00Z"
      },
      {
        "express_code": "STO",
        "express_name": "申通快递",
        "product_code": "STO_INT_BK",
        "product_name": "申通快递25",
        "price": 5.48,
        "continued_weight_per_kg": 2.2,
        "calc_weight": 1,
        "order_code": "ENHANCED_ORDER_CODE_eyJzdGFuZGFyZF9jb2RlIjoiU1RPIiwib3JpZ2luYWxfY29kZSI6IlNUTyIsInByb3ZpZGVyIjoieWlkYSIsImNoYW5uZWxfaWQiOiJ5aWRhXzEwMzIwIiwicHJvZHVjdF9jb2RlIjoiU1RPX0lOVF9CSyIsIm9yaWdpbmFsX3JlcXVlc3QiOnsiZnJvbV9wcm92aW5jZSI6IuWMl+S6rOW4giIsImZyb21fY2l0eSI6IuWMl+S6rOW4giIsImZyb21fZGlzdHJpY3QiOiIiLCJ0b19wcm92aW5jZSI6IuS4iua1t+W4giIsInRvX2NpdHkiOiLkuIrmtbfluIIiLCJ0b19kaXN0cmljdCI6IiIsImV4cHJlc3NfY29kZSI6IiIsInNlbmRlciI6eyJwcm92aW5jZSI6IiIsImNpdHkiOiIiLCJkaXN0cmljdCI6IiJ9LCJyZWNlaXZlciI6eyJwcm92aW5jZSI6IiIsImNpdHkiOiIiLCJkaXN0cmljdCI6IiJ9LCJ3ZWlnaHQiOjEsImxlbmd0aCI6MCwid2lkdGgiOjAsImhlaWdodCI6MCwidm9sdW1lIjowLCJxdWFudGl0eSI6MCwiZ29vZHNfbmFtZSI6Iua1i+ivleeJqeWTgSIsInBheV9tZXRob2QiOjB9fQ==",
        "expires_at": "0001-01-01T00:00:00Z"
      },
      {
        "express_code": "YD",
        "express_name": "韵达速递",
        "product_code": "标准快递",
        "product_name": "标准快递",
        "price": 5.5,
        "continued_weight_per_kg": 2.2,
        "calc_weight": 1,
        "order_code": "ENHANCED_ORDER_CODE_eyJzdGFuZGFyZF9jb2RlIjoiWUQiLCJvcmlnaW5hbF9jb2RlIjoiWUQiLCJwcm92aWRlciI6Imt1YWlkaTEwMCIsImNoYW5uZWxfaWQiOiJrdWFpZGkxMDBfeXVuZGEiLCJwcm9kdWN0X2NvZGUiOiLmoIflh4blv6vpgJIiLCJvcmlnaW5hbF9yZXF1ZXN0Ijp7ImZyb21fcHJvdmluY2UiOiLljJfkuqzluIIiLCJmcm9tX2NpdHkiOiLljJfkuqzluIIiLCJmcm9tX2Rpc3RyaWN0IjoiIiwidG9fcHJvdmluY2UiOiLkuIrmtbfluIIiLCJ0b19jaXR5Ijoi5LiK5rW35biCIiwidG9fZGlzdHJpY3QiOiIiLCJleHByZXNzX2NvZGUiOiIiLCJzZW5kZXIiOnsicHJvdmluY2UiOiIiLCJjaXR5IjoiIiwiZGlzdHJpY3QiOiIifSwicmVjZWl2ZXIiOnsicHJvdmluY2UiOiIiLCJjaXR5IjoiIiwiZGlzdHJpY3QiOiIifSwid2VpZ2h0IjoxLCJsZW5ndGgiOjAsIndpZHRoIjowLCJoZWlnaHQiOjAsInZvbHVtZSI6MCwicXVhbnRpdHkiOjAsImdvb2RzX25hbWUiOiLmtYvor5Xnianlk4EiLCJwYXlfbWV0aG9kIjowfX0=",
        "expires_at": "0001-01-01T00:00:00Z"
      },
      {
        "express_code": "YTO",
        "express_name": "圆通速递",
        "product_code": "标准快递",
        "product_name": "标准快递",
        "price": 5.5,
        "continued_weight_per_kg": 2.4,
        "calc_weight": 1,
        "order_code": "ENHANCED_ORDER_CODE_eyJzdGFuZGFyZF9jb2RlIjoiWVRPIiwib3JpZ2luYWxfY29kZSI6IllUTyIsInByb3ZpZGVyIjoia3VhaWRpMTAwIiwiY2hhbm5lbF9pZCI6Imt1YWlkaTEwMF95dWFudG9uZyIsInByb2R1Y3RfY29kZSI6Iuagh+WHhuW/q+mAkiIsIm9yaWdpbmFsX3JlcXVlc3QiOnsiZnJvbV9wcm92aW5jZSI6IuWMl+S6rOW4giIsImZyb21fY2l0eSI6IuWMl+S6rOW4giIsImZyb21fZGlzdHJpY3QiOiIiLCJ0b19wcm92aW5jZSI6IuS4iua1t+W4giIsInRvX2NpdHkiOiLkuIrmtbfluIIiLCJ0b19kaXN0cmljdCI6IiIsImV4cHJlc3NfY29kZSI6IiIsInNlbmRlciI6eyJwcm92aW5jZSI6IiIsImNpdHkiOiIiLCJkaXN0cmljdCI6IiJ9LCJyZWNlaXZlciI6eyJwcm92aW5jZSI6IiIsImNpdHkiOiIiLCJkaXN0cmljdCI6IiJ9LCJ3ZWlnaHQiOjEsImxlbmd0aCI6MCwid2lkdGgiOjAsImhlaWdodCI6MCwidm9sdW1lIjowLCJxdWFudGl0eSI6MCwiZ29vZHNfbmFtZSI6Iua1i+ivleeJqeWTgSIsInBheV9tZXRob2QiOjB9fQ==",
        "expires_at": "0001-01-01T00:00:00Z"
      },
      {
        "express_code": "ZTO",
        "express_name": "中通快递",
        "product_code": "ZTO_BK",
        "product_name": "中通【两小时运力】25",
        "price": 5.6,
        "continued_weight_per_kg": 2.5,
        "calc_weight": 1,
        "order_code": "ENHANCED_ORDER_CODE_eyJzdGFuZGFyZF9jb2RlIjoiWlRPIiwib3JpZ2luYWxfY29kZSI6IlpUTyIsInByb3ZpZGVyIjoieWlkYSIsImNoYW5uZWxfaWQiOiJ5aWRhXzEwMzE3IiwicHJvZHVjdF9jb2RlIjoiWlRPX0JLIiwib3JpZ2luYWxfcmVxdWVzdCI6eyJmcm9tX3Byb3ZpbmNlIjoi5YyX5Lqs5biCIiwiZnJvbV9jaXR5Ijoi5YyX5Lqs5biCIiwiZnJvbV9kaXN0cmljdCI6IiIsInRvX3Byb3ZpbmNlIjoi5LiK5rW35biCIiwidG9fY2l0eSI6IuS4iua1t+W4giIsInRvX2Rpc3RyaWN0IjoiIiwiZXhwcmVzc19jb2RlIjoiIiwic2VuZGVyIjp7InByb3ZpbmNlIjoiIiwiY2l0eSI6IiIsImRpc3RyaWN0IjoiIn0sInJlY2VpdmVyIjp7InByb3ZpbmNlIjoiIiwiY2l0eSI6IiIsImRpc3RyaWN0IjoiIn0sIndlaWdodCI6MSwibGVuZ3RoIjowLCJ3aWR0aCI6MCwiaGVpZ2h0IjowLCJ2b2x1bWUiOjAsInF1YW50aXR5IjowLCJnb29kc19uYW1lIjoi5rWL6K+V54mp5ZOBIiwicGF5X21ldGhvZCI6MH19",
        "expires_at": "0001-01-01T00:00:00Z"
      },
      {
        "express_code": "JT",
        "express_name": "极兔快递",
        "product_code": "",
        "product_name": "",
        "price": 5.9,
        "continued_weight_per_kg": 0,
        "calc_weight": 1,
        "order_code": "ENHANCED_ORDER_CODE_eyJzdGFuZGFyZF9jb2RlIjoiSlQiLCJvcmlnaW5hbF9jb2RlIjoiSlQiLCJwcm92aWRlciI6Imt1YWlkaW5pYW8iLCJjaGFubmVsX2lkIjoiIiwicHJvZHVjdF9jb2RlIjoiIiwib3JpZ2luYWxfcmVxdWVzdCI6eyJmcm9tX3Byb3ZpbmNlIjoi5YyX5Lqs5biCIiwiZnJvbV9jaXR5Ijoi5YyX5Lqs5biCIiwiZnJvbV9kaXN0cmljdCI6IiIsInRvX3Byb3ZpbmNlIjoi5LiK5rW35biCIiwidG9fY2l0eSI6IuS4iua1t+W4giIsInRvX2Rpc3RyaWN0IjoiIiwiZXhwcmVzc19jb2RlIjoiIiwic2VuZGVyIjp7InByb3ZpbmNlIjoiIiwiY2l0eSI6IiIsImRpc3RyaWN0IjoiIn0sInJlY2VpdmVyIjp7InByb3ZpbmNlIjoiIiwiY2l0eSI6IiIsImRpc3RyaWN0IjoiIn0sIndlaWdodCI6MSwibGVuZ3RoIjowLCJ3aWR0aCI6MCwiaGVpZ2h0IjowLCJ2b2x1bWUiOjAsInF1YW50aXR5IjowLCJnb29kc19uYW1lIjoi5rWL6K+V54mp5ZOBIiwicGF5X21ldGhvZCI6MH19",
        "expires_at": "0001-01-01T00:00:00Z"
      },
      {
        "express_code": "ZTO",
        "express_name": "中通快递",
        "product_code": "",
        "product_name": "",
        "price": 5.9,
        "continued_weight_per_kg": 0,
        "calc_weight": 1,
        "order_code": "ENHANCED_ORDER_CODE_eyJzdGFuZGFyZF9jb2RlIjoiWlRPIiwib3JpZ2luYWxfY29kZSI6IlpUTyIsInByb3ZpZGVyIjoia3VhaWRpbmlhbyIsImNoYW5uZWxfaWQiOiIiLCJwcm9kdWN0X2NvZGUiOiIiLCJvcmlnaW5hbF9yZXF1ZXN0Ijp7ImZyb21fcHJvdmluY2UiOiLljJfkuqzluIIiLCJmcm9tX2NpdHkiOiLljJfkuqzluIIiLCJmcm9tX2Rpc3RyaWN0IjoiIiwidG9fcHJvdmluY2UiOiLkuIrmtbfluIIiLCJ0b19jaXR5Ijoi5LiK5rW35biCIiwidG9fZGlzdHJpY3QiOiIiLCJleHByZXNzX2NvZGUiOiIiLCJzZW5kZXIiOnsicHJvdmluY2UiOiIiLCJjaXR5IjoiIiwiZGlzdHJpY3QiOiIifSwicmVjZWl2ZXIiOnsicHJvdmluY2UiOiIiLCJjaXR5IjoiIiwiZGlzdHJpY3QiOiIifSwid2VpZ2h0IjoxLCJsZW5ndGgiOjAsIndpZHRoIjowLCJoZWlnaHQiOjAsInZvbHVtZSI6MCwicXVhbnRpdHkiOjAsImdvb2RzX25hbWUiOiLmtYvor5Xnianlk4EiLCJwYXlfbWV0aG9kIjowfX0=",
        "expires_at": "0001-01-01T00:00:00Z"
      },
      {
        "express_code": "STO",
        "express_name": "申通快递",
        "product_code": "",
        "product_name": "",
        "price": 5.9,
        "continued_weight_per_kg": 0,
        "calc_weight": 1,
        "order_code": "ENHANCED_ORDER_CODE_eyJzdGFuZGFyZF9jb2RlIjoiU1RPIiwib3JpZ2luYWxfY29kZSI6IlNUTyIsInByb3ZpZGVyIjoia3VhaWRpbmlhbyIsImNoYW5uZWxfaWQiOiIiLCJwcm9kdWN0X2NvZGUiOiIiLCJvcmlnaW5hbF9yZXF1ZXN0Ijp7ImZyb21fcHJvdmluY2UiOiLljJfkuqzluIIiLCJmcm9tX2NpdHkiOiLljJfkuqzluIIiLCJmcm9tX2Rpc3RyaWN0IjoiIiwidG9fcHJvdmluY2UiOiLkuIrmtbfluIIiLCJ0b19jaXR5Ijoi5LiK5rW35biCIiwidG9fZGlzdHJpY3QiOiIiLCJleHByZXNzX2NvZGUiOiIiLCJzZW5kZXIiOnsicHJvdmluY2UiOiIiLCJjaXR5IjoiIiwiZGlzdHJpY3QiOiIifSwicmVjZWl2ZXIiOnsicHJvdmluY2UiOiIiLCJjaXR5IjoiIiwiZGlzdHJpY3QiOiIifSwid2VpZ2h0IjoxLCJsZW5ndGgiOjAsIndpZHRoIjowLCJoZWlnaHQiOjAsInZvbHVtZSI6MCwicXVhbnRpdHkiOjAsImdvb2RzX25hbWUiOiLmtYvor5Xnianlk4EiLCJwYXlfbWV0aG9kIjowfX0=",
        "expires_at": "0001-01-01T00:00:00Z"
      },
      {
        "express_code": "YTO",
        "express_name": "圆通速递",
        "product_code": "",
        "product_name": "",
        "price": 5.9,
        "continued_weight_per_kg": 0,
        "calc_weight": 1,
        "order_code": "ENHANCED_ORDER_CODE_eyJzdGFuZGFyZF9jb2RlIjoiWVRPIiwib3JpZ2luYWxfY29kZSI6IllUTyIsInByb3ZpZGVyIjoia3VhaWRpbmlhbyIsImNoYW5uZWxfaWQiOiIiLCJwcm9kdWN0X2NvZGUiOiIiLCJvcmlnaW5hbF9yZXF1ZXN0Ijp7ImZyb21fcHJvdmluY2UiOiLljJfkuqzluIIiLCJmcm9tX2NpdHkiOiLljJfkuqzluIIiLCJmcm9tX2Rpc3RyaWN0IjoiIiwidG9fcHJvdmluY2UiOiLkuIrmtbfluIIiLCJ0b19jaXR5Ijoi5LiK5rW35biCIiwidG9fZGlzdHJpY3QiOiIiLCJleHByZXNzX2NvZGUiOiIiLCJzZW5kZXIiOnsicHJvdmluY2UiOiIiLCJjaXR5IjoiIiwiZGlzdHJpY3QiOiIifSwicmVjZWl2ZXIiOnsicHJvdmluY2UiOiIiLCJjaXR5IjoiIiwiZGlzdHJpY3QiOiIifSwid2VpZ2h0IjoxLCJsZW5ndGgiOjAsIndpZHRoIjowLCJoZWlnaHQiOjAsInZvbHVtZSI6MCwicXVhbnRpdHkiOjAsImdvb2RzX25hbWUiOiLmtYvor5Xnianlk4EiLCJwYXlfbWV0aG9kIjowfX0=",
        "expires_at": "0001-01-01T00:00:00Z"
      },
      {
        "express_code": "YD",
        "express_name": "韵达速递",
        "product_code": "",
        "product_name": "",
        "price": 5.9,
        "continued_weight_per_kg": 0,
        "calc_weight": 1,
        "order_code": "ENHANCED_ORDER_CODE_eyJzdGFuZGFyZF9jb2RlIjoiWUQiLCJvcmlnaW5hbF9jb2RlIjoiWUQiLCJwcm92aWRlciI6Imt1YWlkaW5pYW8iLCJjaGFubmVsX2lkIjoiIiwicHJvZHVjdF9jb2RlIjoiIiwib3JpZ2luYWxfcmVxdWVzdCI6eyJmcm9tX3Byb3ZpbmNlIjoi5YyX5Lqs5biCIiwiZnJvbV9jaXR5Ijoi5YyX5Lqs5biCIiwiZnJvbV9kaXN0cmljdCI6IiIsInRvX3Byb3ZpbmNlIjoi5LiK5rW35biCIiwidG9fY2l0eSI6IuS4iua1t+W4giIsInRvX2Rpc3RyaWN0IjoiIiwiZXhwcmVzc19jb2RlIjoiIiwic2VuZGVyIjp7InByb3ZpbmNlIjoiIiwiY2l0eSI6IiIsImRpc3RyaWN0IjoiIn0sInJlY2VpdmVyIjp7InByb3ZpbmNlIjoiIiwiY2l0eSI6IiIsImRpc3RyaWN0IjoiIn0sIndlaWdodCI6MSwibGVuZ3RoIjowLCJ3aWR0aCI6MCwiaGVpZ2h0IjowLCJ2b2x1bWUiOjAsInF1YW50aXR5IjowLCJnb29kc19uYW1lIjoi5rWL6K+V54mp5ZOBIiwicGF5X21ldGhvZCI6MH19",
        "expires_at": "0001-01-01T00:00:00Z"
      },
      {
        "express_code": "JT",
        "express_name": "极兔快递",
        "product_code": "JTBK",
        "product_name": "普快",
        "price": 6.2,
        "continued_weight_per_kg": 2,
        "calc_weight": 1,
        "order_code": "ENHANCED_ORDER_CODE_eyJzdGFuZGFyZF9jb2RlIjoiSlQiLCJvcmlnaW5hbF9jb2RlIjoiSlQiLCJwcm92aWRlciI6Inl1bnRvbmciLCJjaGFubmVsX2lkIjoi5p6B5YWU5pmu5pWjLUhG5bi46KeE6Leo55yB6YCa55SoKDUuMTYpIiwicHJvZHVjdF9jb2RlIjoiSlRCSyIsIm9yaWdpbmFsX3JlcXVlc3QiOnsiZnJvbV9wcm92aW5jZSI6IuWMl+S6rOW4giIsImZyb21fY2l0eSI6IuWMl+S6rOW4giIsImZyb21fZGlzdHJpY3QiOiIiLCJ0b19wcm92aW5jZSI6IuS4iua1t+W4giIsInRvX2NpdHkiOiLkuIrmtbfluIIiLCJ0b19kaXN0cmljdCI6IiIsImV4cHJlc3NfY29kZSI6IiIsInNlbmRlciI6eyJwcm92aW5jZSI6IiIsImNpdHkiOiIiLCJkaXN0cmljdCI6IiJ9LCJyZWNlaXZlciI6eyJwcm92aW5jZSI6IiIsImNpdHkiOiIiLCJkaXN0cmljdCI6IiJ9LCJ3ZWlnaHQiOjEsImxlbmd0aCI6MCwid2lkdGgiOjAsImhlaWdodCI6MCwidm9sdW1lIjowLCJxdWFudGl0eSI6MCwiZ29vZHNfbmFtZSI6Iua1i+ivleeJqeWTgSIsInBheV9tZXRob2QiOjB9fQ==",
        "expires_at": "0001-01-01T00:00:00Z"
      },
      {
        "express_code": "JT",
        "express_name": "极兔快递",
        "product_code": "JT_BK",
        "product_name": "极兔25",
        "price": 6.2,
        "continued_weight_per_kg": 2.2,
        "calc_weight": 1,
        "order_code": "ENHANCED_ORDER_CODE_eyJzdGFuZGFyZF9jb2RlIjoiSlQiLCJvcmlnaW5hbF9jb2RlIjoiSlQiLCJwcm92aWRlciI6InlpZGEiLCJjaGFubmVsX2lkIjoieWlkYV8xMDMxNiIsInByb2R1Y3RfY29kZSI6IkpUX0JLIiwib3JpZ2luYWxfcmVxdWVzdCI6eyJmcm9tX3Byb3ZpbmNlIjoi5YyX5Lqs5biCIiwiZnJvbV9jaXR5Ijoi5YyX5Lqs5biCIiwiZnJvbV9kaXN0cmljdCI6IiIsInRvX3Byb3ZpbmNlIjoi5LiK5rW35biCIiwidG9fY2l0eSI6IuS4iua1t+W4giIsInRvX2Rpc3RyaWN0IjoiIiwiZXhwcmVzc19jb2RlIjoiIiwic2VuZGVyIjp7InByb3ZpbmNlIjoiIiwiY2l0eSI6IiIsImRpc3RyaWN0IjoiIn0sInJlY2VpdmVyIjp7InByb3ZpbmNlIjoiIiwiY2l0eSI6IiIsImRpc3RyaWN0IjoiIn0sIndlaWdodCI6MSwibGVuZ3RoIjowLCJ3aWR0aCI6MCwiaGVpZ2h0IjowLCJ2b2x1bWUiOjAsInF1YW50aXR5IjowLCJnb29kc19uYW1lIjoi5rWL6K+V54mp5ZOBIiwicGF5X21ldGhvZCI6MH19",
        "expires_at": "0001-01-01T00:00:00Z"
      },
      {
        "express_code": "ZTO",
        "express_name": "中通快递",
        "product_code": "标准快递",
        "product_name": "标准快递",
        "price": 8,
        "continued_weight_per_kg": 2.8,
        "calc_weight": 1,
        "order_code": "ENHANCED_ORDER_CODE_eyJzdGFuZGFyZF9jb2RlIjoiWlRPIiwib3JpZ2luYWxfY29kZSI6IlpUTyIsInByb3ZpZGVyIjoia3VhaWRpMTAwIiwiY2hhbm5lbF9pZCI6Imt1YWlkaTEwMF96aG9uZ3RvbmciLCJwcm9kdWN0X2NvZGUiOiLmoIflh4blv6vpgJIiLCJvcmlnaW5hbF9yZXF1ZXN0Ijp7ImZyb21fcHJvdmluY2UiOiLljJfkuqzluIIiLCJmcm9tX2NpdHkiOiLljJfkuqzluIIiLCJmcm9tX2Rpc3RyaWN0IjoiIiwidG9fcHJvdmluY2UiOiLkuIrmtbfluIIiLCJ0b19jaXR5Ijoi5LiK5rW35biCIiwidG9fZGlzdHJpY3QiOiIiLCJleHByZXNzX2NvZGUiOiIiLCJzZW5kZXIiOnsicHJvdmluY2UiOiIiLCJjaXR5IjoiIiwiZGlzdHJpY3QiOiIifSwicmVjZWl2ZXIiOnsicHJvdmluY2UiOiIiLCJjaXR5IjoiIiwiZGlzdHJpY3QiOiIifSwid2VpZ2h0IjoxLCJsZW5ndGgiOjAsIndpZHRoIjowLCJoZWlnaHQiOjAsInZvbHVtZSI6MCwicXVhbnRpdHkiOjAsImdvb2RzX25hbWUiOiLmtYvor5Xnianlk4EiLCJwYXlfbWV0aG9kIjowfX0=",
        "expires_at": "0001-01-01T00:00:00Z"
      }
    ]
  },
  "success": true
}
[0;32m[SUCCESS][0m ✅ 简单查价测试 - 测试通过

[0;36m=================================================[0m
[0;34m[INFO][0m 📋 第二部分：京东专用查价接口测试 (QUERY_JD_PRICE)
[0;34m[INFO][0m 特点：专门支持京东和德邦快递，实时查询，禁用缓存
[0;36m=================================================[0m
[0;35m[TEST][0m 🟠 京东专用查价接口测试: 京东快递实际重量大于体积重量
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "success": true,
    "code": 200,
    "message": "查询成功",
    "data": [
      {
        "express_code": "DBL",
        "express_name": "debangkuaidi",
        "product_code": "德邦大件360",
        "product_name": "德邦大件360",
        "price": 41.48,
        "continued_weight_per_kg": 2.72,
        "calc_weight": 13,
        "order_code": "ENHANCED_ORDER_CODE_eyJzdGFuZGFyZF9jb2RlIjoiREJMIiwib3JpZ2luYWxfY29kZSI6IkRCTCIsInByb3ZpZGVyIjoia3VhaWRpMTAwIiwiY2hhbm5lbF9pZCI6Imt1YWlkaTEwMF9kZWJhbmdrdWFpZGkiLCJwcm9kdWN0X2NvZGUiOiLlvrfpgqblpKfku7YzNjAiLCJvcmlnaW5hbF9yZXF1ZXN0Ijp7InNlbmRlciI6eyJuYW1lIjoi5byg5LiJIiwibW9iaWxlIjoiMTM4MDAxMzgwMDAiLCJwcm92aW5jZSI6IuWMl+S6rOW4giIsImNpdHkiOiLljJfkuqzluIIiLCJkaXN0cmljdCI6IuacnemYs+WMuiIsImFkZHJlc3MiOiLkuInph4zlsa/ooZfpgZMx5Y+3In0sInJlY2VpdmVyIjp7Im5hbWUiOiLmnY7lm5siLCJtb2JpbGUiOiIxMzkwMDEzOTAwMCIsInByb3ZpbmNlIjoi5LiK5rW35biCIiwiY2l0eSI6IuS4iua1t+W4giIsImRpc3RyaWN0Ijoi5rWm5Lic5paw5Yy6IiwiYWRkcmVzcyI6IumZhuWutuWYtOihl+mBkzLlj7cifSwid2VpZ2h0IjoxMywibGVuZ3RoIjozMCwid2lkdGgiOjIwLCJoZWlnaHQiOjEwLCJ2b2x1bWUiOjAsInF1YW50aXR5IjoxLCJnb29kc19uYW1lIjoi6YeN6LSn5rWL6K+VIiwicGF5X21ldGhvZCI6MH19",
        "expires_at": "永不过期"
      },
      {
        "express_code": "JD",
        "express_name": "标快",
        "product_code": "P1",
        "product_name": "标快",
        "price": 51.04,
        "continued_weight_per_kg": 0,
        "calc_weight": 13,
        "order_code": "ENHANCED_ORDER_CODE_eyJzdGFuZGFyZF9jb2RlIjoiSkQiLCJvcmlnaW5hbF9jb2RlIjoiSkQiLCJwcm92aWRlciI6Inl1bnRvbmciLCJjaGFubmVsX2lkIjoiSkTmma7mlaMtUVjlhajlm73pgJrnlKgiLCJwcm9kdWN0X2NvZGUiOiJQMSIsIm9yaWdpbmFsX3JlcXVlc3QiOnsic2VuZGVyIjp7Im5hbWUiOiLlvKDkuIkiLCJtb2JpbGUiOiIxMzgwMDEzODAwMCIsInByb3ZpbmNlIjoi5YyX5Lqs5biCIiwiY2l0eSI6IuWMl+S6rOW4giIsImRpc3RyaWN0Ijoi5pyd6Ziz5Yy6IiwiYWRkcmVzcyI6IuS4iemHjOWxr+ihl+mBkzHlj7cifSwicmVjZWl2ZXIiOnsibmFtZSI6IuadjuWbmyIsIm1vYmlsZSI6IjEzOTAwMTM5MDAwIiwicHJvdmluY2UiOiLkuIrmtbfluIIiLCJjaXR5Ijoi5LiK5rW35biCIiwiZGlzdHJpY3QiOiLmtabkuJzmlrDljLoiLCJhZGRyZXNzIjoi6ZmG5a625Zi06KGX6YGTMuWPtyJ9LCJ3ZWlnaHQiOjEzLCJsZW5ndGgiOjMwLCJ3aWR0aCI6MjAsImhlaWdodCI6MTAsInZvbHVtZSI6MCwicXVhbnRpdHkiOjEsImdvb2RzX25hbWUiOiLph43otKfmtYvor5UiLCJwYXlfbWV0aG9kIjowfX0=",
        "expires_at": "永不过期"
      },
      {
        "express_code": "NORMAL",
        "express_name": "标准快递",
        "product_code": "3000000080",
        "product_name": "裹裹异业合作版",
        "price": 33.9,
        "continued_weight_per_kg": 2.4,
        "calc_weight": 13,
        "order_code": "ENHANCED_ORDER_CODE_eyJzdGFuZGFyZF9jb2RlIjoiTk9STUFMIiwib3JpZ2luYWxfY29kZSI6Ik5PUk1BTCIsInByb3ZpZGVyIjoiY2FpbmlhbyIsImNoYW5uZWxfaWQiOiJjYWluaWFvX05PUk1BTCIsInByb2R1Y3RfY29kZSI6IjMwMDAwMDAwODAiLCJvcmlnaW5hbF9yZXF1ZXN0Ijp7InNlbmRlciI6eyJuYW1lIjoi5byg5LiJIiwibW9iaWxlIjoiMTM4MDAxMzgwMDAiLCJwcm92aW5jZSI6IuWMl+S6rOW4giIsImNpdHkiOiLljJfkuqzluIIiLCJkaXN0cmljdCI6IuacnemYs+WMuiIsImFkZHJlc3MiOiLkuInph4zlsa/ooZfpgZMx5Y+3In0sInJlY2VpdmVyIjp7Im5hbWUiOiLmnY7lm5siLCJtb2JpbGUiOiIxMzkwMDEzOTAwMCIsInByb3ZpbmNlIjoi5LiK5rW35biCIiwiY2l0eSI6IuS4iua1t+W4giIsImRpc3RyaWN0Ijoi5rWm5Lic5paw5Yy6IiwiYWRkcmVzcyI6IumZhuWutuWYtOihl+mBkzLlj7cifSwid2VpZ2h0IjoxMywibGVuZ3RoIjozMCwid2lkdGgiOjIwLCJoZWlnaHQiOjEwLCJ2b2x1bWUiOjAsInF1YW50aXR5IjoxLCJnb29kc19uYW1lIjoi6YeN6LSn5rWL6K+VIiwicGF5X21ldGhvZCI6MH19",
        "expires_at": "永不过期",
        "pickup_time_info": {
          "pickup_required": true,
          "supports_pickup_code": true,
          "min_advance_hours": 1,
          "time_format": "2006-01-02 15:04:05",
          "available_slots": [
            {
              "slot_id": "2025-07-20_0",
              "slot_name": "明天 09:00-23:59",
              "start_time": "2025-07-20 09:00:00",
              "end_time": "2025-07-20 23:59:00"
            },
            {
              "slot_id": "2025-07-21_0",
              "slot_name": "后天 09:00-23:59",
              "start_time": "2025-07-21 09:00:00",
              "end_time": "2025-07-21 23:59:00"
            }
          ]
        }
      },
      {
        "express_code": "YD",
        "express_name": "韵达快递",
        "product_code": "3000000080",
        "product_name": "裹裹异业合作版",
        "price": 30.200000000000003,
        "continued_weight_per_kg": 2.1,
        "calc_weight": 13,
        "order_code": "ENHANCED_ORDER_CODE_eyJzdGFuZGFyZF9jb2RlIjoiWUQiLCJvcmlnaW5hbF9jb2RlIjoiWUQiLCJwcm92aWRlciI6ImNhaW5pYW8iLCJjaGFubmVsX2lkIjoiY2Fpbmlhb19ZVU5EQSIsInByb2R1Y3RfY29kZSI6IjMwMDAwMDAwODAiLCJvcmlnaW5hbF9yZXF1ZXN0Ijp7InNlbmRlciI6eyJuYW1lIjoi5byg5LiJIiwibW9iaWxlIjoiMTM4MDAxMzgwMDAiLCJwcm92aW5jZSI6IuWMl+S6rOW4giIsImNpdHkiOiLljJfkuqzluIIiLCJkaXN0cmljdCI6IuacnemYs+WMuiIsImFkZHJlc3MiOiLkuInph4zlsa/ooZfpgZMx5Y+3In0sInJlY2VpdmVyIjp7Im5hbWUiOiLmnY7lm5siLCJtb2JpbGUiOiIxMzkwMDEzOTAwMCIsInByb3ZpbmNlIjoi5LiK5rW35biCIiwiY2l0eSI6IuS4iua1t+W4giIsImRpc3RyaWN0Ijoi5rWm5Lic5paw5Yy6IiwiYWRkcmVzcyI6IumZhuWutuWYtOihl+mBkzLlj7cifSwid2VpZ2h0IjoxMywibGVuZ3RoIjozMCwid2lkdGgiOjIwLCJoZWlnaHQiOjEwLCJ2b2x1bWUiOjAsInF1YW50aXR5IjoxLCJnb29kc19uYW1lIjoi6YeN6LSn5rWL6K+VIiwicGF5X21ldGhvZCI6MH19",
        "expires_at": "永不过期",
        "pickup_time_info": {
          "pickup_required": true,
          "supports_pickup_code": true,
          "min_advance_hours": 1,
          "time_format": "2006-01-02 15:04:05",
          "available_slots": [
            {
              "slot_id": "2025-07-20_0",
              "slot_name": "明天 09:00-23:59",
              "start_time": "2025-07-20 09:00:00",
              "end_time": "2025-07-20 23:59:00"
            },
            {
              "slot_id": "2025-07-21_0",
              "slot_name": "后天 09:00-23:59",
              "start_time": "2025-07-21 09:00:00",
              "end_time": "2025-07-21 23:59:00"
            }
          ]
        }
      },
      {
        "express_code": "STO",
        "express_name": "申通快递",
        "product_code": "3000000080",
        "product_name": "裹裹异业合作版",
        "price": 30.300000000000004,
        "continued_weight_per_kg": 2.1,
        "calc_weight": 13,
        "order_code": "ENHANCED_ORDER_CODE_eyJzdGFuZGFyZF9jb2RlIjoiU1RPIiwib3JpZ2luYWxfY29kZSI6IlNUTyIsInByb3ZpZGVyIjoiY2FpbmlhbyIsImNoYW5uZWxfaWQiOiJjYWluaWFvX1NUTyIsInByb2R1Y3RfY29kZSI6IjMwMDAwMDAwODAiLCJvcmlnaW5hbF9yZXF1ZXN0Ijp7InNlbmRlciI6eyJuYW1lIjoi5byg5LiJIiwibW9iaWxlIjoiMTM4MDAxMzgwMDAiLCJwcm92aW5jZSI6IuWMl+S6rOW4giIsImNpdHkiOiLljJfkuqzluIIiLCJkaXN0cmljdCI6IuacnemYs+WMuiIsImFkZHJlc3MiOiLkuInph4zlsa/ooZfpgZMx5Y+3In0sInJlY2VpdmVyIjp7Im5hbWUiOiLmnY7lm5siLCJtb2JpbGUiOiIxMzkwMDEzOTAwMCIsInByb3ZpbmNlIjoi5LiK5rW35biCIiwiY2l0eSI6IuS4iua1t+W4giIsImRpc3RyaWN0Ijoi5rWm5Lic5paw5Yy6IiwiYWRkcmVzcyI6IumZhuWutuWYtOihl+mBkzLlj7cifSwid2VpZ2h0IjoxMywibGVuZ3RoIjozMCwid2lkdGgiOjIwLCJoZWlnaHQiOjEwLCJ2b2x1bWUiOjAsInF1YW50aXR5IjoxLCJnb29kc19uYW1lIjoi6YeN6LSn5rWL6K+VIiwicGF5X21ldGhvZCI6MH19",
        "expires_at": "永不过期",
        "pickup_time_info": {
          "pickup_required": true,
          "supports_pickup_code": true,
          "min_advance_hours": 1,
          "time_format": "2006-01-02 15:04:05",
          "available_slots": [
            {
              "slot_id": "2025-07-20_0",
              "slot_name": "明天 09:00-23:59",
              "start_time": "2025-07-20 09:00:00",
              "end_time": "2025-07-20 23:59:00"
            },
            {
              "slot_id": "2025-07-21_0",
              "slot_name": "后天 09:00-23:59",
              "start_time": "2025-07-21 09:00:00",
              "end_time": "2025-07-21 23:59:00"
            }
          ]
        }
      }
    ]
  },
  "success": true
}
[0;32m[SUCCESS][0m ✅ 京东快递实际重量大于体积重量 - 测试通过

[0;36m=================================================[0m
[0;34m[INFO][0m 📊 测试结果分析和对比
[0;36m=================================================[0m
[0;36m接口对比分析：[0m
1. 📋 标准查价接口 (QUERY_PRICE)
   - 支持快递公司：所有启用的快递公司（除JD/DBL）
   - 缓存策略：启用缓存，提升查询性能
   - 适用场景：常规业务查价，支持批量查询
   - 响应时间：快（缓存加速）

2. 🟠 京东专用查价接口 (QUERY_JD_PRICE)
   - 支持快递公司：京东快递(JD)、德邦快递(DBL)
   - 缓存策略：禁用缓存，强制实时查询
   - 适用场景：高精度计费，与下单价格完全一致
   - 响应时间：较慢（实时API调用）
   - 特殊功能：熔断器保护、重试机制、超时控制

3. 🔧 体积重量计算规则
   - 京东快递抛比：8000（体积重量 = 长×宽×高÷8000）
   - 德邦快递抛比：6000（体积重量 = 长×宽×高÷6000）
   - 计费重量：取实际重量和体积重量的最大值

4. 🎯 使用建议
   - 日常查价：使用标准查价接口，性能更好
   - 京东/德邦查价：使用京东专用接口，价格更准确
   - 下单前验证：使用对应的专用接口确保价格一致性
[0;36m=================================================[0m
[0;32m[SUCCESS][0m ✅ 所有测试完成！
[0;36m=================================================[0m
