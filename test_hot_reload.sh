#!/bin/bash

echo "🔥 测试热重载修复效果"
echo "=================================="

# 配置
ADMIN_API="http://localhost:8081/api/v1/admin/express/mappings"
GATEWAY_API="http://localhost:8081/api/gateway/execute"
MAPPING_ID="80acd935-24bd-4f48-b5ef-83141b4eee23"  # YD韵达在kuaidi100下的映射ID

# 测试查价请求
test_price_query() {
    echo "📋 执行查价测试..."
    curl -s -X POST "$GATEWAY_API" \
        -H "Content-Type: application/json" \
        -d '{
            "service": "price_query",
            "data": {
                "from_province": "天津市",
                "from_city": "天津市",
                "from_district": "和平区",
                "to_province": "广东省", 
                "to_city": "广州市",
                "to_district": "天河区",
                "weight": 3.7,
                "query_all": true
            }
        }' | jq -r '.data.results[] | "\(.express_code) - \(.provider) - \(.price)"' | grep YD || echo "❌ YD不在结果中"
}

# 禁用YD
disable_yd() {
    echo "🚫 禁用 YD（韵达）映射..."
    curl -s -X PUT "$ADMIN_API/$MAPPING_ID" \
        -H "Content-Type: application/json" \
        -d '{"is_supported": false}' > /dev/null
    echo "✅ 禁用请求已发送"
}

# 启用YD  
enable_yd() {
    echo "✅ 启用 YD（韵达）映射..."
    curl -s -X PUT "$ADMIN_API/$MAPPING_ID" \
        -H "Content-Type: application/json" \
        -d '{"is_supported": true}' > /dev/null
    echo "✅ 启用请求已发送"
}

echo "🟢 1. 初始状态 - 启用YD"
enable_yd
sleep 3
test_price_query

echo ""
echo "🔴 2. 禁用YD，测试热重载"
disable_yd
echo "⏱️  等待3秒让热重载生效..."
sleep 3
test_price_query

echo ""
echo "🟢 3. 重新启用YD，测试热重载"
enable_yd
echo "⏱️  等待3秒让热重载生效..."
sleep 3  
test_price_query

echo ""
echo "🎯 测试完成！观察日志确认："
echo "   1. InvalidateCacheByPattern 显示 deleted_count > 0"
echo "   2. RefreshProviderCache 显示正确的映射列表"
echo "   3. 查价结果立即反映配置变更" 