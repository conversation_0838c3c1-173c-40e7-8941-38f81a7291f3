#!/bin/bash

# 快递鸟取消中订单诊断修复脚本
# 用于诊断和修复卡在cancelling状态的快递鸟订单

set -e

echo "🔍 快递鸟取消中订单诊断修复脚本"
echo "=================================="

# 数据库连接信息
DB_HOST="*************"
DB_PORT="5432"
DB_USER="postgres"
DB_NAME="go_kuaidi"
DB_PASSWORD="gjx6ngf4"

# 服务器信息
API_BASE_URL="http://localhost:8081"

echo "📊 1. 统计快递鸟取消中订单数量..."
CANCELLING_COUNT=$(PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -t -c "SELECT COUNT(*) FROM order_records WHERE provider = 'kuaidiniao' AND status = 'cancelling';" | tr -d ' ')
echo "   快递鸟取消中订单数量: $CANCELLING_COUNT"

if [ "$CANCELLING_COUNT" -eq 0 ]; then
    echo "✅ 没有发现取消中的快递鸟订单，无需处理"
    exit 0
fi

echo ""
echo "📋 2. 获取取消中订单详情..."
# 获取所有取消中的快递鸟订单（只处理1小时前的订单，避免干扰正在处理的订单）
ORDERS=$(PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -t -c "
SELECT customer_order_no || ':' || order_no || ':' || tracking_no
FROM order_records 
WHERE provider = 'kuaidiniao' 
  AND status = 'cancelling' 
  AND updated_at < NOW() - INTERVAL '1 hour'
ORDER BY updated_at DESC;
" | sed 's/^ *//' | grep -v '^$')

if [ -z "$ORDERS" ]; then
    echo "   ⚠️  所有取消中订单都是1小时内更新的，可能正在处理中，跳过自动修复"
    echo "   💡 建议：等待1小时后再运行此脚本"
    exit 0
fi

echo "   找到以下需要处理的订单:"
echo "$ORDERS" | while IFS=':' read -r customer_order_no order_no tracking_no; do
    printf "   - %s (%s)\n" "$customer_order_no" "$order_no"
done

echo ""
echo "🔧 3. 开始诊断和修复流程..."

success_count=0
failed_count=0
total_orders=$(echo "$ORDERS" | wc -l)
current_order=0

echo "$ORDERS" | while IFS=':' read -r customer_order_no order_no tracking_no; do
    current_order=$((current_order + 1))
    echo ""
    echo "🔍 处理订单 [$current_order/$total_orders]: $customer_order_no ($order_no)"
    
    echo "   📞 直接发送取消确认回调（基于超时逻辑）..."
    
    # 对于超过1小时仍在cancelling状态的订单，直接发送取消确认回调
    # 这是因为快递鸟的取消操作通常在几分钟内完成，超过1小时说明可能有问题
    
    callback_data=$(cat <<EOF
{
    "PushTime": "$(date '+%Y-%m-%d %H:%M:%S')",
    "EBusinessID": "1778716",
    "Data": [{
        "ShipperCode": "STO",
        "State": "203",
        "CreateTime": "$(date '+%Y-%m-%d %H:%M:%S')",
        "KDNOrderCode": "$tracking_no",
        "OrderCode": "$order_no",
        "Reason": "诊断脚本超时确认已取消",
        "OperateType": 1,
        "CallRequestType": "1801"
    }],
    "Count": 1
}
EOF
    )
    
    # URL编码回调数据
    encoded_data=$(echo "$callback_data" | jq -c . | python3 -c "import sys, urllib.parse; print(urllib.parse.quote(sys.stdin.read().strip()))")
    
    # 发送回调请求
    echo "   📤 发送取消确认回调..."
    callback_response=$(curl -s -X POST "$API_BASE_URL/api/v1/callbacks/kuaidiniao" \
        -H "Content-Type: application/x-www-form-urlencoded; charset=UTF-8" \
        -H "User-Agent: KuaidiNiao-Diagnostic-Script/1.0" \
        -d "RequestData=$encoded_data&DataSign=DIAGNOSTIC_SCRIPT&RequestType=103")
    
    # 检查回调响应
    if echo "$callback_response" | grep -q '"Success":true'; then
        echo "   ✅ 取消确认回调发送成功"
        ((success_count++))
    else
        echo "   ❌ 取消确认回调发送失败: $callback_response"
        ((failed_count++))
    fi
    
    # 避免请求过快
    sleep 0.5
done

echo ""
echo "📊 4. 处理结果统计:"
echo "   ✅ 成功处理: $success_count 个订单"
echo "   ❌ 处理失败: $failed_count 个订单"

echo ""
echo "📋 5. 等待3秒后检查最终状态..."
sleep 3

FINAL_CANCELLING_COUNT=$(PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -t -c "SELECT COUNT(*) FROM order_records WHERE provider = 'kuaidiniao' AND status = 'cancelling';" | tr -d ' ')
echo "   处理后快递鸟取消中订单数量: $FINAL_CANCELLING_COUNT"

if [ "$FINAL_CANCELLING_COUNT" -lt "$CANCELLING_COUNT" ]; then
    PROCESSED_COUNT=$((CANCELLING_COUNT - FINAL_CANCELLING_COUNT))
    echo "   🎉 成功处理了 $PROCESSED_COUNT 个订单"
else
    echo "   ⚠️  订单数量没有减少，可能需要手动检查"
fi

echo ""
echo "🏁 诊断修复脚本执行完成！"
