#!/bin/bash

# 快递鸟取消订单逻辑测试脚本
# 测试修改后的逻辑：直接变更为已取消状态，跳过"取消中"状态

echo "🧪 快递鸟取消订单逻辑测试"
echo "================================"

# 配置
API_BASE="http://localhost:8081"
TOKEN="****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"

# 1. 查找一个快递鸟的可取消订单（assigned状态）
echo "🔍 1. 查找快递鸟的可取消订单..."
KUAIDINIAO_ORDER=$(curl -s "${API_BASE}/api/v1/admin/orders?provider=kuaidiniao&status=assigned&page=1&page_size=1" \
  -H "Authorization: Bearer $TOKEN" | jq -r '.data.records[0].order_no // empty')

if [ -z "$KUAIDINIAO_ORDER" ]; then
  echo "❌ 没有找到快递鸟的可取消订单，无法测试"
  exit 1
fi

echo "✅ 找到快递鸟订单: $KUAIDINIAO_ORDER"

# 2. 查看订单当前状态
echo ""
echo "🔍 2. 查看订单当前状态..."
CURRENT_STATUS=$(curl -s "${API_BASE}/api/v1/admin/orders/${KUAIDINIAO_ORDER}" \
  -H "Authorization: Bearer $TOKEN" | jq -r '.data.status')

echo "📋 当前状态: $CURRENT_STATUS"

# 3. 发起取消请求
echo ""
echo "🚀 3. 发起快递鸟订单取消请求..."
CANCEL_RESPONSE=$(curl -s -X POST "${API_BASE}/api/v1/express/order/cancel" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d "{
    \"order_no\": \"$KUAIDINIAO_ORDER\",
    \"reason\": \"测试快递鸟直接取消逻辑\"
  }")

echo "📤 取消请求响应:"
echo "$CANCEL_RESPONSE" | jq .

# 4. 检查响应中的状态
RESPONSE_STATUS=$(echo "$CANCEL_RESPONSE" | jq -r '.data.status // empty')
RESPONSE_MESSAGE=$(echo "$CANCEL_RESPONSE" | jq -r '.message // empty')

echo ""
echo "🔍 4. 验证取消响应..."
echo "📋 响应状态: $RESPONSE_STATUS"
echo "📋 响应消息: $RESPONSE_MESSAGE"

# 5. 验证订单状态是否直接变为已取消
echo ""
echo "🔍 5. 验证订单状态是否直接变为已取消..."
sleep 2  # 等待2秒确保状态更新

FINAL_STATUS=$(curl -s "${API_BASE}/api/v1/admin/orders/${KUAIDINIAO_ORDER}" \
  -H "Authorization: Bearer $TOKEN" | jq -r '.data.status')

echo "📋 最终状态: $FINAL_STATUS"

# 6. 结果验证
echo ""
echo "🎯 6. 测试结果验证..."
echo "================================"

if [ "$RESPONSE_STATUS" = "cancelled" ]; then
  echo "✅ 响应状态正确: 直接返回已取消状态"
else
  echo "❌ 响应状态错误: 期望 'cancelled'，实际 '$RESPONSE_STATUS'"
fi

if [ "$FINAL_STATUS" = "cancelled" ]; then
  echo "✅ 订单状态正确: 直接变为已取消状态"
else
  echo "❌ 订单状态错误: 期望 'cancelled'，实际 '$FINAL_STATUS'"
fi

if [[ "$RESPONSE_MESSAGE" == *"订单取消成功"* ]]; then
  echo "✅ 响应消息正确: 包含取消成功信息"
else
  echo "❌ 响应消息错误: 期望包含'订单取消成功'，实际 '$RESPONSE_MESSAGE'"
fi

# 7. 总结
echo ""
echo "📊 测试总结:"
echo "================================"
echo "订单号: $KUAIDINIAO_ORDER"
echo "初始状态: $CURRENT_STATUS"
echo "最终状态: $FINAL_STATUS"
echo "响应状态: $RESPONSE_STATUS"
echo "响应消息: $RESPONSE_MESSAGE"

if [ "$RESPONSE_STATUS" = "cancelled" ] && [ "$FINAL_STATUS" = "cancelled" ]; then
  echo ""
  echo "🎉 测试通过！快递鸟取消订单逻辑修改成功："
  echo "   ✅ 跳过了'取消中'状态"
  echo "   ✅ 直接变更为'已取消'状态"
  echo "   ✅ 立即处理了退款"
else
  echo ""
  echo "❌ 测试失败！需要检查修改是否生效"
fi
