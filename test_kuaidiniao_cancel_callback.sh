#!/bin/bash

# 测试快递鸟取消订单的外部回调转发逻辑
# 验证快递鸟订单取消时是否正确触发外部回调转发

echo "🧪 快递鸟取消订单外部回调转发测试"
echo "================================"

# 配置
API_BASE="http://localhost:8081"
TOKEN="****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"

# 1. 查找一个快递鸟的assigned订单
echo "🔍 1. 查找快递鸟的assigned订单..."
KUAIDINIAO_ORDER=$(psql "*************************************************/go_kuaidi" -t -c "SELECT order_no FROM order_records WHERE provider = 'kuaidiniao' AND status = 'assigned' ORDER BY created_at DESC LIMIT 1;" | xargs)

if [ -z "$KUAIDINIAO_ORDER" ]; then
  echo "❌ 没有找到快递鸟的assigned订单，无法测试"
  exit 1
fi

echo "✅ 找到快递鸟订单: $KUAIDINIAO_ORDER"

# 2. 查看订单当前状态
echo ""
echo "🔍 2. 查看订单当前状态..."
CURRENT_STATUS=$(psql "*************************************************/go_kuaidi" -t -c "SELECT status FROM order_records WHERE order_no = '$KUAIDINIAO_ORDER';" | xargs)
USER_ID=$(psql "*************************************************/go_kuaidi" -t -c "SELECT user_id FROM order_records WHERE order_no = '$KUAIDINIAO_ORDER';" | xargs)

echo "📋 当前状态: $CURRENT_STATUS"
echo "📋 用户ID: $USER_ID"

# 3. 记录回调转发前的状态
echo ""
echo "🔍 3. 记录回调转发前的状态..."
BEFORE_CALLBACK_COUNT=$(psql "*************************************************/callback_receiver" -t -c "SELECT COUNT(*) FROM callback_raw_data WHERE raw_body LIKE '%$KUAIDINIAO_ORDER%';" 2>/dev/null | xargs || echo "0")

echo "📋 回调转发前记录数: $BEFORE_CALLBACK_COUNT"

# 4. 使用普通用户API取消订单（模拟真实场景）
echo ""
echo "🚀 4. 使用普通用户API取消快递鸟订单..."

# 先获取用户token
USER_TOKEN=$(curl -s -X POST "${API_BASE}/api/v1/auth/login" \
  -H "Content-Type: application/json" \
  -d "{\"username\": \"mywl\", \"password\": \"NNJJ@178..n\"}" | jq -r '.data.token')

if [ "$USER_TOKEN" = "null" ] || [ -z "$USER_TOKEN" ]; then
  echo "❌ 获取用户token失败，使用管理员token"
  USER_TOKEN="$TOKEN"
fi

echo "📋 使用token: ${USER_TOKEN:0:50}..."

# 取消订单
CANCEL_RESPONSE=$(curl -s -X POST "${API_BASE}/api/v1/express/order/cancel" \
  -H "Authorization: Bearer $USER_TOKEN" \
  -H "Content-Type: application/json" \
  -d "{
    \"order_no\": \"$KUAIDINIAO_ORDER\",
    \"reason\": \"测试快递鸟外部回调转发逻辑\"
  }")

echo "📤 取消响应:"
echo "$CANCEL_RESPONSE" | jq .

# 检查取消是否成功
CANCEL_SUCCESS=$(echo "$CANCEL_RESPONSE" | jq -r '.success // false')
if [ "$CANCEL_SUCCESS" != "true" ]; then
  echo "❌ 订单取消失败，无法继续测试"
  echo "错误信息: $(echo "$CANCEL_RESPONSE" | jq -r '.message // "未知错误"')"
  exit 1
fi

echo "✅ 订单取消请求成功"

# 5. 等待一段时间让回调处理完成
echo ""
echo "⏳ 5. 等待回调处理完成..."
sleep 8

# 6. 检查订单状态是否更新
echo ""
echo "🔍 6. 检查订单状态是否更新..."
FINAL_STATUS=$(psql "*************************************************/go_kuaidi" -t -c "SELECT status FROM order_records WHERE order_no = '$KUAIDINIAO_ORDER';" | xargs)

echo "📋 最终状态: $FINAL_STATUS"

# 7. 检查是否有新的回调转发记录
echo ""
echo "🔍 7. 检查是否有新的回调转发记录..."
AFTER_CALLBACK_COUNT=$(psql "*************************************************/callback_receiver" -t -c "SELECT COUNT(*) FROM callback_raw_data WHERE raw_body LIKE '%$KUAIDINIAO_ORDER%';" 2>/dev/null | xargs || echo "0")

echo "📋 回调转发后记录数: $AFTER_CALLBACK_COUNT"

# 8. 检查服务日志中的外部回调转发记录
echo ""
echo "🔍 8. 检查服务日志中的外部回调转发记录..."
echo "查找最近的外部回调转发日志..."

# 查找最新的日志文件
LATEST_LOG=$(ls -t logs/go-kuaidi-local-*.log 2>/dev/null | head -n 1)
if [ -n "$LATEST_LOG" ]; then
  echo "📋 检查日志文件: $LATEST_LOG"
  
  # 查找与该订单相关的回调转发日志
  CALLBACK_LOGS=$(grep -n "$KUAIDINIAO_ORDER\|外部回调转发\|triggerCancellationCallback\|sendInternalCallback\|快递鸟直接取消" "$LATEST_LOG" 2>/dev/null | tail -n 15)
  
  if [ -n "$CALLBACK_LOGS" ]; then
    echo "📋 找到相关回调日志:"
    echo "$CALLBACK_LOGS"
  else
    echo "❌ 没有找到相关的回调转发日志"
  fi
else
  echo "❌ 没有找到日志文件"
fi

# 9. 检查快递鸟回调接口是否收到内部回调
echo ""
echo "🔍 9. 检查快递鸟回调接口是否收到内部回调..."
KUAIDINIAO_CALLBACK_COUNT=$(psql "*************************************************/callback_receiver" -t -c "SELECT COUNT(*) FROM callback_raw_data WHERE provider = 'kuaidiniao' AND raw_body LIKE '%$KUAIDINIAO_ORDER%' AND raw_body LIKE '%203%';" 2>/dev/null | xargs || echo "0")

echo "📋 快递鸟取消回调记录数: $KUAIDINIAO_CALLBACK_COUNT"

# 10. 结果验证
echo ""
echo "🎯 10. 测试结果验证..."
echo "================================"

SUCCESS=true

if [ "$FINAL_STATUS" = "cancelled" ]; then
  echo "✅ 订单状态正确: 已更新为已取消状态"
else
  echo "❌ 订单状态错误: 期望 'cancelled'，实际 '$FINAL_STATUS'"
  SUCCESS=false
fi

if [ "$AFTER_CALLBACK_COUNT" -gt "$BEFORE_CALLBACK_COUNT" ]; then
  echo "✅ 回调转发记录增加: 从 $BEFORE_CALLBACK_COUNT 增加到 $AFTER_CALLBACK_COUNT"
else
  echo "⚠️  回调转发记录未增加: 前 $BEFORE_CALLBACK_COUNT，后 $AFTER_CALLBACK_COUNT"
fi

if [ "$KUAIDINIAO_CALLBACK_COUNT" -gt "0" ]; then
  echo "✅ 快递鸟取消回调已生成: $KUAIDINIAO_CALLBACK_COUNT 条记录"
else
  echo "❌ 快递鸟取消回调未生成"
  SUCCESS=false
fi

if [ -n "$CALLBACK_LOGS" ]; then
  echo "✅ 找到回调转发相关日志"
else
  echo "⚠️  没有找到回调转发日志（可能需要检查实现）"
fi

# 11. 总结
echo ""
echo "📊 测试总结:"
echo "================================"
echo "订单号: $KUAIDINIAO_ORDER"
echo "用户ID: $USER_ID"
echo "初始状态: $CURRENT_STATUS"
echo "最终状态: $FINAL_STATUS"
echo "回调前记录数: $BEFORE_CALLBACK_COUNT"
echo "回调后记录数: $AFTER_CALLBACK_COUNT"
echo "快递鸟取消回调数: $KUAIDINIAO_CALLBACK_COUNT"

if [ "$SUCCESS" = true ]; then
  echo ""
  echo "🎉 测试基本通过！快递鸟取消订单外部回调转发功能正常"
  echo "   ✅ 订单已更新为已取消状态"
  echo "   ✅ 快递鸟取消回调已生成"
  echo "   📋 外部回调转发逻辑已触发"
else
  echo ""
  echo "❌ 测试失败！需要检查实现"
fi

echo ""
echo "💡 注意事项："
echo "   - 外部回调转发需要真实的回调URL才能完全验证"
echo "   - 当前主要验证内部回调生成和转发逻辑是否被触发"
echo "   - 检查服务日志以确认sendInternalCallback方法是否被调用"
