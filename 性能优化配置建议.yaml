# Go-Kuaidi 性能优化配置建议
# 基于性能测试结果的优化配置
# 测试结果: 最大QPS 3.16, 50并发用户, 响应时间过长

# =====================================================
# 1. 缓存策略优化 - 立即执行
# =====================================================
performance:
  cache:
    # 延长缓存时间，减少API调用
    price_ttl_minutes: 10           # 从5分钟延长到10分钟
    fast_query_ttl_minutes: 8       # 从3分钟延长到8分钟
    fallback_ttl_minutes: 5         # 从2分钟延长到5分钟
    warmup_ttl_minutes: 15          # 从10分钟延长到15分钟
    
    # 新增缓存预热配置
    warmup_enabled: true            # 启用缓存预热
    warmup_routes:                  # 预热热门路线
      - from: "北京市"
        to: "上海市"
      - from: "广东省"
        to: "浙江省"
      - from: "江苏省"
        to: "山东省"
    
    # 缓存层级配置
    local_cache_enabled: true       # 启用本地缓存
    local_cache_size: 1000          # 本地缓存条目数
    local_cache_ttl_seconds: 300    # 本地缓存5分钟

  # 超时配置优化
  timeout:
    provider_query_ms: 800          # 从1000ms减少到800ms
    total_query_ms: 2500            # 从3000ms减少到2500ms
    http_client_seconds: 8          # 从10秒减少到8秒
    database_seconds: 3             # 从5秒减少到3秒
    redis_seconds: 2                # 从3秒减少到2秒

  # 并发控制优化
  concurrency:
    worker_pool_size: 30            # 从20增加到30
    task_queue_size: 200            # 从100增加到200
    max_concurrent_queries: 80      # 从50增加到80
    
    # 新增并发限制配置
    max_concurrent_users: 40        # 限制最大并发用户数
    queue_timeout_ms: 5000          # 队列等待超时
    circuit_breaker_enabled: true   # 启用熔断器

# =====================================================
# 2. 数据库连接池优化
# =====================================================
database:
  # 连接池配置优化
  max_open_conns: 300              # 从200增加到300
  max_idle_conns: 150              # 从100增加到150
  conn_max_lifetime: 1200          # 从1800减少到1200秒(20分钟)
  conn_max_idle_time: 180          # 从300减少到180秒(3分钟)
  
  # 新增数据库性能配置
  query_timeout_seconds: 3         # 查询超时3秒
  slow_query_threshold_ms: 500     # 慢查询阈值500ms
  connection_retry_attempts: 3     # 连接重试次数

# =====================================================
# 3. Redis连接池优化
# =====================================================
redis:
  # 连接池配置优化
  pool_size: 150                   # 从100增加到150
  min_idle_conns: 30               # 从20增加到30
  max_conn_age: 1200               # 从1800减少到1200秒
  pool_timeout: 5                  # 从10减少到5秒
  idle_timeout: 180                # 从300减少到180秒
  
  # 新增Redis性能配置
  dial_timeout: 3                  # 连接超时3秒
  read_timeout: 2                  # 读取超时2秒
  write_timeout: 2                 # 写入超时2秒
  max_retries: 2                   # 最大重试2次

# =====================================================
# 4. 供应商API优化配置
# =====================================================
suppliers:
  # 供应商优先级配置（基于响应速度）
  priority_order:
    - yida                         # 易达 - 响应最快
    - kuaidi100                    # 快递100 - 稳定性好
    - yuntong                      # 云通 - 备用
    - kuaidiniao                   # 快递鸟 - 功能丰富
    - cainiao                      # 菜鸟 - 功能丰富但较慢
  
  # 供应商超时配置
  timeouts:
    yida: 600                      # 易达600ms
    kuaidi100: 700                 # 快递100 700ms
    yuntong: 800                   # 云通800ms
    kuaidiniao: 900                # 快递鸟900ms
    cainiao: 1000                  # 菜鸟1000ms
  
  # 熔断器配置
  circuit_breaker:
    failure_threshold: 5           # 失败阈值5次
    success_threshold: 3           # 成功阈值3次
    timeout_seconds: 30            # 熔断30秒
    
  # 重试配置
  retry:
    max_attempts: 2                # 最大重试2次
    initial_delay_ms: 50           # 初始延迟50ms
    max_delay_ms: 200              # 最大延迟200ms

# =====================================================
# 5. 查价接口优化配置
# =====================================================
price_query:
  # 快速响应策略优化
  fast_response:
    enabled: true
    max_wait_ms: 1200              # 从1500ms减少到1200ms
    first_stage_timeout_ms: 600    # 从800ms减少到600ms
    provider_timeout_ms: 500       # 从600ms减少到500ms
    min_results: 1                 # 至少1个结果就返回
    
  # 降级策略优化
  fallback:
    enabled: true
    timeout_ms: 1500               # 从2000ms减少到1500ms
    preferred_providers:           # 按响应速度排序
      - yida
      - kuaidi100
    
  # 智能路由配置
  smart_routing:
    enabled: true                  # 启用智能路由
    response_time_weight: 0.6      # 响应时间权重60%
    success_rate_weight: 0.4       # 成功率权重40%
    min_samples: 10                # 最少样本数10个

# =====================================================
# 6. 监控告警配置
# =====================================================
monitoring:
  # 性能指标阈值
  thresholds:
    response_time_warning_ms: 1000    # 响应时间警告阈值
    response_time_critical_ms: 2000   # 响应时间严重阈值
    error_rate_warning: 0.05          # 错误率警告5%
    error_rate_critical: 0.10         # 错误率严重10%
    qps_warning: 2.0                  # QPS下降警告
    concurrent_users_limit: 40        # 并发用户限制
    
  # 系统资源阈值
  system:
    cpu_warning: 0.70                 # CPU使用率警告70%
    cpu_critical: 0.85                # CPU使用率严重85%
    memory_warning: 0.80              # 内存使用率警告80%
    memory_critical: 0.90             # 内存使用率严重90%
    
  # 数据库监控
  database:
    connection_pool_warning: 0.80     # 连接池使用率警告80%
    slow_query_threshold_ms: 500      # 慢查询阈值500ms
    
  # Redis监控
  redis:
    connection_pool_warning: 0.80     # 连接池使用率警告80%
    memory_usage_warning: 0.80       # 内存使用率警告80%

# =====================================================
# 7. 限流配置
# =====================================================
rate_limiting:
  # 全局限流
  global:
    enabled: true
    requests_per_second: 50          # 全局每秒50请求
    burst_size: 100                  # 突发大小100
    
  # 用户级限流
  per_user:
    enabled: true
    requests_per_minute: 100         # 每用户每分钟100请求
    burst_size: 20                   # 突发大小20
    
  # API级限流
  per_api:
    price_query:
      requests_per_second: 30        # 查价接口每秒30请求
      burst_size: 60                 # 突发大小60

# =====================================================
# 8. 日志配置优化
# =====================================================
logging:
  # 性能日志
  performance:
    enabled: true
    slow_request_threshold_ms: 1000  # 慢请求阈值1000ms
    log_request_body: false          # 不记录请求体（减少日志量）
    log_response_body: false         # 不记录响应体
    
  # 错误日志
  error:
    enabled: true
    include_stack_trace: true        # 包含堆栈跟踪
    max_error_length: 1000           # 最大错误长度

# =====================================================
# 9. 健康检查配置
# =====================================================
health_check:
  enabled: true
  interval_seconds: 30               # 检查间隔30秒
  timeout_seconds: 5                 # 超时5秒
  
  # 检查项目
  checks:
    - database_connection
    - redis_connection
    - supplier_apis
    - memory_usage
    - cpu_usage

# =====================================================
# 10. 自动扩容配置
# =====================================================
auto_scaling:
  enabled: false                     # 暂时禁用，需要容器化支持
  
  # 扩容触发条件
  triggers:
    cpu_threshold: 0.70              # CPU使用率70%
    memory_threshold: 0.80           # 内存使用率80%
    response_time_threshold_ms: 1500 # 响应时间1500ms
    error_rate_threshold: 0.05       # 错误率5%
    
  # 扩容配置
  scaling:
    min_instances: 1
    max_instances: 3
    scale_up_cooldown_minutes: 5
    scale_down_cooldown_minutes: 10

---
# 配置说明:
# 1. 此配置基于性能测试结果制定
# 2. 优先解决响应时间过长问题
# 3. 提升系统并发处理能力
# 4. 增强系统稳定性和监控
# 5. 建议分阶段实施，先执行缓存和超时优化
