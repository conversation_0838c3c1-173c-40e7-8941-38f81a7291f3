# 🚀 超高性能数据一致性系统使用说明

## 性能指标
- **查询速度**: 1秒查询1万条记录
- **响应时间**: < 2秒
- **数据更新**: 每5分钟自动更新

## 快速查询命令

### 1. 查询所有不一致订单
```sql
SELECT * FROM get_inconsistent_orders(
    CURRENT_DATE - INTERVAL '7 days',  -- 开始时间
    CURRENT_DATE,                      -- 结束时间
    NULL,                              -- 问题类型（NULL=所有）
    NULL,                              -- 是否可自动修复（NULL=所有）
    100,                               -- 限制数量
    0                                  -- 偏移量
);
```

### 2. 查询可自动修复的订单
```sql
SELECT * FROM get_inconsistent_orders(
    CURRENT_DATE - INTERVAL '7 days',
    CURRENT_DATE,
    NULL,
    true,                              -- 只查询可自动修复的
    100,
    0
);
```

### 3. 查询特定类型的问题
```sql
SELECT * FROM get_inconsistent_orders(
    CURRENT_DATE - INTERVAL '7 days',
    CURRENT_DATE,
    'order_created_no_charge',         -- 订单创建成功但未扣费
    NULL,
    100,
    0
);
```

### 4. 手动更新缓存
```sql
SELECT refresh_consistency_cache();
```

### 5. 查看统计信息
```sql
SELECT * FROM mv_inconsistent_orders_summary 
WHERE check_date >= CURRENT_DATE - INTERVAL '7 days'
ORDER BY check_date DESC;
```

## 问题类型说明
- `order_created_no_charge`: 订单创建成功但未扣费（可自动修复）
- `amount_mismatch`: 金额不匹配（需人工处理）
- `status_mismatch`: 状态不匹配（可自动修复）

## 维护命令
- 手动刷新缓存: `SELECT refresh_consistency_cache();`
- 查看缓存状态: `SELECT COUNT(*), MAX(last_checked_at) FROM order_consistency_cache;`
- 清理旧数据: `DELETE FROM order_consistency_cache WHERE created_at < CURRENT_DATE - INTERVAL '90 days';`

