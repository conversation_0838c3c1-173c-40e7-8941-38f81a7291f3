package handler

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"strings"
	"sync"
	"time"

	"go.uber.org/zap"

	"github.com/your-org/go-kuaidi/internal/adapter"
	"github.com/your-org/go-kuaidi/internal/express"
	"github.com/your-org/go-kuaidi/internal/model"
	"github.com/your-org/go-kuaidi/internal/util"
)

// RealtimePriceHandler 实时查价处理器
// 职责：专门处理实时价格查询，跳过缓存和价格验证
// 特点：高性能、高并发、高稳定性的生产级实现
type RealtimePriceHandler struct {
	providerManager       *adapter.ProviderManager
	expressMappingService express.ExpressMappingService
	expressCompanyService express.ExpressCompanyService        // 新增：快递公司服务
	systemConfigService   adapter.SystemConfigServiceInterface // 系统配置服务
	logger                *zap.Logger

	// 🚀 生产级性能优化配置
	queryTimeout          time.Duration // 查询超时时间
	maxRetries            int           // 最大重试次数
	retryDelay            time.Duration // 重试延迟
	circuitBreakerEnabled bool          // 熔断器启用状态

	// 🔥 熔断器状态（简化实现）
	failureCount          int           // 失败计数
	lastFailureTime       time.Time     // 最后失败时间
	circuitBreakerTimeout time.Duration // 熔断器超时时间

	// 🚀 并发安全保护
	mutex sync.RWMutex // 读写锁保护熔断器状态
}

// NewRealtimePriceHandler 创建实时查价处理器
func NewRealtimePriceHandler(
	providerManager *adapter.ProviderManager,
	expressMappingService express.ExpressMappingService,
	expressCompanyService express.ExpressCompanyService, // 新增参数
	systemConfigService adapter.SystemConfigServiceInterface,
	logger *zap.Logger,
) *RealtimePriceHandler {
	return &RealtimePriceHandler{
		providerManager:       providerManager,
		expressMappingService: expressMappingService,
		expressCompanyService: expressCompanyService, // 新增字段
		systemConfigService:   systemConfigService,
		logger:                logger,

		// 🚀 生产级性能配置
		queryTimeout:          5 * time.Second,        // 查询超时5秒
		maxRetries:            2,                      // 最大重试2次
		retryDelay:            500 * time.Millisecond, // 重试延迟500ms
		circuitBreakerEnabled: true,                   // 启用熔断器
		circuitBreakerTimeout: 30 * time.Second,       // 熔断器超时30秒

		// 初始化熔断器状态
		failureCount:    0,
		lastFailureTime: time.Time{},
	}
}

// QueryRealtimePrice 查询实时价格（纯实时API，无缓存，无验证）
func (h *RealtimePriceHandler) QueryRealtimePrice(ctx context.Context, req *RealtimePriceRequest) (*RealtimePriceResponse, error) {
	start := util.NowBeijing()

	h.logger.Info("开始实时价格查询",
		zap.String("sender_province", req.Sender.Province),
		zap.String("sender_city", req.Sender.City),
		zap.String("receiver_province", req.Receiver.Province),
		zap.String("receiver_city", req.Receiver.City),
		zap.Float64("weight", req.Weight))

	// 🔥 熔断器检查
	if h.circuitBreakerEnabled && h.isCircuitBreakerOpen() {
		h.logger.Warn("熔断器开启，拒绝请求",
			zap.Int("failure_count", h.failureCount),
			zap.Time("last_failure_time", h.lastFailureTime))
		return &RealtimePriceResponse{
			Success: false,
			Code:    503,
			Message: "服务暂时不可用，请稍后重试",
		}, nil
	}

	// 验证请求参数
	if err := h.validateRealtimePriceRequest(req); err != nil {
		h.logger.Warn("实时查价参数验证失败", zap.Error(err))
		return &RealtimePriceResponse{
			Success: false,
			Code:    400,
			Message: fmt.Sprintf("参数验证失败: %s", err.Error()),
		}, nil
	}

	// 转换为内部价格请求格式
	internalReq := h.convertToInternalPriceRequest(req)

	// 🚀 带超时控制的实时API查询
	timeoutCtx, cancel := context.WithTimeout(ctx, h.queryTimeout)
	defer cancel()

	allPrices, errors, err := h.queryAllProvidersWithRetry(timeoutCtx, internalReq)
	if err != nil {
		// 🔥 记录失败并更新熔断器状态
		h.recordFailure()

		h.logger.Error("实时价格查询失败",
			zap.Error(err),
			zap.Duration("duration", util.NowBeijing().Sub(start)))
		return &RealtimePriceResponse{
			Success: false,
			Code:    500,
			Message: fmt.Sprintf("价格查询失败: %s", err.Error()),
		}, nil
	}

	// 🚀 记录成功并重置熔断器状态
	h.recordSuccess()

	// 转换为响应格式
	response := h.convertToRealtimePriceListResponse(allPrices, errors, start, req)

	h.logger.Info("实时价格查询成功",
		zap.Int("total_prices", len(allPrices)),
		zap.Int("success_providers", len(allPrices)),
		zap.Int("failed_providers", len(errors)),
		zap.Duration("duration", util.NowBeijing().Sub(start)))

	return response, nil
}

// validateRealtimePriceRequest 验证实时查价请求参数（生产级验证）
func (h *RealtimePriceHandler) validateRealtimePriceRequest(req *RealtimePriceRequest) error {
	// 验证寄件人信息
	if err := h.validateAddressInfo(&req.Sender, "寄件人"); err != nil {
		return err
	}

	// 验证收件人信息
	if err := h.validateAddressInfo(&req.Receiver, "收件人"); err != nil {
		return err
	}

	// 验证包裹信息
	if err := h.validatePackageInfo(req); err != nil {
		return err
	}

	return nil
}

// validateAddressInfo 验证地址信息（生产级验证）
func (h *RealtimePriceHandler) validateAddressInfo(addr *RealtimeAddressInfo, role string) error {
	// 验证姓名
	if strings.TrimSpace(addr.Name) == "" {
		return fmt.Errorf("%s姓名不能为空", role)
	}
	if len([]rune(addr.Name)) > 50 {
		return fmt.Errorf("%s姓名长度不能超过50个字符", role)
	}

	// 验证手机号
	if err := h.validateMobile(addr.Mobile, role); err != nil {
		return err
	}

	// 验证省份
	if strings.TrimSpace(addr.Province) == "" {
		return fmt.Errorf("%s省份不能为空", role)
	}
	if !strings.HasSuffix(addr.Province, "省") && !strings.HasSuffix(addr.Province, "市") &&
		!strings.Contains(addr.Province, "自治区") && !strings.Contains(addr.Province, "特别行政区") {
		return fmt.Errorf("%s省份格式不正确，应包含'省'、'市'、'自治区'或'特别行政区'", role)
	}

	// 验证城市
	if strings.TrimSpace(addr.City) == "" {
		return fmt.Errorf("%s城市不能为空", role)
	}

	// 验证区县
	if strings.TrimSpace(addr.District) == "" {
		return fmt.Errorf("%s区县不能为空", role)
	}

	// 验证详细地址
	if strings.TrimSpace(addr.Address) == "" {
		return fmt.Errorf("%s详细地址不能为空", role)
	}
	if len([]rune(addr.Address)) < 2 {
		return fmt.Errorf("%s详细地址过于简单，请提供更详细的地址信息", role)
	}
	if len([]rune(addr.Address)) > 200 {
		return fmt.Errorf("%s详细地址长度不能超过200个字符", role)
	}

	return nil
}

// validateMobile 验证联系方式（支持手机号和固定电话）
func (h *RealtimePriceHandler) validateMobile(mobile, role string) error {
	mobile = strings.TrimSpace(mobile)
	if mobile == "" {
		return fmt.Errorf("%s联系方式不能为空", role)
	}

	// 🔥 修复：使用生产级电话验证器，支持多种格式
	validator := util.GetPhoneValidator()
	result := validator.ValidatePhone(mobile)

	if !result.IsValid {
		return fmt.Errorf("%s联系方式格式不正确，请提供有效的手机号（如：13800138000）或固定电话（如：010-12345678 或 28698723）", role)
	}

	return nil
}

// validatePackageInfo 验证包裹信息（生产级验证）
func (h *RealtimePriceHandler) validatePackageInfo(req *RealtimePriceRequest) error {
	// 验证重量
	if req.Weight <= 0 {
		return fmt.Errorf("包裹重量必须大于0")
	}
	if req.Weight > 100 {
		return fmt.Errorf("包裹重量不能超过100kg，如需寄递大件请联系客服")
	}

	// 验证体积信息（如果提供）
	if req.Length > 0 || req.Width > 0 || req.Height > 0 {
		if req.Length <= 0 || req.Width <= 0 || req.Height <= 0 {
			return fmt.Errorf("如果提供包裹尺寸，长宽高都必须大于0")
		}
		if req.Length > 200 || req.Width > 200 || req.Height > 200 {
			return fmt.Errorf("包裹单边尺寸不能超过200cm")
		}
	}

	// 验证体积（如果提供）
	if req.Volume > 0 && req.Volume > 1.0 {
		return fmt.Errorf("包裹体积不能超过1立方米")
	}

	// 验证数量
	if req.Quantity < 0 {
		return fmt.Errorf("包裹数量不能为负数")
	}
	if req.Quantity > 100 {
		return fmt.Errorf("包裹数量不能超过100件")
	}

	// 验证物品名称（如果提供）
	if req.GoodsName != "" && len([]rune(req.GoodsName)) > 100 {
		return fmt.Errorf("物品名称长度不能超过100个字符")
	}

	// 验证支付方式
	if req.PayMethod < 0 || req.PayMethod > 2 {
		return fmt.Errorf("支付方式无效，必须为0(寄付)、1(到付)或2(月结)")
	}

	return nil
}

// convertToInternalPriceRequest 转换为内部价格请求格式
func (h *RealtimePriceHandler) convertToInternalPriceRequest(req *RealtimePriceRequest) *model.PriceRequest {
	// 设置默认值
	quantity := req.Quantity
	if quantity <= 0 {
		quantity = 1
	}

	goodsName := req.GoodsName
	if goodsName == "" {
		goodsName = "物品"
	}

	return &model.PriceRequest{
		// 🔥 修复：不硬编码快递类型，让查询逻辑决定查询哪些快递公司
		// 京东专用接口支持 JD（京东快递）和 DBL（德邦快递）
		QueryAllCompanies: true, // 查询所有支持的快递公司（JD和DBL）

		// 寄件人信息
		Sender: model.SenderInfo{
			Name:     req.Sender.Name,
			Mobile:   req.Sender.Mobile,
			Province: req.Sender.Province,
			City:     req.Sender.City,
			District: req.Sender.District,
			Address:  req.Sender.Address,
		},

		// 收件人信息
		Receiver: model.ReceiverInfo{
			Name:     req.Receiver.Name,
			Mobile:   req.Receiver.Mobile,
			Province: req.Receiver.Province,
			City:     req.Receiver.City,
			District: req.Receiver.District,
			Address:  req.Receiver.Address,
		},

		// 包裹信息
		Package: model.PackageInfo{
			Weight:    req.Weight,
			Length:    req.Length,
			Width:     req.Width,
			Height:    req.Height,
			Volume:    req.Volume,
			Quantity:  quantity,
			GoodsName: goodsName,
		},

		// 其他信息
		PayMethod: req.PayMethod,

		// 强制禁用缓存，确保实时查询
		DisableCache: true,
	}
}

// queryAllProvidersPrice 查询所有供应商的实时价格（基于数据库启用状态）
func (h *RealtimePriceHandler) queryAllProvidersPrice(ctx context.Context, req *model.PriceRequest) ([]model.StandardizedPrice, []string, error) {
	// 🔥 修复：使用动态供应商管理，排除菜鸟供应商（菜鸟供应商单独处理）
	// 获取所有启用的供应商，排除菜鸟供应商，包含快递鸟供应商
	providerAdapters := h.providerManager.GetAllExcluding([]string{"cainiao"})
	var providers []string
	for _, adapter := range providerAdapters {
		// 🔧 修复：适配器的Name()方法返回的是供应商代码，不是中文名称
		// 直接使用适配器返回的供应商代码
		providerCode := adapter.Name()
		switch providerCode {
		case "kuaidi100":
			providers = append(providers, "kuaidi100")
		case "yida":
			providers = append(providers, "yida")
		case "yuntong":
			providers = append(providers, "yuntong")
		case "kuaidiniao":
			providers = append(providers, "kuaidiniao")
		default:
			h.logger.Debug("未知的供应商代码，跳过",
				zap.String("provider_code", providerCode))
		}
	}

	var allPrices []model.StandardizedPrice
	var errors []string
	var wg sync.WaitGroup
	var mu sync.Mutex // 用于保护allPrices和errors的并发访问

	// 🚀 从数据库获取启用的快递公司列表
	activeCompanies, err := h.expressCompanyService.GetActiveCompanies(ctx)
	if err != nil {
		h.logger.Error("获取启用快递公司列表失败", zap.Error(err))
		return nil, []string{fmt.Sprintf("获取启用快递公司列表失败: %s", err.Error())}, err
	}

	// 🚀 根据数据库配置并行查询已启用的快递公司价格
	for _, company := range activeCompanies {
		// 🎯 只查询配置为使用专用接口的快递公司
		shouldUse := h.shouldUseRealtimeInterface(company.ID)
		h.logger.Info("实时查价接口快递公司过滤检查",
			zap.String("company_code", company.Code),
			zap.String("company_id", company.ID),
			zap.Bool("should_use_jd_interface", shouldUse))

		if shouldUse {
			wg.Add(1)
			go func(companyCode, companyName string) {
				defer wg.Done()

				h.logger.Info("查询启用快递公司价格",
					zap.String("company_code", companyCode),
					zap.String("company_name", companyName))

				companyPrices, companyErrors := h.queryExpressCompanyPrices(ctx, req, providers, companyCode, companyName)
				mu.Lock()
				allPrices = append(allPrices, companyPrices...)
				errors = append(errors, companyErrors...)
				mu.Unlock()
			}(company.Code, company.Name)
		}
	}

	// 🚀 并行查询菜鸟裹裹价格（如果启用）
	// 检查菜鸟供应商是否启用
	isCainiaoEnabled := h.isCainiaoEnabled()
	if isCainiaoEnabled {
		wg.Add(1)
		go func() {
			defer wg.Done()
			cainiaoProvider := "cainiao" // 菜鸟供应商代码
			cainiaoProviders := []string{cainiaoProvider}

			h.logger.Info("查询菜鸟裹裹价格",
				zap.String("provider", cainiaoProvider))

			// 查询所有快递公司的价格（菜鸟会返回多个快递公司的价格）
			cainiaoPrices, cainiaoErrors := h.queryExpressCompanyPrices(ctx, req, cainiaoProviders, "", "菜鸟裹裹")
			mu.Lock()
			allPrices = append(allPrices, cainiaoPrices...)
			errors = append(errors, cainiaoErrors...)
			mu.Unlock()
		}()
	}

	// 🚀 并行查询快递鸟价格（如果启用）
	// 检查快递鸟供应商是否启用
	isKuaidiNiaoEnabled := h.isKuaidiNiaoEnabled()
	if isKuaidiNiaoEnabled {
		wg.Add(1)
		go func() {
			defer wg.Done()
			kuaidiniaoProvider := "kuaidiniao" // 快递鸟供应商代码
			kuaidiniaoProviders := []string{kuaidiniaoProvider}

			h.logger.Info("查询快递鸟价格",
				zap.String("provider", kuaidiniaoProvider))

			// 查询所有快递公司的价格（快递鸟会返回多个快递公司的价格）
			kuaidiniaoPrices, kuaidiniaoErrors := h.queryExpressCompanyPrices(ctx, req, kuaidiniaoProviders, "", "快递鸟")
			mu.Lock()
			allPrices = append(allPrices, kuaidiniaoPrices...)
			errors = append(errors, kuaidiniaoErrors...)
			mu.Unlock()
		}()
	}

	// 等待所有查询完成
	wg.Wait()

	if len(allPrices) == 0 {
		return nil, errors, fmt.Errorf("所有供应商查询失败: %s", strings.Join(errors, "; "))
	}

	h.logger.Info("多快递公司查询完成",
		zap.Int("total_prices", len(allPrices)),
		zap.Bool("cainiao_enabled", isCainiaoEnabled),
		zap.Bool("kuaidiniao_enabled", isKuaidiNiaoEnabled),
		zap.Int("failed_providers", len(errors)))

	return allPrices, errors, nil
}

// queryExpressCompanyPrices 查询指定快递公司的价格
func (h *RealtimePriceHandler) queryExpressCompanyPrices(ctx context.Context, req *model.PriceRequest, providers []string, expressCode, expressName string) ([]model.StandardizedPrice, []string) {
	var prices []model.StandardizedPrice
	var errors []string

	// 创建针对特定快递公司的查询请求
	companyReq := &model.PriceRequest{
		CustomerOrderNo:   req.CustomerOrderNo,
		ExpressType:       expressCode, // 指定快递公司代码
		ProductType:       req.ProductType,
		Provider:          req.Provider,
		Sender:            req.Sender,
		Receiver:          req.Receiver,
		Package:           req.Package,
		PayMethod:         req.PayMethod,
		IsCompare:         req.IsCompare,
		QueryAllCompanies: false, // 只查询指定快递公司
		DisableCache:      true,  // 🔥 实时查价接口强制禁用缓存
	}

	// 查询所有供应商的价格
	for _, providerName := range providers {
		provider, exists := h.providerManager.Get(providerName)
		if !exists {
			h.logger.Warn("供应商不存在",
				zap.String("provider", providerName),
				zap.String("express", expressName))
			errors = append(errors, fmt.Sprintf("%s(%s): 供应商不存在", providerName, expressName))
			continue
		}

		providerAdapter := provider
		if providerAdapter == nil {
			h.logger.Warn("供应商不支持价格查询",
				zap.String("provider", providerName),
				zap.String("express", expressName))
			errors = append(errors, fmt.Sprintf("%s(%s): 不支持价格查询", providerName, expressName))
			continue
		}

		// 🔥 新增：检查供应商是否支持该快递公司（检查is_supported字段）
		if expressCode != "" {
			isSupported, err := h.checkProviderSupportsCompany(ctx, providerName, expressCode)
			if err != nil {
				h.logger.Warn("检查供应商支持状态失败",
					zap.String("provider", providerName),
					zap.String("express", expressCode),
					zap.Error(err))
				errors = append(errors, fmt.Sprintf("%s(%s): 检查支持状态失败", providerName, expressName))
				continue
			}

			if !isSupported {
				h.logger.Info("供应商不支持该快递公司，跳过查询",
					zap.String("provider", providerName),
					zap.String("express", expressCode),
					zap.String("express_name", expressName))
				errors = append(errors, fmt.Sprintf("%s(%s): 供应商已禁用该快递公司", providerName, expressName))
				continue
			}
		}

		h.logger.Info("查询供应商快递价格",
			zap.String("provider", providerName),
			zap.String("express", expressName))

		providerPrices, err := providerAdapter.QueryPrice(ctx, companyReq)
		if err != nil {
			h.logger.Warn("供应商查询失败",
				zap.String("provider", providerName),
				zap.String("express", expressName),
				zap.Error(err))
			errors = append(errors, fmt.Sprintf("%s(%s): %s", providerName, expressName, err.Error()))
			continue
		}

		if len(providerPrices) > 0 {
			// 添加所有返回的价格选项
			for _, price := range providerPrices {
				prices = append(prices, price)
				h.logger.Info("供应商查询成功",
					zap.String("provider", providerName),
					zap.String("express", expressName),
					zap.String("product", price.ProductName),
					zap.Float64("price", price.Price))
			}
		} else {
			h.logger.Warn("供应商未返回价格",
				zap.String("provider", providerName),
				zap.String("express", expressName))
			errors = append(errors, fmt.Sprintf("%s(%s): 未返回价格", providerName, expressName))
		}
	}

	return prices, errors
}

// convertToRealtimePriceListResponse 转换为实时查价响应列表（标准格式）
func (h *RealtimePriceHandler) convertToRealtimePriceListResponse(allPrices []model.StandardizedPrice, errors []string, queryTime time.Time, originalReq *RealtimePriceRequest) *RealtimePriceResponse {
	// 转换所有价格
	var priceResults []RealtimePriceResult
	for _, price := range allPrices {
		// 🔥 生成增强版下单代码（包含事务一致性信息）
		orderCode, expiresAt, err := h.generateRealtimeEnhancedOrderCode(price.ExpressCode, price.ExpressCode, price.Provider, price.ChannelID, price.ProductCode, price.Price, originalReq)
		if err != nil {
			// 如果生成增强版代码失败，使用旧版本代码作为备选
			orderCode = fmt.Sprintf("%s_%s_%s_%d",
				strings.ToUpper(price.Provider),
				price.ProductCode,
				price.ExpressCode,
				time.Now().Unix())
			expiresAt = util.NowBeijing().Add(30 * time.Minute)
		}

		// 格式化过期时间
		expiresAtStr := expiresAt.Format("2006-01-02 15:04:05")
		if expiresAt.IsZero() {
			// 增强版代码永不过期
			expiresAtStr = "永不过期"
		}

		priceResult := RealtimePriceResult{
			ExpressCode:          price.ExpressCode, // 🔥 直接使用实际的快递公司代码
			ExpressName:          price.ExpressName, // 🔥 直接使用实际的快递公司名称
			ProductCode:          price.ProductCode,
			ProductName:          price.ProductName,
			Price:                price.Price,
			ContinuedWeightPerKg: price.ContinuedWeightPerKg,
			CalcWeight:           price.CalcWeight,
			OrderCode:            orderCode,
			ExpiresAt:            expiresAtStr,

			// 🚀 新增：包含取件预约时间信息
			PickupTimeInfo: price.PickupTimeInfo,
		}
		priceResults = append(priceResults, priceResult)
	}

	return &RealtimePriceResponse{
		Success: true,
		Code:    200,
		Message: "查询成功",
		Data:    priceResults,
	}
}

// 🚀 生产级性能优化方法

// isCircuitBreakerOpen 检查熔断器是否开启（并发安全）
func (h *RealtimePriceHandler) isCircuitBreakerOpen() bool {
	h.mutex.RLock()
	defer h.mutex.RUnlock()

	// 如果失败次数少于阈值，熔断器关闭
	if h.failureCount < 5 {
		return false
	}

	// 如果超过熔断器超时时间，尝试半开状态
	if time.Since(h.lastFailureTime) > h.circuitBreakerTimeout {
		h.logger.Info("熔断器进入半开状态，允许一次尝试")
		return false
	}

	// 熔断器开启
	return true
}

// recordFailure 记录失败并更新熔断器状态（并发安全）
func (h *RealtimePriceHandler) recordFailure() {
	h.mutex.Lock()
	defer h.mutex.Unlock()

	h.failureCount++
	h.lastFailureTime = time.Now()

	h.logger.Warn("记录查询失败",
		zap.Int("failure_count", h.failureCount),
		zap.Time("last_failure_time", h.lastFailureTime))
}

// recordSuccess 记录成功并重置熔断器状态（并发安全）
func (h *RealtimePriceHandler) recordSuccess() {
	h.mutex.Lock()
	defer h.mutex.Unlock()

	if h.failureCount > 0 {
		h.logger.Info("查询成功，重置熔断器状态",
			zap.Int("previous_failure_count", h.failureCount))
		h.failureCount = 0
		h.lastFailureTime = time.Time{}
	}
}

// queryAllProvidersWithRetry 带重试机制的所有供应商价格查询
func (h *RealtimePriceHandler) queryAllProvidersWithRetry(ctx context.Context, req *model.PriceRequest) ([]model.StandardizedPrice, []string, error) {
	var lastErr error

	for attempt := 0; attempt <= h.maxRetries; attempt++ {
		if attempt > 0 {
			// 重试前等待
			select {
			case <-time.After(h.retryDelay):
			case <-ctx.Done():
				return nil, nil, ctx.Err()
			}

			h.logger.Info("重试查询京东快递价格",
				zap.Int("attempt", attempt),
				zap.Int("max_retries", h.maxRetries))
		}

		allPrices, errors, err := h.queryAllProvidersPrice(ctx, req)
		if err == nil {
			if attempt > 0 {
				h.logger.Info("重试查询成功",
					zap.Int("attempt", attempt))
			}
			return allPrices, errors, nil
		}

		lastErr = err
		h.logger.Warn("查询尝试失败",
			zap.Int("attempt", attempt),
			zap.Error(err))

		// 检查是否是不可重试的错误
		if h.isNonRetryableError(err) {
			h.logger.Info("遇到不可重试错误，停止重试", zap.Error(err))
			break
		}

		// 检查上下文是否已取消
		if ctx.Err() != nil {
			return nil, nil, ctx.Err()
		}
	}

	return nil, nil, fmt.Errorf("重试%d次后仍然失败，最后错误: %w", h.maxRetries, lastErr)
}

// isNonRetryableError 判断是否为不可重试的错误
func (h *RealtimePriceHandler) isNonRetryableError(err error) bool {
	if err == nil {
		return false
	}

	errMsg := strings.ToLower(err.Error())

	// 不可重试的错误类型
	nonRetryableErrors := []string{
		"参数", "parameter", "invalid", "格式", "format",
		"权限", "permission", "unauthorized", "forbidden",
		"不支持", "unsupported", "not supported",
	}

	for _, nonRetryable := range nonRetryableErrors {
		if strings.Contains(errMsg, nonRetryable) {
			return true
		}
	}

	return false
}

// isCainiaoEnabled 检查菜鸟供应商是否启用
func (h *RealtimePriceHandler) isCainiaoEnabled() bool {
	// 从系统配置中获取菜鸟供应商启用状态
	// 配置键格式：provider.cainiao_enabled
	enabled := h.systemConfigService.GetConfigAsBoolWithDefault("provider.cainiao_enabled", false)

	// 记录日志
	h.logger.Debug("检查菜鸟供应商启用状态", zap.Bool("enabled", enabled))

	return enabled
}

// isKuaidiNiaoEnabled 检查快递鸟供应商是否启用
func (h *RealtimePriceHandler) isKuaidiNiaoEnabled() bool {
	// 从系统配置中获取快递鸟供应商启用状态
	// 配置键格式：provider.kuaidiniao_enabled
	enabled := h.systemConfigService.GetConfigAsBoolWithDefault("provider.kuaidiniao_enabled", false)

	// 记录日志
	h.logger.Debug("检查快递鸟供应商启用状态", zap.Bool("enabled", enabled))

	return enabled
}

// checkProviderSupportsCompany 检查供应商是否支持指定快递公司（检查is_supported字段）
func (h *RealtimePriceHandler) checkProviderSupportsCompany(ctx context.Context, providerCode, companyCode string) (bool, error) {
	// 获取快递公司信息
	company, err := h.expressCompanyService.GetCompanyByCode(ctx, companyCode)
	if err != nil {
		h.logger.Debug("获取快递公司信息失败",
			zap.String("company_code", companyCode),
			zap.Error(err))
		return false, fmt.Errorf("获取快递公司信息失败: %w", err)
	}

	// 获取供应商信息
	provider, err := h.expressCompanyService.GetProviderByCode(ctx, providerCode)
	if err != nil {
		h.logger.Debug("获取供应商信息失败",
			zap.String("provider_code", providerCode),
			zap.Error(err))
		return false, fmt.Errorf("获取供应商信息失败: %w", err)
	}

	// 获取映射关系
	mapping, err := h.expressCompanyService.GetMapping(ctx, company.ID, provider.ID)
	if err != nil {
		h.logger.Debug("获取映射关系失败",
			zap.String("company_code", companyCode),
			zap.String("provider_code", providerCode),
			zap.Error(err))
		return false, nil // 没有映射关系视为不支持
	}

	h.logger.Debug("检查供应商支持状态",
		zap.String("company_code", companyCode),
		zap.String("provider_code", providerCode),
		zap.Bool("is_supported", mapping.IsSupported))

	return mapping.IsSupported, nil
}

// generateRealtimeEnhancedOrderCode 生成实时查价增强版下单代码
func (h *RealtimePriceHandler) generateRealtimeEnhancedOrderCode(standardCode, originalCode, provider, channelID, productCode string, _ float64, originalReq *RealtimePriceRequest) (string, time.Time, error) {
	// 🔥 移除价格锁定机制：不再生成唯一ID和过期时间
	// 🔥 下单代码永久有效，不再有过期时间限制

	// 创建增强版代码结构（仅包含路由信息）
	enhancedCode := EnhancedOrderCode{
		StandardCode: standardCode,
		OriginalCode: originalCode,
		Provider:     provider,
		ChannelID:    channelID,
		ProductCode:  productCode,
		// 🔥 移除价格锁定：删除 LockedPrice、CreatedAt、ExpiresAt、CodeID 字段
		OriginalRequest: originalReq, // 存储原始查价请求用于实时价格验证
	}

	// 序列化为JSON
	jsonData, err := json.Marshal(enhancedCode)
	if err != nil {
		return "", time.Time{}, fmt.Errorf("序列化增强代码失败: %v", err)
	}

	// 🔥 移除加密机制：直接使用Base64编码，简化处理
	encodedCode := base64.StdEncoding.EncodeToString(jsonData)

	// 🔥 移除过期时间：返回零值时间，表示永不过期
	return "ENHANCED_ORDER_CODE_" + encodedCode, time.Time{}, nil
}

// shouldUseRealtimeInterface 检查快递公司是否应该使用实时查价接口
func (h *RealtimePriceHandler) shouldUseRealtimeInterface(companyID string) bool {
	// 获取快递公司信息
	ctx := context.Background()
	company, err := h.expressCompanyService.GetCompanyByID(ctx, companyID)
	if err != nil {
		h.logger.Debug("获取快递公司信息失败，默认不使用专用接口",
			zap.String("company_id", companyID),
			zap.Error(err))
		return false
	}

	// 🎯 京东专用接口完全基于数据库配置：
	// 只包含 interface_type = "dedicated" 的快递公司

	// 再检查是否配置为dedicated接口
	configs, err := h.expressCompanyService.GetConfigsByCompany(ctx, companyID)
	if err != nil {
		h.logger.Debug("获取快递公司配置失败，默认不使用专用接口",
			zap.String("company_id", companyID),
			zap.Error(err))
		return false
	}

	// 查找interface_type配置
	for _, config := range configs {
		if config.ConfigKey == "interface_type" {
			isDedicated := config.ConfigValue == "dedicated"
			h.logger.Debug("快递公司接口类型检查",
				zap.String("company_code", company.Code),
				zap.String("interface_type", config.ConfigValue),
				zap.Bool("use_jd_interface", isDedicated))
			return isDedicated
		}
	}

	// 默认不使用专用接口
	h.logger.Debug("快递公司无interface_type配置，默认不使用专用接口",
		zap.String("company_code", company.Code))
	return false
}
