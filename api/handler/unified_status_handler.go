package handler

import (
	"fmt"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/your-org/go-kuaidi/internal/errors"
	"github.com/your-org/go-kuaidi/internal/express"
	"go.uber.org/zap"
)

// UnifiedStatusHandler 统一状态管理处理器
type UnifiedStatusHandler struct {
	statusService express.UnifiedStatusService
	logger        *zap.Logger
}

// NewUnifiedStatusHandler 创建统一状态管理处理器
func NewUnifiedStatusHandler(statusService express.UnifiedStatusService, logger *zap.Logger) *UnifiedStatusHandler {
	return &UnifiedStatusHandler{
		statusService: statusService,
		logger:        logger,
	}
}

// StatusControlRequest 状态控制请求
type StatusControlRequest struct {
	Reason string `json:"reason" binding:"required"`
}

// GetStatusOverview 获取状态总览
func (h *UnifiedStatusHandler) GetStatusOverview(c *gin.Context) {
	requestID := getRequestID(c)

	overview, err := h.statusService.GetStatusOverview(c.Request.Context())
	if err != nil {
		h.logger.Error("获取状态总览失败",
			zap.String("request_id", requestID),
			zap.Error(err))

		businessErr := errors.NewBusinessError(errors.ErrCodeInternal, err.Error())
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    200,
		"message": "获取成功",
		"data":    overview,
	})
}

// GetProviderStatuses 获取供应商状态列表
func (h *UnifiedStatusHandler) GetProviderStatuses(c *gin.Context) {
	requestID := getRequestID(c)

	statuses, err := h.statusService.GetProviderStatuses(c.Request.Context())
	if err != nil {
		h.logger.Error("获取供应商状态列表失败",
			zap.String("request_id", requestID),
			zap.Error(err))

		businessErr := errors.NewBusinessError(errors.ErrCodeInternal, err.Error())
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    200,
		"message": "获取成功",
		"data":    statuses,
	})
}

// GetCompanyStatuses 获取快递公司状态列表
func (h *UnifiedStatusHandler) GetCompanyStatuses(c *gin.Context) {
	requestID := getRequestID(c)

	statuses, err := h.statusService.GetCompanyStatuses(c.Request.Context())
	if err != nil {
		h.logger.Error("获取快递公司状态列表失败",
			zap.String("request_id", requestID),
			zap.Error(err))

		businessErr := errors.NewBusinessError(errors.ErrCodeInternal, err.Error())
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    200,
		"message": "获取成功",
		"data":    statuses,
	})
}

// GetMappingStatuses 获取映射关系状态列表
func (h *UnifiedStatusHandler) GetMappingStatuses(c *gin.Context) {
	requestID := getRequestID(c)

	statuses, err := h.statusService.GetMappingStatuses(c.Request.Context())
	if err != nil {
		h.logger.Error("获取映射关系状态列表失败",
			zap.String("request_id", requestID),
			zap.Error(err))

		businessErr := errors.NewBusinessError(errors.ErrCodeInternal, err.Error())
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    200,
		"message": "获取成功",
		"data":    statuses,
	})
}

// EnableProvider 启用供应商
func (h *UnifiedStatusHandler) EnableProvider(c *gin.Context) {
	h.updateProviderStatus(c, true)
}

// DisableProvider 禁用供应商
func (h *UnifiedStatusHandler) DisableProvider(c *gin.Context) {
	h.updateProviderStatus(c, false)
}

// updateProviderStatus 更新供应商状态
func (h *UnifiedStatusHandler) updateProviderStatus(c *gin.Context, enable bool) {
	requestID := getRequestID(c)
	providerCode := c.Param("code")

	if providerCode == "" {
		businessErr := errors.NewBusinessError(errors.ErrCodeInvalidRequest, "供应商代码不能为空")
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	var req StatusControlRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		businessErr := errors.NewBusinessError(errors.ErrCodeInvalidRequest, "请求参数无效")
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	// 获取操作者ID
	operatorID, exists := c.Get("admin_user_id")
	if !exists {
		businessErr := errors.NewBusinessError(errors.ErrCodeUnauthorized, "用户未认证")
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	var err error
	if enable {
		err = h.statusService.EnableProvider(c.Request.Context(), providerCode, operatorID.(string), req.Reason)
	} else {
		err = h.statusService.DisableProvider(c.Request.Context(), providerCode, operatorID.(string), req.Reason)
	}

	if err != nil {
		h.logger.Error("更新供应商状态失败",
			zap.String("request_id", requestID),
			zap.String("provider_code", providerCode),
			zap.Bool("enable", enable),
			zap.Error(err))

		businessErr := errors.NewBusinessError(errors.ErrCodeInternal, err.Error())
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	action := "禁用"
	if enable {
		action = "启用"
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    200,
		"message": fmt.Sprintf("供应商%s成功", action),
	})
}

// EnableCompany 启用快递公司
func (h *UnifiedStatusHandler) EnableCompany(c *gin.Context) {
	h.updateCompanyStatus(c, true)
}

// DisableCompany 禁用快递公司
func (h *UnifiedStatusHandler) DisableCompany(c *gin.Context) {
	h.updateCompanyStatus(c, false)
}

// updateCompanyStatus 更新快递公司状态
func (h *UnifiedStatusHandler) updateCompanyStatus(c *gin.Context, enable bool) {
	requestID := getRequestID(c)
	companyCode := c.Param("code")

	if companyCode == "" {
		businessErr := errors.NewBusinessError(errors.ErrCodeInvalidRequest, "快递公司代码不能为空")
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	var req StatusControlRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		businessErr := errors.NewBusinessError(errors.ErrCodeInvalidRequest, "请求参数无效")
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	// 获取操作者ID
	operatorID, exists := c.Get("admin_user_id")
	if !exists {
		businessErr := errors.NewBusinessError(errors.ErrCodeUnauthorized, "用户未认证")
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	var err error
	if enable {
		err = h.statusService.EnableCompany(c.Request.Context(), companyCode, operatorID.(string), req.Reason)
	} else {
		err = h.statusService.DisableCompany(c.Request.Context(), companyCode, operatorID.(string), req.Reason)
	}

	if err != nil {
		h.logger.Error("更新快递公司状态失败",
			zap.String("request_id", requestID),
			zap.String("company_code", companyCode),
			zap.Bool("enable", enable),
			zap.Error(err))

		businessErr := errors.NewBusinessError(errors.ErrCodeInternal, err.Error())
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	action := "禁用"
	if enable {
		action = "启用"
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    200,
		"message": fmt.Sprintf("快递公司%s成功", action),
	})
}

// EnableMapping 启用映射关系
func (h *UnifiedStatusHandler) EnableMapping(c *gin.Context) {
	h.updateMappingStatus(c, true)
}

// DisableMapping 禁用映射关系
func (h *UnifiedStatusHandler) DisableMapping(c *gin.Context) {
	h.updateMappingStatus(c, false)
}

// updateMappingStatus 更新映射关系状态
func (h *UnifiedStatusHandler) updateMappingStatus(c *gin.Context, enable bool) {
	requestID := getRequestID(c)
	mappingID := c.Param("id")

	if mappingID == "" {
		businessErr := errors.NewBusinessError(errors.ErrCodeInvalidRequest, "映射关系ID不能为空")
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	var req StatusControlRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		businessErr := errors.NewBusinessError(errors.ErrCodeInvalidRequest, "请求参数无效")
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	// 获取操作者ID
	operatorID, exists := c.Get("admin_user_id")
	if !exists {
		businessErr := errors.NewBusinessError(errors.ErrCodeUnauthorized, "用户未认证")
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	var err error
	if enable {
		err = h.statusService.EnableMapping(c.Request.Context(), mappingID, operatorID.(string), req.Reason)
	} else {
		err = h.statusService.DisableMapping(c.Request.Context(), mappingID, operatorID.(string), req.Reason)
	}

	if err != nil {
		h.logger.Error("更新映射关系状态失败",
			zap.String("request_id", requestID),
			zap.String("mapping_id", mappingID),
			zap.Bool("enable", enable),
			zap.Error(err))

		businessErr := errors.NewBusinessError(errors.ErrCodeInternal, err.Error())
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	action := "禁用"
	if enable {
		action = "启用"
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    200,
		"message": fmt.Sprintf("映射关系%s成功", action),
	})
}

// BatchUpdateStatus 批量更新状态
func (h *UnifiedStatusHandler) BatchUpdateStatus(c *gin.Context) {
	requestID := getRequestID(c)

	var req express.BatchStatusRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		businessErr := errors.NewBusinessError(errors.ErrCodeInvalidRequest, "请求参数无效")
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	// 获取操作者ID
	operatorID, exists := c.Get("admin_user_id")
	if !exists {
		businessErr := errors.NewBusinessError(errors.ErrCodeUnauthorized, "用户未认证")
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	response, err := h.statusService.BatchUpdateStatus(c.Request.Context(), req, operatorID.(string))
	if err != nil {
		h.logger.Error("批量更新状态失败",
			zap.String("request_id", requestID),
			zap.Any("request", req),
			zap.Error(err))

		businessErr := errors.NewBusinessError(errors.ErrCodeInternal, err.Error())
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    200,
		"message": "批量操作完成",
		"data":    response,
	})
}

// EnableAllServices 启用所有服务
func (h *UnifiedStatusHandler) EnableAllServices(c *gin.Context) {
	requestID := getRequestID(c)

	var req StatusControlRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		businessErr := errors.NewBusinessError(errors.ErrCodeInvalidRequest, "请求参数无效")
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	// 获取操作者ID
	operatorID, exists := c.Get("admin_user_id")
	if !exists {
		businessErr := errors.NewBusinessError(errors.ErrCodeUnauthorized, "用户未认证")
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	err := h.statusService.EnableAllServices(c.Request.Context(), operatorID.(string), req.Reason)
	if err != nil {
		h.logger.Error("启用所有服务失败",
			zap.String("request_id", requestID),
			zap.Error(err))

		businessErr := errors.NewBusinessError(errors.ErrCodeInternal, err.Error())
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    200,
		"message": "启用所有服务成功",
	})
}

// DisableAllServices 禁用所有服务
func (h *UnifiedStatusHandler) DisableAllServices(c *gin.Context) {
	requestID := getRequestID(c)

	var req StatusControlRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		businessErr := errors.NewBusinessError(errors.ErrCodeInvalidRequest, "请求参数无效")
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	// 获取操作者ID
	operatorID, exists := c.Get("admin_user_id")
	if !exists {
		businessErr := errors.NewBusinessError(errors.ErrCodeUnauthorized, "用户未认证")
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	err := h.statusService.DisableAllServices(c.Request.Context(), operatorID.(string), req.Reason)
	if err != nil {
		h.logger.Error("禁用所有服务失败",
			zap.String("request_id", requestID),
			zap.Error(err))

		businessErr := errors.NewBusinessError(errors.ErrCodeInternal, err.Error())
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    200,
		"message": "禁用所有服务成功",
	})
}

// SetMaintenanceMode 设置维护模式
func (h *UnifiedStatusHandler) SetMaintenanceMode(c *gin.Context) {
	requestID := getRequestID(c)

	enabledStr := c.Query("enabled")
	if enabledStr == "" {
		businessErr := errors.NewBusinessError(errors.ErrCodeInvalidRequest, "enabled参数不能为空")
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	enabled, err := strconv.ParseBool(enabledStr)
	if err != nil {
		businessErr := errors.NewBusinessError(errors.ErrCodeInvalidRequest, "enabled参数格式错误")
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	var req StatusControlRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		businessErr := errors.NewBusinessError(errors.ErrCodeInvalidRequest, "请求参数无效")
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	// 获取操作者ID
	operatorID, exists := c.Get("admin_user_id")
	if !exists {
		businessErr := errors.NewBusinessError(errors.ErrCodeUnauthorized, "用户未认证")
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	err = h.statusService.SetMaintenanceMode(c.Request.Context(), enabled, operatorID.(string), req.Reason)
	if err != nil {
		h.logger.Error("设置维护模式失败",
			zap.String("request_id", requestID),
			zap.Bool("enabled", enabled),
			zap.Error(err))

		businessErr := errors.NewBusinessError(errors.ErrCodeInternal, err.Error())
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	mode := "关闭"
	if enabled {
		mode = "开启"
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    200,
		"message": fmt.Sprintf("维护模式%s成功", mode),
	})
}
