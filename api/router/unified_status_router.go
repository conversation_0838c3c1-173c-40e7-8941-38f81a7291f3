package router

import (
	"github.com/gin-gonic/gin"
	"github.com/your-org/go-kuaidi/api/handler"
	"github.com/your-org/go-kuaidi/api/middleware"
)

// UnifiedStatusRouterConfig 统一状态管理路由配置
type UnifiedStatusRouterConfig struct {
	StatusHandler     *handler.UnifiedStatusHandler
	AuthMiddleware    *middleware.AuthMiddleware
	AdminMiddleware   *middleware.AdminMiddleware
}

// SetupUnifiedStatusRouter 设置统一状态管理路由
func SetupUnifiedStatusRouter(r *gin.Engine, config UnifiedStatusRouterConfig) {
	// 管理员统一状态管理路由组
	adminStatusGroup := r.Group("/api/v1/admin/status")
	adminStatusGroup.Use(config.AuthMiddleware.RequireAuth())
	adminStatusGroup.Use(config.AdminMiddleware.RequireAdmin())
	{
		// 状态查询
		adminStatusGroup.GET("/overview", config.StatusHandler.GetStatusOverview)                // 状态总览
		adminStatusGroup.GET("/providers", config.StatusHandler.GetProviderStatuses)             // 供应商状态列表
		adminStatusGroup.GET("/companies", config.StatusHandler.GetCompanyStatuses)              // 快递公司状态列表
		adminStatusGroup.GET("/mappings", config.StatusHandler.GetMappingStatuses)               // 映射关系状态列表

		// 全局控制
		adminStatusGroup.POST("/global/enable-all", config.StatusHandler.EnableAllServices)      // 启用所有服务
		adminStatusGroup.POST("/global/disable-all", config.StatusHandler.DisableAllServices)    // 禁用所有服务
		adminStatusGroup.POST("/global/maintenance", config.StatusHandler.SetMaintenanceMode)    // 设置维护模式

		// 供应商状态控制
		providers := adminStatusGroup.Group("/providers")
		providers.Use(config.AdminMiddleware.RequireResourcePermission("express_provider", "manage"))
		{
			providers.POST("/:code/enable", config.StatusHandler.EnableProvider)    // 启用供应商
			providers.POST("/:code/disable", config.StatusHandler.DisableProvider)  // 禁用供应商
		}

		// 快递公司状态控制
		companies := adminStatusGroup.Group("/companies")
		companies.Use(config.AdminMiddleware.RequireResourcePermission("express_company", "manage"))
		{
			companies.POST("/:code/enable", config.StatusHandler.EnableCompany)     // 启用快递公司
			companies.POST("/:code/disable", config.StatusHandler.DisableCompany)   // 禁用快递公司
		}

		// 映射关系状态控制
		mappings := adminStatusGroup.Group("/mappings")
		mappings.Use(config.AdminMiddleware.RequireResourcePermission("express_mapping", "manage"))
		{
			mappings.POST("/:id/enable", config.StatusHandler.EnableMapping)        // 启用映射关系
			mappings.POST("/:id/disable", config.StatusHandler.DisableMapping)     // 禁用映射关系
		}

		// 批量操作
		batch := adminStatusGroup.Group("/batch")
		batch.Use(config.AdminMiddleware.RequireResourcePermission("express_status", "manage"))
		{
			batch.POST("/providers", config.StatusHandler.BatchUpdateStatus)        // 批量操作供应商
			batch.POST("/companies", config.StatusHandler.BatchUpdateStatus)        // 批量操作快递公司
			batch.POST("/mappings", config.StatusHandler.BatchUpdateStatus)         // 批量操作映射关系
		}
	}
}
