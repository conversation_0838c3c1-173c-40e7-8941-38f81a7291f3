#!/bin/bash

# 手动重新推送回调脚本
# 用于重新推送快递100的状态更新回调

set -e

echo "🔄 手动重新推送快递100回调"
echo "================================"

# API配置
API_BASE_URL="http://localhost:8081"
CALLBACK_ENDPOINT="/api/v1/callbacks/kuaidi100"

# 从数据库获取的原始回调数据
CALLBACK_DATA="param=%7B%22data%22%3A%7B%22cancelMsg%22%3A%22%22%2C%22orderId%22%3A%22296416979%22%2C%22courierMobile%22%3A%2216621923715%22%2C%22freight%22%3Anull%2C%22weight%22%3Anull%2C%22pollToken%22%3A%22QDAM73ZuZLfF3Gei48aBtXMLNH7%2FPdHThINYEVanrA0%3D%22%2C%22updateTime%22%3A1754008620550%2C%22sentStatus%22%3A2%2C%22userCancel%22%3Afalse%2C%22courierName%22%3A%22%E4%B8%98%E4%BB%81%E5%AE%BD%22%2C%22defPrice%22%3Anull%2C%22thirdOrderId%22%3A%22GK20250731000003054%22%2C%22comment%22%3A%22%22%2C%22payStatus%22%3A0%2C%22status%22%3A2%7D%2C%22kuaidicom%22%3A%22debangkuaidi%22%2C%22kuaidinum%22%3A%22DPK202580385165%22%2C%22message%22%3A%22%E6%88%90%E5%8A%9F%22%2C%22status%22%3A%22200%22%7D&sign=C683267B94FA5D4DBC0102AE3DED3911&taskId=453585FE2B4E9EED71276DF380CF7EFB"

echo "📦 重新推送订单: DPK202580385165"
echo "📊 回调数据包含: status=2 (已揽收), sentStatus=2 (已发送)"

echo ""
echo "🔄 发送回调请求..."

# 发送回调请求
RESPONSE=$(curl -s -w "\nHTTP_CODE:%{http_code}" -X POST "$API_BASE_URL$CALLBACK_ENDPOINT" \
    -H "Content-Type: application/x-www-form-urlencoded; charset=UTF-8" \
    -H "User-Agent: Manual-Repush-Script/1.0" \
    -H "X-Repush-Callback: true" \
    -H "X-Repush-Order: DPK202580385165" \
    -d "$CALLBACK_DATA")

# 提取HTTP状态码
HTTP_CODE=$(echo "$RESPONSE" | grep "HTTP_CODE:" | cut -d: -f2)
RESPONSE_BODY=$(echo "$RESPONSE" | grep -v "HTTP_CODE:")

echo "📤 HTTP状态码: $HTTP_CODE"
echo "📋 响应内容: $RESPONSE_BODY"

if [ "$HTTP_CODE" = "200" ]; then
    echo "✅ 回调发送成功"
    
    echo ""
    echo "⏳ 等待3秒让系统处理..."
    sleep 3
    
    echo ""
    echo "📊 检查订单状态是否更新..."
    
    # 检查订单状态
    NEW_STATUS=$(PGPASSWORD=gjx6ngf4 psql -h ************* -p 5432 -U postgres -d go_kuaidi -t -c "
    SELECT customer_order_no || ' | ' || status || ' | ' || updated_at
    FROM order_records 
    WHERE tracking_no = 'DPK202580385165';
    " | sed 's/^ *//' | grep -v '^$')
    
    if [ -n "$NEW_STATUS" ]; then
        echo "📦 当前订单状态: $NEW_STATUS"
        
        if echo "$NEW_STATUS" | grep -q "picked_up"; then
            echo "🎉 状态更新成功！订单已变更为 picked_up (已揽收)"
        elif echo "$NEW_STATUS" | grep -q "awaiting_pickup"; then
            echo "⚠️  状态仍为 awaiting_pickup，可能需要检查回调处理逻辑"
        else
            echo "📊 状态已更新为其他状态"
        fi
    else
        echo "❌ 无法查询订单状态"
    fi
else
    echo "❌ 回调发送失败，HTTP状态码: $HTTP_CODE"
fi

echo ""
echo "🏁 重新推送完成！"
