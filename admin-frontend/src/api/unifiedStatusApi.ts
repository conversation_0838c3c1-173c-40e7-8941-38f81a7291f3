import http from '@/utils/http'

// 状态总览接口
export interface StatusOverview {
  providers: {
    total: number
    active: number
    inactive: number
  }
  companies: {
    total: number
    active: number
    inactive: number
  }
  mappings: {
    total: number
    supported: number
    unsupported: number
  }
  last_updated: string
}

// 统一状态接口
export interface UnifiedStatus {
  entity_type: 'provider' | 'company' | 'mapping'
  entity_id: string
  entity_code: string
  entity_name: string
  is_active: boolean
  is_maintenance: boolean
  last_changed: string
  changed_by: string
  reason?: string
}

// 批量操作请求接口
export interface BatchStatusRequest {
  entity_type: 'provider' | 'company' | 'mapping'
  entity_ids: string[]
  action: 'enable' | 'disable' | 'maintenance'
  reason: string
}

// 批量操作响应接口
export interface BatchStatusResponse {
  total_count: number
  success_count: number
  failed_count: number
  results: Array<{
    entity_id: string
    success: boolean
    error?: string
  }>
}

// 状态控制请求接口
export interface StatusControlRequest {
  reason: string
}

// API响应接口
export interface ApiResponse<T = any> {
  success: boolean
  code: number
  message: string
  data?: T
}

/**
 * 统一状态管理API
 */
export const unifiedStatusApi = {
  /**
   * 获取状态总览
   */
  async getStatusOverview(): Promise<ApiResponse<StatusOverview>> {
    return http.get({
      url: '/api/v1/admin/status/overview'
    })
  },

  /**
   * 获取供应商状态列表
   */
  async getProviderStatuses(): Promise<ApiResponse<UnifiedStatus[]>> {
    return http.get({
      url: '/api/v1/admin/status/providers'
    })
  },

  /**
   * 获取快递公司状态列表
   */
  async getCompanyStatuses(): Promise<ApiResponse<UnifiedStatus[]>> {
    return http.get({
      url: '/api/v1/admin/status/companies'
    })
  },

  /**
   * 获取映射关系状态列表
   */
  async getMappingStatuses(): Promise<ApiResponse<UnifiedStatus[]>> {
    return http.get({
      url: '/api/v1/admin/status/mappings'
    })
  },

  /**
   * 启用供应商
   */
  async enableProvider(providerCode: string, data: StatusControlRequest): Promise<ApiResponse> {
    return http.post({
      url: `/api/v1/admin/status/providers/${providerCode}/enable`,
      data
    })
  },

  /**
   * 禁用供应商
   */
  async disableProvider(providerCode: string, data: StatusControlRequest): Promise<ApiResponse> {
    return http.post({
      url: `/api/v1/admin/status/providers/${providerCode}/disable`,
      data
    })
  },

  /**
   * 启用快递公司
   */
  async enableCompany(companyCode: string, data: StatusControlRequest): Promise<ApiResponse> {
    return http.post({
      url: `/api/v1/admin/status/companies/${companyCode}/enable`,
      data
    })
  },

  /**
   * 禁用快递公司
   */
  async disableCompany(companyCode: string, data: StatusControlRequest): Promise<ApiResponse> {
    return http.post({
      url: `/api/v1/admin/status/companies/${companyCode}/disable`,
      data
    })
  },

  /**
   * 启用映射关系
   */
  async enableMapping(mappingId: string, data: StatusControlRequest): Promise<ApiResponse> {
    return http.post({
      url: `/api/v1/admin/status/mappings/${mappingId}/enable`,
      data
    })
  },

  /**
   * 禁用映射关系
   */
  async disableMapping(mappingId: string, data: StatusControlRequest): Promise<ApiResponse> {
    return http.post({
      url: `/api/v1/admin/status/mappings/${mappingId}/disable`,
      data
    })
  },

  /**
   * 批量更新状态
   */
  async batchUpdateStatus(data: BatchStatusRequest): Promise<ApiResponse<BatchStatusResponse>> {
    return http.post({
      url: '/api/v1/admin/status/batch/update',
      data
    })
  },

  /**
   * 启用所有服务
   */
  async enableAllServices(data: StatusControlRequest): Promise<ApiResponse> {
    return http.post({
      url: '/api/v1/admin/status/global/enable-all',
      data
    })
  },

  /**
   * 禁用所有服务
   */
  async disableAllServices(data: StatusControlRequest): Promise<ApiResponse> {
    return http.post({
      url: '/api/v1/admin/status/global/disable-all',
      data
    })
  },

  /**
   * 设置维护模式
   */
  async setMaintenanceMode(enabled: boolean, data: StatusControlRequest): Promise<ApiResponse> {
    return http.post({
      url: `/api/v1/admin/status/global/maintenance?enabled=${enabled}`,
      data
    })
  }
}

export default unifiedStatusApi
