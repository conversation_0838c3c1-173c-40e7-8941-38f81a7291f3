<template>
  <div class="mapping-status-table">
    <!-- 操作工具栏 -->
    <div class="table-toolbar">
      <div class="toolbar-left">
        <el-button 
          type="success" 
          :disabled="selectedRows.length === 0"
          @click="batchEnable"
        >
          批量启用 ({{ selectedRows.length }})
        </el-button>
        <el-button 
          type="danger" 
          :disabled="selectedRows.length === 0"
          @click="batchDisable"
        >
          批量禁用 ({{ selectedRows.length }})
        </el-button>
      </div>
      <div class="toolbar-right">
        <el-select
          v-model="filterProvider"
          placeholder="筛选供应商"
          clearable
          style="width: 120px; margin-right: 8px;"
        >
          <el-option
            v-for="provider in providerOptions"
            :key="provider"
            :label="provider"
            :value="provider"
          />
        </el-select>
        <el-select
          v-model="filterStatus"
          placeholder="筛选状态"
          clearable
          style="width: 100px; margin-right: 8px;"
        >
          <el-option label="支持" value="true" />
          <el-option label="不支持" value="false" />
        </el-select>
        <el-input
          v-model="searchKeyword"
          placeholder="搜索映射关系..."
          style="width: 200px; margin-right: 8px;"
          clearable
        />
        <el-button 
          type="primary" 
          :icon="Refresh" 
          @click="$emit('refresh')"
          :loading="loading"
        >
          刷新
        </el-button>
      </div>
    </div>

    <!-- 状态表格 -->
    <el-table
      :data="filteredData"
      :loading="loading"
      @selection-change="handleSelectionChange"
      stripe
      border
    >
      <el-table-column type="selection" width="55" />
      
      <el-table-column label="映射关系" min-width="200">
        <template #default="{ row }">
          <div class="mapping-info">
            <div class="mapping-name">{{ row.entity_name }}</div>
            <div class="mapping-code text-muted">{{ row.entity_code }}</div>
          </div>
        </template>
      </el-table-column>
      
      <el-table-column label="快递公司" width="120">
        <template #default="{ row }">
          <el-tag type="primary">{{ getCompanyCode(row.entity_code) }}</el-tag>
        </template>
      </el-table-column>
      
      <el-table-column label="供应商" width="100">
        <template #default="{ row }">
          <el-tag type="info">{{ getProviderCode(row.entity_code) }}</el-tag>
        </template>
      </el-table-column>
      
      <el-table-column prop="is_active" label="支持状态" width="100">
        <template #default="{ row }">
          <el-tag :type="row.is_active ? 'success' : 'danger'">
            {{ row.is_active ? '支持' : '不支持' }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column label="供应商代码" width="120">
        <template #default="{ row }">
          <el-tag size="small" type="warning">
            {{ getProviderMappingCode(row.entity_code) }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column label="配置信息" width="150">
        <template #default="{ row }">
          <div class="config-info">
            <div class="config-item">
              <span class="label">抛比:</span>
              <span class="value">{{ getMappingConfig(row.entity_code, 'volume_ratio') }}</span>
            </div>
            <div class="config-item">
              <span class="label">限重:</span>
              <span class="value">{{ getMappingConfig(row.entity_code, 'weight_limit') }}</span>
            </div>
          </div>
        </template>
      </el-table-column>
      
      <el-table-column prop="last_changed" label="最后变更" width="160">
        <template #default="{ row }">
          <div class="time-info">
            <div>{{ formatDateTime(row.last_changed) }}</div>
            <div class="text-muted" v-if="row.changed_by">
              by {{ row.changed_by }}
            </div>
          </div>
        </template>
      </el-table-column>
      
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="{ row }">
          <div class="action-buttons">
            <el-button
              v-if="!row.is_active"
              type="success"
              size="small"
              @click="$emit('enable', row.entity_id)"
            >
              启用
            </el-button>
            <el-button
              v-if="row.is_active"
              type="danger"
              size="small"
              @click="$emit('disable', row.entity_id)"
            >
              禁用
            </el-button>
            <el-button
              type="info"
              size="small"
              @click="showMappingDetails(row)"
            >
              详情
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 映射详情对话框 -->
    <el-dialog
      v-model="detailDialog.visible"
      title="映射关系详情"
      width="600px"
    >
      <div class="mapping-details" v-if="detailDialog.data">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="映射关系">
            {{ detailDialog.data.entity_name }}
          </el-descriptions-item>
          <el-descriptions-item label="映射代码">
            {{ detailDialog.data.entity_code }}
          </el-descriptions-item>
          <el-descriptions-item label="快递公司">
            {{ getCompanyCode(detailDialog.data.entity_code) }}
          </el-descriptions-item>
          <el-descriptions-item label="供应商">
            {{ getProviderCode(detailDialog.data.entity_code) }}
          </el-descriptions-item>
          <el-descriptions-item label="支持状态">
            <el-tag :type="detailDialog.data.is_active ? 'success' : 'danger'">
              {{ detailDialog.data.is_active ? '支持' : '不支持' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="供应商代码">
            {{ getProviderMappingCode(detailDialog.data.entity_code) }}
          </el-descriptions-item>
          <el-descriptions-item label="最后变更时间">
            {{ formatDateTime(detailDialog.data.last_changed) }}
          </el-descriptions-item>
          <el-descriptions-item label="变更人">
            {{ detailDialog.data.changed_by || '-' }}
          </el-descriptions-item>
        </el-descriptions>
        
        <div class="config-details" style="margin-top: 20px;">
          <h4>配置详情</h4>
          <el-descriptions :column="1" border size="small">
            <el-descriptions-item label="体积重量比">
              {{ getMappingConfig(detailDialog.data.entity_code, 'volume_ratio') }}
            </el-descriptions-item>
            <el-descriptions-item label="重量限制">
              {{ getMappingConfig(detailDialog.data.entity_code, 'weight_limit') }}
            </el-descriptions-item>
            <el-descriptions-item label="尺寸限制">
              {{ getMappingConfig(detailDialog.data.entity_code, 'size_limit') }}
            </el-descriptions-item>
            <el-descriptions-item label="支持服务">
              {{ getMappingConfig(detailDialog.data.entity_code, 'services') }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { Refresh } from '@element-plus/icons-vue'
import type { UnifiedStatus } from '@/api/unifiedStatusApi'

// Props
interface Props {
  data: UnifiedStatus[]
  loading: boolean
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  refresh: []
  enable: [mappingId: string]
  disable: [mappingId: string]
  batchOperation: [entityIds: string[], action: string]
}>()

// 响应式数据
const selectedRows = ref<UnifiedStatus[]>([])
const searchKeyword = ref('')
const filterProvider = ref('')
const filterStatus = ref('')

// 详情对话框
const detailDialog = ref({
  visible: false,
  data: null as UnifiedStatus | null
})

// 供应商选项
const providerOptions = computed(() => {
  const providers = new Set<string>()
  props.data.forEach(item => {
    providers.add(getProviderCode(item.entity_code))
  })
  return Array.from(providers)
})

// 过滤后的数据
const filteredData = computed(() => {
  let filtered = props.data

  // 关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    filtered = filtered.filter(item => 
      item.entity_code.toLowerCase().includes(keyword) ||
      item.entity_name.toLowerCase().includes(keyword)
    )
  }

  // 供应商筛选
  if (filterProvider.value) {
    filtered = filtered.filter(item => 
      getProviderCode(item.entity_code) === filterProvider.value
    )
  }

  // 状态筛选
  if (filterStatus.value) {
    const isActive = filterStatus.value === 'true'
    filtered = filtered.filter(item => item.is_active === isActive)
  }

  return filtered
})

// 处理选择变更
const handleSelectionChange = (selection: UnifiedStatus[]) => {
  selectedRows.value = selection
}

// 批量启用
const batchEnable = () => {
  const entityIds = selectedRows.value.map(row => row.entity_id)
  emit('batchOperation', entityIds, '启用')
}

// 批量禁用
const batchDisable = () => {
  const entityIds = selectedRows.value.map(row => row.entity_id)
  emit('batchOperation', entityIds, '禁用')
}

// 显示映射详情
const showMappingDetails = (row: UnifiedStatus) => {
  detailDialog.value.data = row
  detailDialog.value.visible = true
}

// 从映射代码中提取快递公司代码
const getCompanyCode = (entityCode: string) => {
  return entityCode.split('-')[0] || entityCode
}

// 从映射代码中提取供应商代码
const getProviderCode = (entityCode: string) => {
  return entityCode.split('-')[1] || entityCode
}

// 获取供应商映射代码
const getProviderMappingCode = (entityCode: string) => {
  const companyCode = getCompanyCode(entityCode)
  const providerCode = getProviderCode(entityCode)
  
  // 模拟映射代码
  const mappings: Record<string, Record<string, string>> = {
    'kuaidi100': {
      'STO': 'shentong',
      'YTO': 'yuantong',
      'ZTO': 'zhongtong',
      'YD': 'yunda',
      'JT': 'jtexpress'
    },
    'yuntong': {
      'STO': 'ST',
      'YTO': 'YT',
      'ZTO': 'ZT'
    },
    'yida': {
      'YTO': 'YTO',
      'ZTO': 'ZTO',
      'DBL': 'DOP'
    },
    'cainiao': {
      'YD': 'YUNDA',
      'JT': 'HTKY',
      'JD': 'LE04284890'
    }
  }
  
  return mappings[providerCode]?.[companyCode] || companyCode
}

// 获取映射配置信息
const getMappingConfig = (entityCode: string, configType: string) => {
  const companyCode = getCompanyCode(entityCode)
  
  // 模拟配置数据
  const configs: Record<string, Record<string, string>> = {
    'STO': {
      volume_ratio: '8000',
      weight_limit: '50kg',
      size_limit: '60×40×40cm',
      services: '标准快递'
    },
    'YTO': {
      volume_ratio: '8000',
      weight_limit: '50kg',
      size_limit: '60×40×40cm',
      services: '标准快递'
    },
    'ZTO': {
      volume_ratio: '8000',
      weight_limit: '50kg',
      size_limit: '60×40×40cm',
      services: '标准快递'
    },
    'YD': {
      volume_ratio: '8000',
      weight_limit: '50kg',
      size_limit: '60×40×40cm',
      services: '标准快递'
    },
    'JT': {
      volume_ratio: '8000',
      weight_limit: '50kg',
      size_limit: '60×40×40cm',
      services: '标准快递'
    },
    'JD': {
      volume_ratio: '8000',
      weight_limit: '100kg',
      size_limit: '80×60×60cm',
      services: '京东快递'
    },
    'DBL': {
      volume_ratio: '6000',
      weight_limit: '500kg',
      size_limit: '200×100×100cm',
      services: '德邦快递'
    }
  }
  
  return configs[companyCode]?.[configType] || '-'
}

// 格式化日期时间
const formatDateTime = (dateStr: string) => {
  if (!dateStr) return '-'
  const date = new Date(dateStr)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}
</script>

<style scoped lang="scss">
.mapping-status-table {
  .table-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    
    .toolbar-left {
      .el-button {
        margin-right: 8px;
      }
    }
    
    .toolbar-right {
      display: flex;
      align-items: center;
    }
  }

  .mapping-info {
    .mapping-name {
      font-weight: 500;
    }
    
    .mapping-code {
      font-size: 12px;
      margin-top: 2px;
    }
  }

  .config-info {
    .config-item {
      font-size: 12px;
      margin-bottom: 2px;
      
      .label {
        color: #909399;
        margin-right: 4px;
      }
      
      .value {
        color: #303133;
      }
    }
  }

  .time-info {
    font-size: 12px;
    
    .text-muted {
      color: #909399;
      margin-top: 2px;
    }
  }

  .action-buttons {
    .el-button {
      margin-right: 4px;
    }
  }

  .text-muted {
    color: #909399;
    font-size: 12px;
  }

  .mapping-details {
    .config-details {
      h4 {
        margin-bottom: 12px;
        color: #303133;
      }
    }
  }
}
</style>
