<template>
  <div class="company-status-table">
    <!-- 操作工具栏 -->
    <div class="table-toolbar">
      <div class="toolbar-left">
        <el-button 
          type="success" 
          :disabled="selectedRows.length === 0"
          @click="batchEnable"
        >
          批量启用 ({{ selectedRows.length }})
        </el-button>
        <el-button 
          type="danger" 
          :disabled="selectedRows.length === 0"
          @click="batchDisable"
        >
          批量禁用 ({{ selectedRows.length }})
        </el-button>
      </div>
      <div class="toolbar-right">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索快递公司..."
          style="width: 200px; margin-right: 8px;"
          clearable
        />
        <el-button 
          type="primary" 
          :icon="Refresh" 
          @click="$emit('refresh')"
          :loading="loading"
        >
          刷新
        </el-button>
      </div>
    </div>

    <!-- 状态表格 -->
    <el-table
      :data="filteredData"
      :loading="loading"
      @selection-change="handleSelectionChange"
      stripe
      border
    >
      <el-table-column type="selection" width="55" />
      
      <el-table-column prop="entity_code" label="快递公司代码" width="120">
        <template #default="{ row }">
          <el-tag type="primary">{{ row.entity_code }}</el-tag>
        </template>
      </el-table-column>
      
      <el-table-column prop="entity_name" label="快递公司名称" min-width="150">
        <template #default="{ row }">
          <div class="company-info">
            <div class="name">{{ row.entity_name }}</div>
            <div class="code text-muted">{{ row.entity_code }}</div>
          </div>
        </template>
      </el-table-column>
      
      <el-table-column prop="is_active" label="状态" width="100">
        <template #default="{ row }">
          <el-tag :type="row.is_active ? 'success' : 'danger'">
            {{ row.is_active ? '启用' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column prop="is_maintenance" label="维护模式" width="100">
        <template #default="{ row }">
          <el-tag v-if="row.is_maintenance" type="warning">维护中</el-tag>
          <span v-else class="text-muted">正常</span>
        </template>
      </el-table-column>
      
      <el-table-column label="支持供应商" width="200">
        <template #default="{ row }">
          <div class="provider-tags">
            <el-tag 
              v-for="provider in getCompanyProviders(row.entity_code)"
              :key="provider"
              size="small"
              class="provider-tag"
            >
              {{ provider }}
            </el-tag>
            <span v-if="getCompanyProviders(row.entity_code).length === 0" class="text-muted">
              暂无支持
            </span>
          </div>
        </template>
      </el-table-column>
      
      <el-table-column prop="last_changed" label="最后变更" width="160">
        <template #default="{ row }">
          <div class="time-info">
            <div>{{ formatDateTime(row.last_changed) }}</div>
            <div class="text-muted" v-if="row.changed_by">
              by {{ row.changed_by }}
            </div>
          </div>
        </template>
      </el-table-column>
      
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="{ row }">
          <div class="action-buttons">
            <el-button
              v-if="!row.is_active"
              type="success"
              size="small"
              @click="$emit('enable', row.entity_code)"
            >
              启用
            </el-button>
            <el-button
              v-if="row.is_active"
              type="danger"
              size="small"
              @click="$emit('disable', row.entity_code)"
            >
              禁用
            </el-button>
            <el-button
              type="info"
              size="small"
              @click="showCompanyDetails(row)"
            >
              详情
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 快递公司详情对话框 -->
    <el-dialog
      v-model="detailDialog.visible"
      title="快递公司详情"
      width="700px"
    >
      <div class="company-details" v-if="detailDialog.data">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="公司代码">
            {{ detailDialog.data.entity_code }}
          </el-descriptions-item>
          <el-descriptions-item label="公司名称">
            {{ detailDialog.data.entity_name }}
          </el-descriptions-item>
          <el-descriptions-item label="当前状态">
            <el-tag :type="detailDialog.data.is_active ? 'success' : 'danger'">
              {{ detailDialog.data.is_active ? '启用' : '禁用' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="维护模式">
            <el-tag v-if="detailDialog.data.is_maintenance" type="warning">维护中</el-tag>
            <span v-else>正常</span>
          </el-descriptions-item>
          <el-descriptions-item label="最后变更时间">
            {{ formatDateTime(detailDialog.data.last_changed) }}
          </el-descriptions-item>
          <el-descriptions-item label="变更人">
            {{ detailDialog.data.changed_by || '-' }}
          </el-descriptions-item>
        </el-descriptions>
        
        <div class="provider-mappings" style="margin-top: 20px;">
          <h4>供应商映射关系</h4>
          <el-table :data="getCompanyMappings(detailDialog.data.entity_code)" size="small">
            <el-table-column prop="provider" label="供应商" />
            <el-table-column prop="status" label="状态">
              <template #default="{ row }">
                <el-tag :type="row.status === '支持' ? 'success' : 'danger'" size="small">
                  {{ row.status }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="provider_code" label="供应商代码" />
          </el-table>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { Refresh } from '@element-plus/icons-vue'
import type { UnifiedStatus } from '@/api/unifiedStatusApi'

// Props
interface Props {
  data: UnifiedStatus[]
  loading: boolean
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  refresh: []
  enable: [companyCode: string]
  disable: [companyCode: string]
  batchOperation: [entityIds: string[], action: string]
}>()

// 响应式数据
const selectedRows = ref<UnifiedStatus[]>([])
const searchKeyword = ref('')

// 详情对话框
const detailDialog = ref({
  visible: false,
  data: null as UnifiedStatus | null
})

// 过滤后的数据
const filteredData = computed(() => {
  if (!searchKeyword.value) return props.data
  
  const keyword = searchKeyword.value.toLowerCase()
  return props.data.filter(item => 
    item.entity_code.toLowerCase().includes(keyword) ||
    item.entity_name.toLowerCase().includes(keyword)
  )
})

// 处理选择变更
const handleSelectionChange = (selection: UnifiedStatus[]) => {
  selectedRows.value = selection
}

// 批量启用
const batchEnable = () => {
  const entityIds = selectedRows.value.map(row => row.entity_code)
  emit('batchOperation', entityIds, '启用')
}

// 批量禁用
const batchDisable = () => {
  const entityIds = selectedRows.value.map(row => row.entity_code)
  emit('batchOperation', entityIds, '禁用')
}

// 显示快递公司详情
const showCompanyDetails = (row: UnifiedStatus) => {
  detailDialog.value.data = row
  detailDialog.value.visible = true
}

// 获取快递公司支持的供应商
const getCompanyProviders = (companyCode: string) => {
  // TODO: 从实际数据获取
  const mockProviders: Record<string, string[]> = {
    'STO': ['快递100', '云通'],
    'YTO': ['快递100', '易达'],
    'ZTO': ['快递100', '云通', '易达'],
    'YD': ['快递100', '菜鸟'],
    'JT': ['快递100', '菜鸟'],
    'SF': ['快递100'],
    'JD': ['菜鸟'],
    'DBL': ['易达']
  }
  return mockProviders[companyCode] || []
}

// 获取快递公司映射关系
const getCompanyMappings = (companyCode: string) => {
  const providers = getCompanyProviders(companyCode)
  return providers.map(provider => ({
    provider,
    status: '支持',
    provider_code: getProviderCode(provider, companyCode)
  }))
}

// 获取供应商代码
const getProviderCode = (provider: string, companyCode: string) => {
  const mappings: Record<string, Record<string, string>> = {
    '快递100': {
      'STO': 'shentong',
      'YTO': 'yuantong',
      'ZTO': 'zhongtong',
      'YD': 'yunda',
      'JT': 'jtexpress',
      'SF': 'sf'
    },
    '云通': {
      'STO': 'ST',
      'ZTO': 'ZT',
      'YTO': 'YT'
    },
    '易达': {
      'YTO': 'YTO',
      'ZTO': 'ZTO',
      'DBL': 'DOP'
    },
    '菜鸟': {
      'YD': 'YUNDA',
      'JT': 'HTKY',
      'JD': 'LE04284890'
    }
  }
  return mappings[provider]?.[companyCode] || companyCode
}

// 格式化日期时间
const formatDateTime = (dateStr: string) => {
  if (!dateStr) return '-'
  const date = new Date(dateStr)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}
</script>

<style scoped lang="scss">
.company-status-table {
  .table-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    
    .toolbar-left {
      .el-button {
        margin-right: 8px;
      }
    }
    
    .toolbar-right {
      display: flex;
      align-items: center;
    }
  }

  .company-info {
    .name {
      font-weight: 500;
    }
    
    .code {
      font-size: 12px;
      margin-top: 2px;
    }
  }

  .provider-tags {
    .provider-tag {
      margin-right: 4px;
      margin-bottom: 2px;
    }
  }

  .time-info {
    font-size: 12px;
    
    .text-muted {
      color: #909399;
      margin-top: 2px;
    }
  }

  .action-buttons {
    .el-button {
      margin-right: 4px;
    }
  }

  .text-muted {
    color: #909399;
    font-size: 12px;
  }

  .company-details {
    .provider-mappings {
      h4 {
        margin-bottom: 12px;
        color: #303133;
      }
    }
  }
}
</style>
