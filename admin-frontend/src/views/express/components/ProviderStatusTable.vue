<template>
  <div class="provider-status-table">
    <!-- 操作工具栏 -->
    <div class="table-toolbar">
      <div class="toolbar-left">
        <el-button 
          type="success" 
          :disabled="selectedRows.length === 0"
          @click="batchEnable"
        >
          批量启用 ({{ selectedRows.length }})
        </el-button>
        <el-button 
          type="danger" 
          :disabled="selectedRows.length === 0"
          @click="batchDisable"
        >
          批量禁用 ({{ selectedRows.length }})
        </el-button>
      </div>
      <div class="toolbar-right">
        <el-button 
          type="primary" 
          :icon="Refresh" 
          @click="$emit('refresh')"
          :loading="loading"
        >
          刷新
        </el-button>
      </div>
    </div>

    <!-- 状态表格 -->
    <el-table
      :data="data"
      :loading="loading"
      @selection-change="handleSelectionChange"
      stripe
      border
    >
      <el-table-column type="selection" width="55" />
      
      <el-table-column prop="entity_code" label="供应商代码" width="120">
        <template #default="{ row }">
          <el-tag type="info">{{ row.entity_code }}</el-tag>
        </template>
      </el-table-column>
      
      <el-table-column prop="entity_name" label="供应商名称" min-width="150" />
      
      <el-table-column prop="is_active" label="状态" width="100">
        <template #default="{ row }">
          <el-tag :type="row.is_active ? 'success' : 'danger'">
            {{ row.is_active ? '启用' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column prop="is_maintenance" label="维护模式" width="100">
        <template #default="{ row }">
          <el-tag v-if="row.is_maintenance" type="warning">维护中</el-tag>
          <span v-else class="text-muted">正常</span>
        </template>
      </el-table-column>
      
      <el-table-column prop="last_changed" label="最后变更" width="160">
        <template #default="{ row }">
          <div class="time-info">
            <div>{{ formatDateTime(row.last_changed) }}</div>
            <div class="text-muted" v-if="row.changed_by">
              by {{ row.changed_by }}
            </div>
          </div>
        </template>
      </el-table-column>
      
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="{ row }">
          <div class="action-buttons">
            <el-button
              v-if="!row.is_active"
              type="success"
              size="small"
              @click="$emit('enable', row.entity_code)"
            >
              启用
            </el-button>
            <el-button
              v-if="row.is_active"
              type="danger"
              size="small"
              @click="$emit('disable', row.entity_code)"
            >
              禁用
            </el-button>
            <el-button
              type="info"
              size="small"
              @click="showStatusHistory(row)"
            >
              历史
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 状态历史对话框 -->
    <el-dialog
      v-model="historyDialog.visible"
      title="状态变更历史"
      width="600px"
    >
      <div class="history-content">
        <el-timeline>
          <el-timeline-item
            v-for="(item, index) in historyDialog.data"
            :key="index"
            :timestamp="formatDateTime(item.timestamp)"
            :type="getTimelineType(item.action)"
          >
            <div class="history-item">
              <div class="action">{{ getActionText(item.action) }}</div>
              <div class="operator">操作人: {{ item.operator }}</div>
              <div class="reason" v-if="item.reason">原因: {{ item.reason }}</div>
            </div>
          </el-timeline-item>
        </el-timeline>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { Refresh } from '@element-plus/icons-vue'
import type { UnifiedStatus } from '@/api/unifiedStatusApi'

// Props
interface Props {
  data: UnifiedStatus[]
  loading: boolean
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  refresh: []
  enable: [providerCode: string]
  disable: [providerCode: string]
  batchOperation: [entityIds: string[], action: string]
}>()

// 响应式数据
const selectedRows = ref<UnifiedStatus[]>([])

// 状态历史对话框
const historyDialog = ref({
  visible: false,
  data: [] as any[]
})

// 处理选择变更
const handleSelectionChange = (selection: UnifiedStatus[]) => {
  selectedRows.value = selection
}

// 批量启用
const batchEnable = () => {
  const entityIds = selectedRows.value.map(row => row.entity_code)
  emit('batchOperation', entityIds, '启用')
}

// 批量禁用
const batchDisable = () => {
  const entityIds = selectedRows.value.map(row => row.entity_code)
  emit('batchOperation', entityIds, '禁用')
}

// 显示状态历史
const showStatusHistory = (row: UnifiedStatus) => {
  // TODO: 从API获取状态历史数据
  historyDialog.value.data = [
    {
      timestamp: row.last_changed,
      action: row.is_active ? 'enable' : 'disable',
      operator: row.changed_by,
      reason: '系统操作'
    }
  ]
  historyDialog.value.visible = true
}

// 格式化日期时间
const formatDateTime = (dateStr: string) => {
  if (!dateStr) return '-'
  const date = new Date(dateStr)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

// 获取时间线类型
const getTimelineType = (action: string) => {
  switch (action) {
    case 'enable': return 'success'
    case 'disable': return 'danger'
    case 'maintenance': return 'warning'
    default: return 'info'
  }
}

// 获取操作文本
const getActionText = (action: string) => {
  switch (action) {
    case 'enable': return '启用'
    case 'disable': return '禁用'
    case 'maintenance': return '维护模式'
    default: return '未知操作'
  }
}
</script>

<style scoped lang="scss">
.provider-status-table {
  .table-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    
    .toolbar-left {
      .el-button {
        margin-right: 8px;
      }
    }
  }

  .time-info {
    font-size: 12px;
    
    .text-muted {
      color: #909399;
      margin-top: 2px;
    }
  }

  .action-buttons {
    .el-button {
      margin-right: 4px;
    }
  }

  .text-muted {
    color: #909399;
    font-size: 12px;
  }

  .history-content {
    max-height: 400px;
    overflow-y: auto;
    
    .history-item {
      .action {
        font-weight: 600;
        margin-bottom: 4px;
      }
      
      .operator {
        font-size: 12px;
        color: #606266;
        margin-bottom: 2px;
      }
      
      .reason {
        font-size: 12px;
        color: #909399;
      }
    }
  }
}
</style>
