<template>
  <div class="unified-status-management">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1>统一状态管理</h1>
      <p class="description">统一管理快递公司、供应商和映射关系的启用/禁用状态</p>
    </div>

    <!-- 状态总览卡片 -->
    <div class="overview-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="overview-card">
            <div class="card-content">
              <div class="card-icon provider-icon">
                <el-icon><Connection /></el-icon>
              </div>
              <div class="card-info">
                <h3>供应商</h3>
                <div class="stats">
                  <span class="active">{{ overview.providers.active }} 启用</span>
                  <span class="inactive">{{ overview.providers.inactive }} 禁用</span>
                </div>
                <div class="total">总计: {{ overview.providers.total }}</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="overview-card">
            <div class="card-content">
              <div class="card-icon company-icon">
                <el-icon><Truck /></el-icon>
              </div>
              <div class="card-info">
                <h3>快递公司</h3>
                <div class="stats">
                  <span class="active">{{ overview.companies.active }} 启用</span>
                  <span class="inactive">{{ overview.companies.inactive }} 禁用</span>
                </div>
                <div class="total">总计: {{ overview.companies.total }}</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="overview-card">
            <div class="card-content">
              <div class="card-icon mapping-icon">
                <el-icon><Link /></el-icon>
              </div>
              <div class="card-info">
                <h3>映射关系</h3>
                <div class="stats">
                  <span class="active">{{ overview.mappings.supported }} 支持</span>
                  <span class="inactive">{{ overview.mappings.unsupported }} 不支持</span>
                </div>
                <div class="total">总计: {{ overview.mappings.total }}</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="overview-card">
            <div class="card-content">
              <div class="card-icon global-icon">
                <el-icon><Setting /></el-icon>
              </div>
              <div class="card-info">
                <h3>全局控制</h3>
                <div class="global-actions">
                  <el-button 
                    type="success" 
                    size="small" 
                    @click="enableAllServices"
                    :loading="globalLoading"
                  >
                    启用全部
                  </el-button>
                  <el-button 
                    type="danger" 
                    size="small" 
                    @click="disableAllServices"
                    :loading="globalLoading"
                  >
                    禁用全部
                  </el-button>
                </div>
                <div class="maintenance">
                  <el-button 
                    type="warning" 
                    size="small" 
                    @click="toggleMaintenanceMode"
                    :loading="maintenanceLoading"
                  >
                    {{ maintenanceMode ? '退出维护' : '维护模式' }}
                  </el-button>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 状态管理标签页 -->
    <el-tabs v-model="activeTab" class="status-tabs">
      <!-- 供应商状态管理 -->
      <el-tab-pane label="供应商状态" name="providers">
        <ProviderStatusTable 
          :data="providerStatuses"
          :loading="providersLoading"
          @refresh="loadProviderStatuses"
          @enable="enableProvider"
          @disable="disableProvider"
          @batch-operation="batchUpdateProviders"
        />
      </el-tab-pane>

      <!-- 快递公司状态管理 -->
      <el-tab-pane label="快递公司状态" name="companies">
        <CompanyStatusTable 
          :data="companyStatuses"
          :loading="companiesLoading"
          @refresh="loadCompanyStatuses"
          @enable="enableCompany"
          @disable="disableCompany"
          @batch-operation="batchUpdateCompanies"
        />
      </el-tab-pane>

      <!-- 映射关系状态管理 -->
      <el-tab-pane label="映射关系状态" name="mappings">
        <MappingStatusTable 
          :data="mappingStatuses"
          :loading="mappingsLoading"
          @refresh="loadMappingStatuses"
          @enable="enableMapping"
          @disable="disableMapping"
          @batch-operation="batchUpdateMappings"
        />
      </el-tab-pane>
    </el-tabs>

    <!-- 操作确认对话框 -->
    <el-dialog
      v-model="confirmDialog.visible"
      :title="confirmDialog.title"
      width="400px"
    >
      <div class="confirm-content">
        <p>{{ confirmDialog.message }}</p>
        <el-input
          v-model="confirmDialog.reason"
          type="textarea"
          placeholder="请输入操作原因（必填）"
          :rows="3"
          maxlength="200"
          show-word-limit
        />
      </div>
      <template #footer>
        <el-button @click="confirmDialog.visible = false">取消</el-button>
        <el-button 
          type="primary" 
          @click="executeConfirmedAction"
          :disabled="!confirmDialog.reason.trim()"
          :loading="confirmDialog.loading"
        >
          确认
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Connection, Truck, Link, Setting } from '@element-plus/icons-vue'
import ProviderStatusTable from './components/ProviderStatusTable.vue'
import CompanyStatusTable from './components/CompanyStatusTable.vue'
import MappingStatusTable from './components/MappingStatusTable.vue'
import { unifiedStatusApi } from '@/api/unifiedStatusApi'

// 响应式数据
const activeTab = ref('providers')
const globalLoading = ref(false)
const maintenanceLoading = ref(false)
const maintenanceMode = ref(false)

const providersLoading = ref(false)
const companiesLoading = ref(false)
const mappingsLoading = ref(false)

// 状态总览数据
const overview = reactive({
  providers: { total: 0, active: 0, inactive: 0 },
  companies: { total: 0, active: 0, inactive: 0 },
  mappings: { total: 0, supported: 0, unsupported: 0 },
  lastUpdated: new Date()
})

// 状态数据
const providerStatuses = ref([])
const companyStatuses = ref([])
const mappingStatuses = ref([])

// 确认对话框
const confirmDialog = reactive({
  visible: false,
  title: '',
  message: '',
  reason: '',
  loading: false,
  action: null as (() => Promise<void>) | null
})

// 页面初始化
onMounted(() => {
  loadOverview()
  loadProviderStatuses()
})

// 加载状态总览
const loadOverview = async () => {
  try {
    const response = await unifiedStatusApi.getStatusOverview()
    if (response.success) {
      Object.assign(overview, response.data)
    }
  } catch (error) {
    console.error('加载状态总览失败:', error)
    ElMessage.error('加载状态总览失败')
  }
}

// 加载供应商状态
const loadProviderStatuses = async () => {
  providersLoading.value = true
  try {
    const response = await unifiedStatusApi.getProviderStatuses()
    if (response.success) {
      providerStatuses.value = response.data
    }
  } catch (error) {
    console.error('加载供应商状态失败:', error)
    ElMessage.error('加载供应商状态失败')
  } finally {
    providersLoading.value = false
  }
}

// 加载快递公司状态
const loadCompanyStatuses = async () => {
  companiesLoading.value = true
  try {
    const response = await unifiedStatusApi.getCompanyStatuses()
    if (response.success) {
      companyStatuses.value = response.data
    }
  } catch (error) {
    console.error('加载快递公司状态失败:', error)
    ElMessage.error('加载快递公司状态失败')
  } finally {
    companiesLoading.value = false
  }
}

// 加载映射关系状态
const loadMappingStatuses = async () => {
  mappingsLoading.value = true
  try {
    const response = await unifiedStatusApi.getMappingStatuses()
    if (response.success) {
      mappingStatuses.value = response.data
    }
  } catch (error) {
    console.error('加载映射关系状态失败:', error)
    ElMessage.error('加载映射关系状态失败')
  } finally {
    mappingsLoading.value = false
  }
}

// 显示确认对话框
const showConfirmDialog = (title: string, message: string, action: () => Promise<void>) => {
  confirmDialog.title = title
  confirmDialog.message = message
  confirmDialog.reason = ''
  confirmDialog.action = action
  confirmDialog.visible = true
}

// 执行确认的操作
const executeConfirmedAction = async () => {
  if (!confirmDialog.action || !confirmDialog.reason.trim()) return
  
  confirmDialog.loading = true
  try {
    await confirmDialog.action()
    confirmDialog.visible = false
    ElMessage.success('操作成功')
    // 刷新数据
    loadOverview()
    loadProviderStatuses()
    loadCompanyStatuses()
    loadMappingStatuses()
  } catch (error) {
    console.error('操作失败:', error)
    ElMessage.error('操作失败')
  } finally {
    confirmDialog.loading = false
  }
}

// 全局操作
const enableAllServices = () => {
  showConfirmDialog(
    '启用所有服务',
    '确定要启用所有供应商、快递公司和映射关系吗？',
    async () => {
      await unifiedStatusApi.enableAllServices({ reason: confirmDialog.reason })
    }
  )
}

const disableAllServices = () => {
  showConfirmDialog(
    '禁用所有服务',
    '确定要禁用所有供应商、快递公司和映射关系吗？这将影响所有快递服务！',
    async () => {
      await unifiedStatusApi.disableAllServices({ reason: confirmDialog.reason })
    }
  )
}

const toggleMaintenanceMode = () => {
  const enabled = !maintenanceMode.value
  showConfirmDialog(
    enabled ? '开启维护模式' : '关闭维护模式',
    enabled ? '开启维护模式将禁用所有服务，确定继续吗？' : '确定要关闭维护模式吗？',
    async () => {
      await unifiedStatusApi.setMaintenanceMode(enabled, { reason: confirmDialog.reason })
      maintenanceMode.value = enabled
    }
  )
}

// 供应商操作
const enableProvider = (providerCode: string) => {
  showConfirmDialog(
    '启用供应商',
    `确定要启用供应商 ${providerCode} 吗？`,
    async () => {
      await unifiedStatusApi.enableProvider(providerCode, { reason: confirmDialog.reason })
    }
  )
}

const disableProvider = (providerCode: string) => {
  showConfirmDialog(
    '禁用供应商',
    `确定要禁用供应商 ${providerCode} 吗？`,
    async () => {
      await unifiedStatusApi.disableProvider(providerCode, { reason: confirmDialog.reason })
    }
  )
}

// 快递公司操作
const enableCompany = (companyCode: string) => {
  showConfirmDialog(
    '启用快递公司',
    `确定要启用快递公司 ${companyCode} 吗？`,
    async () => {
      await unifiedStatusApi.enableCompany(companyCode, { reason: confirmDialog.reason })
    }
  )
}

const disableCompany = (companyCode: string) => {
  showConfirmDialog(
    '禁用快递公司',
    `确定要禁用快递公司 ${companyCode} 吗？`,
    async () => {
      await unifiedStatusApi.disableCompany(companyCode, { reason: confirmDialog.reason })
    }
  )
}

// 映射关系操作
const enableMapping = (mappingId: string) => {
  showConfirmDialog(
    '启用映射关系',
    `确定要启用该映射关系吗？`,
    async () => {
      await unifiedStatusApi.enableMapping(mappingId, { reason: confirmDialog.reason })
    }
  )
}

const disableMapping = (mappingId: string) => {
  showConfirmDialog(
    '禁用映射关系',
    `确定要禁用该映射关系吗？`,
    async () => {
      await unifiedStatusApi.disableMapping(mappingId, { reason: confirmDialog.reason })
    }
  )
}

// 批量操作
const batchUpdateProviders = (entityIds: string[], action: string) => {
  showConfirmDialog(
    `批量${action}供应商`,
    `确定要${action} ${entityIds.length} 个供应商吗？`,
    async () => {
      await unifiedStatusApi.batchUpdateStatus({
        entity_type: 'provider',
        entity_ids: entityIds,
        action: action === '启用' ? 'enable' : 'disable',
        reason: confirmDialog.reason
      })
    }
  )
}

const batchUpdateCompanies = (entityIds: string[], action: string) => {
  showConfirmDialog(
    `批量${action}快递公司`,
    `确定要${action} ${entityIds.length} 个快递公司吗？`,
    async () => {
      await unifiedStatusApi.batchUpdateStatus({
        entity_type: 'company',
        entity_ids: entityIds,
        action: action === '启用' ? 'enable' : 'disable',
        reason: confirmDialog.reason
      })
    }
  )
}

const batchUpdateMappings = (entityIds: string[], action: string) => {
  showConfirmDialog(
    `批量${action}映射关系`,
    `确定要${action} ${entityIds.length} 个映射关系吗？`,
    async () => {
      await unifiedStatusApi.batchUpdateStatus({
        entity_type: 'mapping',
        entity_ids: entityIds,
        action: action === '启用' ? 'enable' : 'disable',
        reason: confirmDialog.reason
      })
    }
  )
}
</script>

<style scoped lang="scss">
.unified-status-management {
  padding: 20px;

  .page-header {
    margin-bottom: 24px;

    h1 {
      margin: 0 0 8px 0;
      font-size: 24px;
      font-weight: 600;
      color: #303133;
    }

    .description {
      margin: 0;
      color: #606266;
      font-size: 14px;
    }
  }

  .overview-cards {
    margin-bottom: 24px;

    .overview-card {
      height: 120px;

      .card-content {
        display: flex;
        align-items: center;
        height: 100%;

        .card-icon {
          width: 60px;
          height: 60px;
          border-radius: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 16px;

          .el-icon {
            font-size: 24px;
            color: white;
          }

          &.provider-icon {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          }

          &.company-icon {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
          }

          &.mapping-icon {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
          }

          &.global-icon {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
          }
        }

        .card-info {
          flex: 1;

          h3 {
            margin: 0 0 8px 0;
            font-size: 16px;
            font-weight: 600;
            color: #303133;
          }

          .stats {
            margin-bottom: 4px;

            .active {
              color: #67c23a;
              margin-right: 12px;
            }

            .inactive {
              color: #f56c6c;
            }
          }

          .total {
            font-size: 12px;
            color: #909399;
          }

          .global-actions {
            margin-bottom: 8px;

            .el-button {
              margin-right: 8px;
            }
          }

          .maintenance {
            .el-button {
              width: 100%;
            }
          }
        }
      }
    }
  }

  .status-tabs {
    .el-tabs__content {
      padding-top: 20px;
    }
  }

  .confirm-content {
    p {
      margin-bottom: 16px;
      color: #606266;
    }
  }
}
</style>
