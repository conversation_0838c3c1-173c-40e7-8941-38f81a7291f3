<template>
  <div class="provider-management">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>供应商管理</h2>
      <p class="page-description">管理快递供应商的开关状态、密钥配置和连接测试</p>
    </div>



    <!-- 操作栏 -->
    <div class="action-bar">
      <el-button type="primary" @click="refreshProviders" :loading="loading">
        <el-icon><Refresh /></el-icon>
        刷新状态
      </el-button>
      <el-button type="success" @click="reloadAllProviders" :loading="reloadingAll">
        <el-icon><Connection /></el-icon>
        重载所有供应商
      </el-button>
      <el-button @click="testAllProviders" :loading="testingAll">
        测试所有连接
      </el-button>
      <el-button @click="showMetricsDialog" type="info">
        <el-icon><DataAnalysis /></el-icon>
        查看指标
      </el-button>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="3" animated />
    </div>

    <!-- 供应商列表 -->
    <div v-else class="provider-list">
      <div v-if="providers.length === 0" class="empty-state">
        <el-empty description="暂无供应商数据" />
      </div>
      <el-row v-else :gutter="24">
        <el-col :span="8" v-for="provider in providers" :key="provider.code">
          <el-card class="provider-card" :class="{ 'provider-enabled': provider.enabled }">
            <!-- 卡片头部 -->
            <template #header>
              <div class="card-header">
                <div class="provider-info">
                  <h3>{{ provider.name }}</h3>
                  <span class="provider-code">{{ provider.code }}</span>
                </div>
                <div class="provider-actions">
                  <el-button-group>
                    <el-button
                      type="primary"
                      size="small"
                      @click="reloadProvider(provider)"
                      :loading="provider.reloading"
                      title="热重载配置"
                    >
                      <el-icon><Refresh /></el-icon>
                    </el-button>
                    <el-switch
                      v-model="provider.enabled"
                      @change="handleToggleProvider(provider)"
                      :loading="provider.switching"
                      size="large"
                    />
                  </el-button-group>
                </div>
              </div>
            </template>

            <!-- 卡片内容 -->
            <div class="provider-content">
              <!-- 状态指示器 -->
              <div class="status-indicator">
                <div class="status-tags">
                  <el-tag
                    :type="getStatusType(provider.status)"
                    :effect="provider.enabled ? 'dark' : 'plain'"
                  >
                    {{ getStatusText(provider.status) }}
                  </el-tag>
                  <el-tag :type="provider.has_adapter ? 'success' : 'danger'" size="small">
                    {{ provider.has_adapter ? '适配器已加载' : '适配器未加载' }}
                  </el-tag>
                </div>
                <div class="status-info">
                  <span class="last-test" v-if="provider.last_test_time">
                    最后测试: {{ formatTime(provider.last_test_time) }}
                  </span>
                  <span class="last-reload" v-if="provider.last_reload_time">
                    最后重载: {{ formatTime(provider.last_reload_time) }}
                  </span>
                  <span class="reload-count" v-if="provider.reload_count > 0">
                    重载次数: {{ provider.reload_count }}
                  </span>
                </div>
              </div>

              <!-- 描述 -->
              <p class="provider-description">{{ provider.description }}</p>

              <!-- 配置信息 -->
              <div class="config-info" v-if="provider.enabled">
                <div class="config-header">
                  <span class="config-title">配置信息</span>
                  <el-tag
                    :type="getConfigStatus(provider.config_data).type"
                    size="small"
                  >
                    {{ getConfigStatus(provider.config_data).text }}
                  </el-tag>
                </div>
                <div class="config-item" v-for="(value, key) in getDisplayConfig(provider.config_data)" :key="key">
                  <span class="config-label">{{ getConfigLabel(key) }}:</span>
                  <span class="config-value">{{ formatConfigValue(key, value) }}</span>
                </div>
              </div>

              <!-- 操作按钮 -->
              <div class="provider-actions-bottom">
                <el-button 
                  size="small" 
                  @click="openConfigDialog(provider)"
                  :disabled="!provider.enabled"
                >
                  <el-icon><Setting /></el-icon>
                  配置
                </el-button>
                <el-button 
                  size="small" 
                  type="primary" 
                  @click="testProvider(provider)"
                  :loading="provider.testing"
                  :disabled="!provider.enabled"
                >
                  <el-icon><Connection /></el-icon>
                  测试连接
                </el-button>
              </div>

              <!-- 测试结果 -->
              <div class="test-result" v-if="provider.last_test_result">
                <el-alert
                  :type="provider.last_test_result.success ? 'success' : 'error'"
                  :title="provider.last_test_result.message"
                  :closable="false"
                  show-icon
                >
                  <template v-if="provider.last_test_result.response_time">
                    <p>响应时间: {{ provider.last_test_result.response_time }}ms</p>
                  </template>
                  <template v-if="provider.last_test_result.error_details">
                    <p>错误详情: {{ provider.last_test_result.error_details }}</p>
                  </template>
                </el-alert>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 配置对话框 -->
    <el-dialog
      v-model="configDialogVisible"
      :title="`配置 ${currentProvider?.name}`"
      width="600px"
      @close="resetConfigDialog"
    >
      <el-form
        ref="configFormRef"
        :model="configForm"
        :rules="configRules"
        label-width="120px"
        v-if="currentProvider"
      >
        <!-- 动态配置字段 -->
        <template v-for="field in getConfigFields(currentProvider.code)" :key="field.key">
          <el-form-item :label="field.label" :prop="field.key" :required="field.required">
            <el-input
              v-if="field.type === 'text'"
              v-model="configForm[field.key]"
              :placeholder="field.placeholder"
              :show-password="field.sensitive"
              clearable
            />
            <el-input-number
              v-else-if="field.type === 'number'"
              v-model="configForm[field.key]"
              :min="field.min"
              :max="field.max"
              :placeholder="field.placeholder"
              style="width: 100%"
            />
            <el-input
              v-else-if="field.type === 'textarea'"
              v-model="configForm[field.key]"
              type="textarea"
              :rows="3"
              :placeholder="field.placeholder"
            />
          </el-form-item>
        </template>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="configDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveConfig" :loading="saving">
            保存配置
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 指标对话框 -->
    <el-dialog
      v-model="metricsDialogVisible"
      title="供应商重载指标"
      width="800px"
      :close-on-click-modal="false"
    >
      <div class="metrics-content">
        <!-- 全局指标 -->
        <div class="global-metrics">
          <h3>全局指标</h3>
          <el-row :gutter="16">
            <el-col :span="6">
              <el-statistic title="总重载次数" :value="metrics.total_reloads || 0" />
            </el-col>
            <el-col :span="6">
              <el-statistic title="成功次数" :value="metrics.success_reloads || 0" />
            </el-col>
            <el-col :span="6">
              <el-statistic title="失败次数" :value="metrics.failed_reloads || 0" />
            </el-col>
            <el-col :span="6">
              <el-statistic
                title="成功率"
                :value="getSuccessRate()"
                suffix="%"
                :precision="1"
              />
            </el-col>
          </el-row>
        </div>

        <!-- 供应商指标 -->
        <div class="provider-metrics" v-if="metrics.provider_metrics">
          <h3>供应商指标</h3>
          <el-table :data="getProviderMetricsArray()" style="width: 100%">
            <el-table-column prop="code" label="供应商" width="120" />
            <el-table-column prop="reload_count" label="重载次数" width="100" />
            <el-table-column prop="success_rate" label="成功率" width="100">
              <template #default="{ row }">
                {{ row.success_rate.toFixed(1) }}%
              </template>
            </el-table-column>
            <el-table-column prop="avg_reload_time" label="平均时间" width="120">
              <template #default="{ row }">
                {{ row.avg_reload_time.toFixed(1) }}ms
              </template>
            </el-table-column>
            <el-table-column prop="last_reload" label="最后重载" width="160">
              <template #default="{ row }">
                {{ formatTime(row.last_reload) }}
              </template>
            </el-table-column>
            <el-table-column prop="last_error" label="最后错误" min-width="200">
              <template #default="{ row }">
                <span v-if="row.last_error" class="error-text">{{ row.last_error }}</span>
                <span v-else class="success-text">无</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="metricsDialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="refreshMetrics">刷新指标</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Refresh, Connection, Setting, DataAnalysis } from '@element-plus/icons-vue'
import { SystemConfigApi, type ProviderConfig, type ProviderConfigData, type ProviderTestResult } from '@/api/systemConfigApi'
import { ProviderReloadApi, type ProviderMetrics } from '@/api/providerReloadApi'
import { useTimerManager } from '@/utils/timerManager'

// 响应式数据
const loading = ref(false)
const testingAll = ref(false)
const reloadingAll = ref(false)
const saving = ref(false)
const providers = ref<ProviderConfig[]>([])
const configDialogVisible = ref(false)
const metricsDialogVisible = ref(false)
const currentProvider = ref<ProviderConfig | null>(null)
const configFormRef = ref()
const metrics = ref<ProviderMetrics>({} as ProviderMetrics)

// 配置表单
const configForm = reactive<Record<string, any>>({})

// 配置验证规则
const configRules = reactive<Record<string, any>>({})

// 生命周期
onMounted(() => {
  loadProviders()
})

// 加载供应商列表
const loadProviders = async () => {
  try {
    loading.value = true

    // 同时获取供应商配置和状态
    const [configResponse, statusResponse] = await Promise.all([
      SystemConfigApi.getProviderConfigs(),
      ProviderReloadApi.getProviderStatus()
    ])

    if (configResponse && configResponse.success) {
      const providerData = configResponse.data || []
      const statusData = statusResponse?.success ? statusResponse.data : {}

      providers.value = providerData.map(provider => ({
        ...provider,
        switching: false,
        testing: false,
        reloading: false,
        // 合并状态信息
        has_adapter: statusData[provider.code]?.has_adapter || false,
        last_reload_time: statusData[provider.code]?.last_reload || null,
        reload_count: statusData[provider.code]?.reload_count || 0,
        health_status: statusData[provider.code]?.health_status || 'unknown'
      }))
      ElMessage.success(`加载了 ${providers.value.length} 个供应商`)
    } else {
      console.error('API返回错误:', configResponse?.message || '未知错误')
      ElMessage.error(configResponse?.message || '加载供应商列表失败')
    }
  } catch (error) {
    console.error('加载供应商列表失败:', error)
    ElMessage.error('加载供应商列表失败: ' + (error instanceof Error ? error.message : '未知错误'))
  } finally {
    loading.value = false
  }
}

// 刷新供应商列表
const refreshProviders = () => {
  loadProviders()
}

// 重载单个供应商
const reloadProvider = async (provider: ProviderConfig) => {
  try {
    provider.reloading = true

    const response = await ProviderReloadApi.reloadProvider(provider.code)
    if (response.success) {
      ElMessage.success(`${provider.name} 重载成功`)
      // 刷新状态
      await loadProviders()
    } else {
      ElMessage.error(response.message || '重载失败')
    }
  } catch (error) {
    console.error('重载供应商失败:', error)
    ElMessage.error('重载失败')
  } finally {
    provider.reloading = false
  }
}

// 重载所有供应商
const reloadAllProviders = async () => {
  try {
    reloadingAll.value = true

    const response = await ProviderReloadApi.reloadAllProviders()
    if (response.success) {
      ElMessage.success('所有供应商重载成功')
      // 刷新状态
      await loadProviders()
    } else {
      ElMessage.error(response.message || '重载失败')
    }
  } catch (error) {
    console.error('重载所有供应商失败:', error)
    ElMessage.error('重载失败')
  } finally {
    reloadingAll.value = false
  }
}

// 显示指标对话框
const showMetricsDialog = async () => {
  try {
    await refreshMetrics()
    metricsDialogVisible.value = true
  } catch (error) {
    console.error('获取指标失败:', error)
    ElMessage.error('获取指标失败')
  }
}

// 刷新指标
const refreshMetrics = async () => {
  try {
    const response = await ProviderReloadApi.getProviderMetrics()
    if (response.success) {
      metrics.value = response.data
    } else {
      ElMessage.error(response.message || '获取指标失败')
    }
  } catch (error) {
    console.error('获取指标失败:', error)
    ElMessage.error('获取指标失败')
  }
}

// 计算成功率
const getSuccessRate = () => {
  const total = metrics.value.total_reloads || 0
  const success = metrics.value.success_reloads || 0
  return total > 0 ? (success / total) * 100 : 0
}

// 获取供应商指标数组
const getProviderMetricsArray = () => {
  if (!metrics.value.provider_metrics) return []
  return Object.values(metrics.value.provider_metrics)
}

// 切换供应商状态
const handleToggleProvider = async (provider: ProviderConfig) => {
  try {
    provider.switching = true
    
    const response = await SystemConfigApi.updateProviderStatus(provider.code, provider.enabled)
    if (response.success) {
      ElMessage.success(`${provider.enabled ? '启用' : '禁用'}供应商成功`)
      provider.status = provider.enabled ? 'active' : 'inactive'

      // 如果启用了供应商，重新加载配置以确保数据同步
      if (provider.enabled) {
        await loadProviders()
      }
    } else {
      // 恢复状态
      provider.enabled = !provider.enabled
      ElMessage.error(response.message || '操作失败')
    }
  } catch (error) {
    // 恢复状态
    provider.enabled = !provider.enabled
    console.error('切换供应商状态失败:', error)
    ElMessage.error('操作失败')
  } finally {
    provider.switching = false
  }
}

// 测试单个供应商连接
const testProvider = async (provider: ProviderConfig) => {
  try {
    provider.testing = true
    provider.status = 'testing'
    
    const response = await SystemConfigApi.testProviderConnection(provider.code)
    if (response.success) {
      provider.last_test_result = response.data
      provider.last_test_time = new Date().toISOString()
      provider.status = response.data.success ? 'active' : 'error'
      
      ElMessage.success('连接测试完成')
    } else {
      provider.status = 'error'
      ElMessage.error(response.message || '测试失败')
    }
  } catch (error) {
    provider.status = 'error'
    console.error('测试供应商连接失败:', error)
    ElMessage.error('测试失败')
  } finally {
    provider.testing = false
  }
}

// 测试所有供应商连接
const testAllProviders = async () => {
  try {
    testingAll.value = true
    const enabledProviders = providers.value.filter(p => p.enabled)
    
    if (enabledProviders.length === 0) {
      ElMessage.warning('没有启用的供应商')
      return
    }

    ElMessage.info(`开始测试 ${enabledProviders.length} 个供应商连接...`)
    
    // 并发测试所有启用的供应商
    const testPromises = enabledProviders.map(provider => testProvider(provider))
    await Promise.allSettled(testPromises)
    
    ElMessage.success('所有供应商连接测试完成')
  } catch (error) {
    console.error('批量测试失败:', error)
    ElMessage.error('批量测试失败')
  } finally {
    testingAll.value = false
  }
}

// 工具函数
const getStatusType = (status: string) => {
  switch (status) {
    case 'active': return 'success'
    case 'testing': return 'warning'
    case 'error': return 'danger'
    default: return 'info'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'active': return '正常'
    case 'inactive': return '未启用'
    case 'testing': return '测试中'
    case 'error': return '异常'
    default: return '未知'
  }
}

const formatTime = (time: string) => {
  const date = new Date(time)
  const now = new Date()
  const diff = now.getTime() - date.getTime()

  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))

  if (minutes < 1) return '刚刚'
  if (minutes < 60) return `${minutes}分钟前`
  if (hours < 24) return `${hours}小时前`
  if (days < 7) return `${days}天前`

  return date.toLocaleDateString('zh-CN')
}

const getDisplayConfig = (config: ProviderConfigData) => {
  const display: Record<string, any> = {}

  // 只显示关键的配置信息
  const importantKeys = ['api_key', 'secret', 'private_key', 'customer', 'timeout']

  Object.keys(config).forEach(key => {
    if (importantKeys.includes(key) && config[key]) {
      display[key] = config[key]
    }
  })
  return display
}

const getConfigLabel = (key: string) => {
  const labels: Record<string, string> = {
    api_key: 'API Key',
    secret: 'Secret',
    private_key: 'Private Key',
    customer: '客户编号',
    timeout: '超时时间',
    username: '用户名',
    business_id: '商户ID',
    base_url: 'API地址',
    max_retries: '最大重试',
    rate_limit: '限流'
  }
  return labels[key] || key
}

const formatConfigValue = (key: string, value: any) => {
  if (key === 'timeout') return `${value}秒`
  if (key === 'rate_limit') return `${value}/秒`

  // 敏感信息脱敏显示
  if (['api_key', 'secret', 'private_key'].includes(key)) {
    if (!value) return '未配置'
    const str = String(value)
    if (str.length <= 8) return '****'
    return str.substring(0, 4) + '****' + str.substring(str.length - 4)
  }

  return value || '未配置'
}

// 检查配置完整性
const getConfigStatus = (config: ProviderConfigData) => {
  const requiredKeys = ['api_key', 'secret', 'customer']
  const missingKeys = requiredKeys.filter(key => !config[key])

  if (missingKeys.length === 0) {
    return { type: 'success', text: '配置完整' }
  } else if (missingKeys.length < requiredKeys.length) {
    return { type: 'warning', text: '配置不完整' }
  } else {
    return { type: 'danger', text: '未配置' }
  }
}

// 打开配置对话框
const openConfigDialog = (provider: ProviderConfig) => {
  currentProvider.value = provider

  // 重置表单
  Object.keys(configForm).forEach(key => {
    delete configForm[key]
  })

  // 填充当前配置
  Object.assign(configForm, provider.config_data)

  // 设置验证规则
  setupConfigRules(provider.code)

  configDialogVisible.value = true
}

// 重置配置对话框
const resetConfigDialog = () => {
  currentProvider.value = null
  Object.keys(configForm).forEach(key => {
    delete configForm[key]
  })
  Object.keys(configRules).forEach(key => {
    delete configRules[key]
  })
}

// 获取配置字段定义（基于后端实际配置结构）
const getConfigFields = (providerCode: string) => {
  const fieldConfigs: Record<string, any[]> = {
    kuaidi100: [
      { key: 'api_key', label: 'API Key', type: 'text', required: true, sensitive: true, placeholder: '请输入快递100 API Key' },
      { key: 'secret', label: 'Secret', type: 'text', required: true, sensitive: true, placeholder: '请输入快递100 Secret' },
      { key: 'customer', label: '客户编号', type: 'text', required: true, placeholder: '请输入客户编号' },
      { key: 'base_url', label: 'API地址', type: 'text', required: false, placeholder: '默认: https://poll.kuaidi100.com' },
      { key: 'timeout', label: '超时时间(秒)', type: 'number', required: false, min: 1, max: 60, placeholder: '默认10秒' }
    ],
    yida: [
      { key: 'username', label: '用户名', type: 'text', required: true, placeholder: '请输入易达用户名' },
      { key: 'private_key', label: '私钥', type: 'textarea', required: true, sensitive: true, placeholder: '请输入易达私钥' },
      { key: 'base_url', label: 'API地址', type: 'text', required: false, placeholder: '默认使用官方API地址' },
      { key: 'timeout', label: '超时时间(秒)', type: 'number', required: false, min: 1, max: 60, placeholder: '默认10秒' }
    ],
    yuntong: [
      { key: 'business_id', label: '商户ID', type: 'text', required: true, placeholder: '请输入云通商户ID' },
      { key: 'api_key', label: 'API Key', type: 'text', required: true, sensitive: true, placeholder: '请输入云通API Key' },
      { key: 'base_url', label: 'API地址', type: 'text', required: false, placeholder: '默认使用官方API地址' },
      { key: 'timeout', label: '超时时间(秒)', type: 'number', required: false, min: 1, max: 60, placeholder: '默认10秒' }
    ]
  }

  return fieldConfigs[providerCode] || []
}

// 设置配置验证规则
const setupConfigRules = (providerCode: string) => {
  // 清空现有规则
  Object.keys(configRules).forEach(key => {
    delete configRules[key]
  })

  const fields = getConfigFields(providerCode)
  fields.forEach(field => {
    if (field.required) {
      configRules[field.key] = [
        { required: true, message: `请输入${field.label}`, trigger: 'blur' }
      ]
    }

    if (field.type === 'number') {
      configRules[field.key] = [
        ...(configRules[field.key] || []),
        { type: 'number', message: `${field.label}必须是数字`, trigger: 'blur' }
      ]
    }
  })
}

// 保存配置
const saveConfig = async () => {
  if (!currentProvider.value) return

  try {
    // 验证表单
    await configFormRef.value?.validate()

    saving.value = true

    const response = await SystemConfigApi.updateProviderConfig(
      currentProvider.value.code,
      configForm
    )

    if (response.success) {
      ElMessage.success('配置保存成功')
      configDialogVisible.value = false

      // 重新加载供应商配置以获取最新数据
      await loadProviders()

      // 自动测试连接
      const updatedProvider = providers.value.find(p => p.code === currentProvider.value?.code)
      if (updatedProvider) {
        await testProvider(updatedProvider)
      }
    } else {
      ElMessage.error(response.message || '保存配置失败')
    }
  } catch (error) {
    console.error('保存配置失败:', error)
    if (error !== false) { // 不是表单验证错误
      ElMessage.error('保存配置失败')
    }
  } finally {
    saving.value = false
  }
}

// 页面初始化
onMounted(() => {
  loadProviders()

  // 🔧 使用定时器管理器替代原生定时器
  const { createTimer } = useTimerManager('provider-management')

  // 设置智能定时刷新（每30秒，页面隐藏时暂停）
  createTimer({
    id: 'provider-status-refresh',
    type: 'interval',
    delay: 30000,
    pauseWhenHidden: true,
    callback: async () => {
      // 只在非加载状态时刷新
      if (!loading.value && !reloadingAll.value) {
        console.log('🔄 定时刷新供应商状态')
        await loadProviders()
      } else {
        console.log('⏸️ 跳过定时刷新（正在加载中）')
      }
    }
  })

  console.log('✅ 供应商管理页面初始化完成')
})
</script>

<style scoped lang="scss">
.provider-management {
  padding: 20px;

  .page-header {
    margin-bottom: 24px;

    h2 {
      margin: 0 0 8px 0;
      font-size: 24px;
      font-weight: 600;
      color: #1f2937;
    }

    .page-description {
      margin: 0;
      color: #6b7280;
      font-size: 14px;
    }
  }

  .action-bar {
    margin-bottom: 24px;
    display: flex;
    gap: 12px;
  }

  .provider-list {
    .provider-card {
      margin-bottom: 24px;
      border-radius: 8px;
      transition: all 0.3s ease;

      &:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }

      &.provider-enabled {
        border-color: #10b981;

        :deep(.el-card__header) {
          background: linear-gradient(135deg, #f0fdf4 0%, #ecfdf5 100%);
        }
      }

      :deep(.el-card__header) {
        padding: 16px 20px;
        background: #f9fafb;
        border-bottom: 1px solid #e5e7eb;
      }

      :deep(.el-card__body) {
        padding: 20px;
      }
    }

    .status-indicator {
      margin-bottom: 16px;

      .status-tags {
        display: flex;
        gap: 8px;
        margin-bottom: 8px;
        flex-wrap: wrap;
      }

      .status-info {
        display: flex;
        flex-direction: column;
        gap: 4px;
        font-size: 12px;
        color: #6b7280;

        .last-test, .last-reload, .reload-count {
          display: block;
        }
      }
    }

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .provider-info {
        h3 {
          margin: 0 0 4px 0;
          font-size: 18px;
          font-weight: 600;
          color: #1f2937;
        }

        .provider-code {
          font-size: 12px;
          color: #6b7280;
          background: #f3f4f6;
          padding: 2px 8px;
          border-radius: 4px;
          font-family: 'Monaco', 'Menlo', monospace;
        }
      }
    }

    .provider-content {
      .status-indicator {
        display: flex;
        align-items: center;
        gap: 12px;
        margin-bottom: 16px;

        .last-test {
          font-size: 12px;
          color: #6b7280;
        }
      }

      .provider-description {
        margin: 0 0 16px 0;
        color: #4b5563;
        font-size: 14px;
        line-height: 1.5;
      }

      .config-info {
        background: #f9fafb;
        border-radius: 6px;
        padding: 12px;
        margin-bottom: 16px;

        .config-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 12px;
          padding-bottom: 8px;
          border-bottom: 1px solid #e5e7eb;

          .config-title {
            font-weight: 600;
            color: #374151;
            font-size: 14px;
          }
        }

        .config-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 8px;
          font-size: 13px;

          &:last-child {
            margin-bottom: 0;
          }

          .config-label {
            color: #6b7280;
            font-weight: 500;
          }

          .config-value {
            color: #1f2937;
            font-family: 'Monaco', 'Menlo', monospace;
          }
        }
      }

      .provider-actions-bottom {
        display: flex;
        gap: 8px;
        margin-bottom: 16px;
      }

      .test-result {
        :deep(.el-alert) {
          border-radius: 6px;

          .el-alert__content {
            font-size: 13px;

            p {
              margin: 4px 0 0 0;
              font-size: 12px;
              opacity: 0.8;
            }
          }
        }
      }
    }
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .provider-management {
    .provider-list {
      :deep(.el-col) {
        width: 50% !important;
        max-width: 50% !important;
        flex: 0 0 50% !important;
      }
    }
  }
}

@media (max-width: 768px) {
  .provider-management {
    padding: 16px;

    .provider-list {
      :deep(.el-col) {
        width: 100% !important;
        max-width: 100% !important;
        flex: 0 0 100% !important;
      }
    }

    .action-bar {
      flex-direction: column;

      .el-button {
        width: 100%;
      }
    }
  }
}

// 指标对话框样式
.metrics-content {
  .global-metrics {
    margin-bottom: 24px;

    h3 {
      margin: 0 0 16px 0;
      font-size: 16px;
      font-weight: 600;
      color: #1f2937;
    }
  }

  .provider-metrics {
    h3 {
      margin: 0 0 16px 0;
      font-size: 16px;
      font-weight: 600;
      color: #1f2937;
    }

    .error-text {
      color: #ef4444;
      font-size: 12px;
    }

    .success-text {
      color: #10b981;
      font-size: 12px;
    }
  }
}
</style>
