#!/bin/bash

# 简单的订单列表性能测试脚本
# 解决macOS兼容性问题

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
BASE_URL="http://localhost:8081"
TEST_USERNAME="mywl"
TEST_PASSWORD="NNJJ@178..n"
RESULTS_DIR="test_results"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)

# 创建结果目录
mkdir -p $RESULTS_DIR

echo -e "${BLUE}=== 用户订单列表接口并发性能测试 ===${NC}"
echo "测试时间: $(date)"
echo "基础URL: $BASE_URL"
echo "测试账号: $TEST_USERNAME"
echo "测试范围: 用户订单列表接口 (/api/v1/express/orders)"
echo

# 检查服务器状态
check_server() {
    echo -e "${YELLOW}检查服务器状态...${NC}"
    
    if curl -s --max-time 5 "$BASE_URL/health" > /dev/null; then
        echo -e "${GREEN}服务器运行正常${NC}"
        return 0
    else
        echo -e "${RED}服务器未响应${NC}"
        return 1
    fi
}

# 用户登录获取令牌
login_user() {
    echo -e "${YELLOW}用户登录...${NC}"
    
    local response_file="$RESULTS_DIR/login_response_$TIMESTAMP.json"
    
    # 发送登录请求
    local response=$(curl -s -X POST \
        -H "Content-Type: application/json" \
        -d "{\"username\":\"$TEST_USERNAME\",\"password\":\"$TEST_PASSWORD\"}" \
        "$BASE_URL/api/v1/auth/login")
    
    echo "$response" > "$response_file"
    
    # 检查是否登录成功
    if echo "$response" | grep -q '"success":true'; then
        # 提取access_token
        if command -v jq &> /dev/null; then
            USER_TOKEN=$(echo "$response" | jq -r '.access_token // empty')
        else
            USER_TOKEN=$(echo "$response" | grep -o '"access_token":"[^"]*"' | sed 's/"access_token":"//; s/"//')
        fi
        
        if [[ -n "$USER_TOKEN" ]]; then
            echo -e "${GREEN}用户登录成功${NC}"
            echo "Token: ${USER_TOKEN:0:20}..."
            return 0
        else
            echo -e "${RED}无法解析访问令牌${NC}"
            return 1
        fi
    else
        echo -e "${RED}用户登录失败${NC}"
        echo "$response"
        return 1
    fi
}



# 测试用户订单列表
test_user_orders() {
    echo -e "${YELLOW}测试用户订单列表...${NC}"
    
    if [[ -z "$USER_TOKEN" ]]; then
        echo -e "${RED}用户令牌为空，跳过测试${NC}"
        return 1
    fi
    
    local response_file="$RESULTS_DIR/user_orders_$TIMESTAMP.json"
    
    # 测试基本列表查询
    echo "测试基本列表查询..."
    local start_time=$(date +%s.%N)
    
    local response=$(curl -s -w "HTTP_CODE:%{http_code}\nTIME_TOTAL:%{time_total}\n" \
        -H "Authorization: Bearer $USER_TOKEN" \
        -H "Content-Type: application/json" \
        "$BASE_URL/api/v1/express/orders?page=1&page_size=20")
    
    local end_time=$(date +%s.%N)
    local duration=$(echo "$end_time - $start_time" | bc 2>/dev/null || echo "N/A")
    
    echo "$response" > "$response_file"
    
    # 解析响应
    local http_code=$(echo "$response" | grep "HTTP_CODE:" | sed 's/.*HTTP_CODE://')
    local time_total=$(echo "$response" | grep "TIME_TOTAL:" | sed 's/.*TIME_TOTAL://')
    
    if [[ "$http_code" == "200" ]]; then
        echo -e "${GREEN}✓ 用户订单列表请求成功${NC}"
        echo "  响应时间: ${time_total}s"
        echo "  总耗时: ${duration}s"
        
        # 尝试解析订单数量
        local order_count=""
        if command -v jq &> /dev/null; then
            # 只解析JSON部分，排除HTTP状态码行
            local json_response=$(echo "$response" | sed '/HTTP_CODE:/d; /TIME_TOTAL:/d')
            order_count=$(echo "$json_response" | jq -r '.data.total // "N/A"' 2>/dev/null || echo "N/A")
        fi
        
        if [[ -n "$order_count" && "$order_count" != "N/A" ]]; then
            echo "  订单总数: $order_count"
        fi
        
        echo -e "${GREEN}✓ 订单列表接口正常，准备开始并发测试${NC}"
        return 0
    else
        echo -e "${RED}✗ 用户订单列表请求失败 (HTTP $http_code)${NC}"
        echo "响应内容:"
        echo "$response" | head -c 200
        return 1
    fi
}



# 并发性能测试
run_concurrent_test() {
    echo -e "${YELLOW}=== 并发性能测试 ===${NC}"
    
    if [[ -z "$USER_TOKEN" ]]; then
        echo -e "${RED}用户令牌为空，跳过性能测试${NC}"
        return 1
    fi
    
    local endpoint="$BASE_URL/api/v1/express/orders?page=1&page_size=20"
    local results_file="$RESULTS_DIR/performance_$TIMESTAMP.txt"
    
    echo "测试端点: $endpoint"
    echo "开始并发测试..." | tee "$results_file"
    echo
    
    # 测试不同并发级别
    local concurrent_levels=(1 5 10 20 50)
    local requests_per_level=20
    
    for concurrent in "${concurrent_levels[@]}"; do
        echo -e "${BLUE}=== 并发级别: $concurrent ===${NC}" | tee -a "$results_file"
        
        # 创建临时脚本用于并发测试
        local temp_script="/tmp/concurrent_test_$$.sh"
        cat > "$temp_script" << 'EOF'
#!/bin/bash
USER_TOKEN="$1"
ENDPOINT="$2"
TEST_ID="$3"
RESULTS_FILE="$4"

start_time=$(date +%s.%N)
response=$(curl -s -w "%{http_code}" \
    -H "Authorization: Bearer $USER_TOKEN" \
    -H "Content-Type: application/json" \
    "$ENDPOINT" 2>/dev/null)
end_time=$(date +%s.%N)

duration=$(echo "$end_time - $start_time" | bc 2>/dev/null || echo "0")
http_code=$(echo "$response" | tail -c 4)

echo "TEST_${TEST_ID}: HTTP $http_code, 耗时: ${duration}s" >> "$RESULTS_FILE"
echo "$duration $http_code" # 返回给父进程
EOF
        chmod +x "$temp_script"
        
        local total_time=0
        local success_count=0
        local fail_count=0
        local min_time=999999
        local max_time=0
        
        echo "启动 $concurrent 个并发请求，每个请求 $requests_per_level 次..."
        
        # 并发执行测试
        for batch in $(seq 1 $requests_per_level); do
            local pids=()
            local batch_start=$(date +%s.%N)
            
            # 启动并发请求
            for i in $(seq 1 $concurrent); do
                "$temp_script" "$USER_TOKEN" "$endpoint" "${batch}_${i}" "$results_file" &
                pids+=($!)
            done
            
            # 等待所有请求完成并收集结果
            for pid in "${pids[@]}"; do
                wait "$pid"
            done
            
            # 收集这个批次的结果
            local batch_results=$(grep "TEST_${batch}_" "$results_file" | tail -n $concurrent)
            while IFS= read -r result; do
                if [[ -n "$result" ]]; then
                    local duration=$(echo "$result" | grep -o '[0-9]\+\.[0-9]\+s' | sed 's/s//')
                    local http_code=$(echo "$result" | grep -o 'HTTP [0-9]\+' | sed 's/HTTP //')
                    
                    if [[ "$http_code" == "200" ]]; then
                        success_count=$((success_count + 1))
                        total_time=$(echo "$total_time + $duration" | bc 2>/dev/null || echo "$total_time")
                        
                        # 更新最小最大时间
                        if (( $(echo "$duration < $min_time" | bc -l) )); then
                            min_time=$duration
                        fi
                        if (( $(echo "$duration > $max_time" | bc -l) )); then
                            max_time=$duration
                        fi
                    else
                        fail_count=$((fail_count + 1))
                    fi
                fi
            done <<< "$batch_results"
            
            local batch_end=$(date +%s.%N)
            local batch_duration=$(echo "$batch_end - $batch_start" | bc 2>/dev/null || echo "0")
            
            echo -n "批次 $batch/$requests_per_level 完成 (${batch_duration}s) - "
            echo "成功: $success_count, 失败: $fail_count"
            
            # 短暂延迟
            sleep 0.1
        done
        
        # 清理临时脚本
        rm -f "$temp_script"
        
        # 计算统计信息
        local total_requests=$((success_count + fail_count))
        local success_rate=0
        local avg_time=0
        local throughput=0
        
        if [[ $total_requests -gt 0 ]]; then
            success_rate=$(echo "scale=2; $success_count * 100 / $total_requests" | bc 2>/dev/null || echo "0")
        fi
        
        if [[ $success_count -gt 0 ]]; then
            avg_time=$(echo "scale=3; $total_time / $success_count" | bc 2>/dev/null || echo "0")
            throughput=$(echo "scale=2; $success_count / $total_time" | bc 2>/dev/null || echo "0")
        fi
        
        echo
        echo "=== 并发级别 $concurrent 测试结果 ===" | tee -a "$results_file"
        echo "总请求数: $total_requests" | tee -a "$results_file"
        echo "成功请求: $success_count" | tee -a "$results_file"
        echo "失败请求: $fail_count" | tee -a "$results_file"
        echo "成功率: ${success_rate}%" | tee -a "$results_file"
        echo "平均响应时间: ${avg_time}s" | tee -a "$results_file"
        echo "最快响应时间: ${min_time}s" | tee -a "$results_file"
        echo "最慢响应时间: ${max_time}s" | tee -a "$results_file"
        echo "吞吐量: ${throughput} 请求/秒" | tee -a "$results_file"
        echo "总耗时: ${total_time}s" | tee -a "$results_file"
        echo | tee -a "$results_file"
        
        # 重置计数器
        success_count=0
        fail_count=0
        total_time=0
        min_time=999999
        max_time=0
    done
}

# 生成测试报告
generate_report() {
    echo -e "${YELLOW}=== 生成测试报告 ===${NC}"
    
    local report_file="$RESULTS_DIR/test_report_$TIMESTAMP.md"
    
    cat > "$report_file" << EOF
# 用户订单列表接口并发性能测试报告

## 测试信息
- 测试时间: $(date)
- 基础URL: $BASE_URL
- 测试账号: $TEST_USERNAME
- 测试接口: /api/v1/express/orders (用户订单列表)

## 测试结果
EOF

    if [[ -f "$RESULTS_DIR/performance_$TIMESTAMP.txt" ]]; then
        echo "### 并发性能测试结果" >> "$report_file"
        echo '```' >> "$report_file"
        cat "$RESULTS_DIR/performance_$TIMESTAMP.txt" >> "$report_file"
        echo '```' >> "$report_file"
    fi
    
    echo "### 生成的文件" >> "$report_file"
    find "$RESULTS_DIR" -name "*_$TIMESTAMP.*" -type f | sort | while read file; do
        echo "- $(basename "$file")" >> "$report_file"
    done
    
    echo -e "${GREEN}测试报告已生成: $report_file${NC}"
}

# 主函数
main() {
    # 检查服务器
    if ! check_server; then
        echo -e "${RED}服务器未启动，请先启动后端服务${NC}"
        exit 1
    fi
    
    # 登录获取令牌
    if ! login_user; then
        echo -e "${RED}用户登录失败，无法进行测试${NC}"
        exit 1
    fi
    
    # 测试订单列表基本功能
    test_user_orders
    
    # 并发性能测试
    run_concurrent_test
    
    # 生成报告
    generate_report
    
    echo -e "${GREEN}=== 测试完成 ===${NC}"
    echo "结果文件保存在: $RESULTS_DIR"
}

# 运行主函数
main "$@" 