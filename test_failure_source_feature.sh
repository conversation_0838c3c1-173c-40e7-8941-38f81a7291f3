#!/bin/bash

# 测试失败来源标识功能
echo "🧪 测试失败来源标识功能..."

# 1. 检查数据库字段是否添加成功
echo "📊 检查数据库中的failure_source字段..."
psql "*************************************************/go_kuaidi?sslmode=disable" -c "
SELECT 
    column_name, 
    data_type, 
    is_nullable, 
    column_default,
    col_description(pgc.oid, pgac.attnum) as comment
FROM information_schema.columns pgac
JOIN pg_class pgc ON pgc.relname = 'order_records'
JOIN pg_namespace pgn ON pgn.oid = pgc.relnamespace
WHERE table_name = 'order_records' 
AND column_name = 'failure_source'
AND table_schema = 'public';
"

# 2. 检查现有失败订单的failure_source值分布
echo -e "\n📈 检查现有失败订单的failure_source分布..."
psql "*************************************************/go_kuaidi?sslmode=disable" -c "
SELECT 
    failure_source,
    COUNT(*) as count,
    ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER(), 2) as percentage
FROM order_records 
WHERE status = 'failed' 
GROUP BY failure_source 
ORDER BY count DESC;
"

# 3. 显示一些失败订单的详细信息（包含failure_source）
echo -e "\n🔍 显示最近的失败订单样例..."
psql "*************************************************/go_kuaidi?sslmode=disable" -c "
SELECT 
    platform_order_no,
    customer_order_no,
    provider,
    failure_reason,
    failure_source,
    failure_stage,
    substring(failure_message, 1, 50) as failure_message_preview,
    created_at
FROM order_records 
WHERE status = 'failed' 
AND failure_source IS NOT NULL 
ORDER BY created_at DESC 
LIMIT 5;
"

echo -e "\n✅ 数据库检查完成！"
echo "🎯 如果看到failure_source字段且有provider/system值，说明后端功能正常"
echo "📱 请检查用户前端页面，失败订单详情应该显示「供应商返回」或「系统返回」标签"

# 4. 构建状态检查
echo -e "\n🔧 检查构建状态..."
if [ -f "kuaidi-server" ]; then
    echo "✅ 后端构建成功 (kuaidi-server)"
else
    echo "❌ 后端构建失败"
fi

if [ -d "user-frontend/dist" ]; then
    echo "✅ 前端构建成功 (user-frontend/dist)"
else
    echo "❌ 前端构建失败"
fi

echo -e "\n🚀 功能测试完成！"