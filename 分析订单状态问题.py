#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import urllib.parse
import json
import psycopg2
from datetime import datetime

# 数据库连接配置
DB_CONFIG = {
    'host': '*************',
    'port': 5432,
    'user': 'postgres',
    'password': 'gjx6ngf4',
    'database': 'callback_receiver'
}

def decode_callback_data(raw_body):
    """解码回调数据"""
    try:
        # 解析URL编码的参数
        if 'param=' in raw_body:
            param_start = raw_body.find('param=') + 6
            param_end = raw_body.find('&', param_start)
            if param_end == -1:
                param_end = len(raw_body)
            
            encoded_param = raw_body[param_start:param_end]
            decoded_param = urllib.parse.unquote(encoded_param)
            
            # 解析JSON
            data = json.loads(decoded_param)
            return data
        return None
    except Exception as e:
        print(f"解码失败: {e}")
        return None

def analyze_order_callbacks():
    """分析订单回调"""
    order_numbers = [
        'DPK202580385165', 'DPK202580340237', 'DPK202579994323', 'DPK202579941633',
        'DPK202579931694', 'DPK202579913553', 'DPK202579712495', 'DPK202572917023',
        'DPK202572911053', 'DPK202572900214', 'DPK202572776229', 'DPK202571899463',
        'DPK202571736764'
    ]
    
    try:
        # 连接数据库
        conn = psycopg2.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        print("🔍 分析订单回调状态问题")
        print("=" * 50)
        
        for order_no in order_numbers:
            print(f"\n📦 分析订单: {order_no}")
            
            # 查询该订单的所有回调
            query = """
            SELECT id, provider, received_at, processed, raw_body 
            FROM callback_raw_data 
            WHERE raw_body LIKE %s 
            ORDER BY received_at DESC
            """
            
            cursor.execute(query, (f'%{order_no}%',))
            callbacks = cursor.fetchall()
            
            if not callbacks:
                print(f"   ❌ 没有找到订单 {order_no} 的回调")
                continue
                
            print(f"   📊 找到 {len(callbacks)} 条回调记录")
            
            for i, (cb_id, provider, received_at, processed, raw_body) in enumerate(callbacks):
                print(f"   📋 回调 {i+1}:")
                print(f"      - ID: {cb_id}")
                print(f"      - 供应商: {provider}")
                print(f"      - 接收时间: {received_at}")
                print(f"      - 已处理: {'是' if processed else '否'}")
                
                # 解码回调数据
                decoded_data = decode_callback_data(raw_body)
                if decoded_data:
                    data = decoded_data.get('data', {})
                    print(f"      - 订单ID: {data.get('orderId', 'N/A')}")
                    print(f"      - 第三方订单号: {data.get('thirdOrderId', 'N/A')}")
                    print(f"      - 状态: {data.get('status', 'N/A')}")
                    print(f"      - 发送状态: {data.get('sentStatus', 'N/A')}")
                    print(f"      - 取消消息: {data.get('cancelMsg', 'N/A')}")
                    print(f"      - 运费: {data.get('freight', 'N/A')}")
                    print(f"      - 重量: {data.get('weight', 'N/A')}")
                    print(f"      - 快递员: {data.get('courierName', 'N/A')}")
                    print(f"      - 快递员电话: {data.get('courierMobile', 'N/A')}")
                    
                    # 分析状态
                    status = data.get('status')
                    sent_status = data.get('sentStatus')
                    cancel_msg = data.get('cancelMsg')
                    
                    if status == 0:
                        print(f"      ⚠️  状态分析: 订单创建/待处理")
                    elif status == 1:
                        print(f"      ✅ 状态分析: 订单已接单")
                    elif status == 2:
                        print(f"      🚚 状态分析: 订单已揽收")
                    elif status == 3:
                        print(f"      📦 状态分析: 订单运输中")
                    elif status == 4:
                        print(f"      🎯 状态分析: 订单已签收")
                    elif status == 5:
                        print(f"      ❌ 状态分析: 订单异常")
                    elif status == 6:
                        print(f"      🔄 状态分析: 订单退回")
                    elif status == 7:
                        print(f"      ❌ 状态分析: 订单取消")
                    else:
                        print(f"      ❓ 状态分析: 未知状态 ({status})")
                    
                    if sent_status == 2:
                        print(f"      📤 发送状态: 已发送")
                    elif sent_status == 1:
                        print(f"      ⏳ 发送状态: 发送中")
                    elif sent_status == 0:
                        print(f"      ⏸️  发送状态: 未发送")
                    
                    if cancel_msg:
                        print(f"      ❌ 取消原因: {cancel_msg}")
                else:
                    print(f"      ❌ 无法解码回调数据")
                
                print()
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"❌ 数据库查询失败: {e}")

def check_main_database_orders():
    """检查主数据库中的订单状态"""
    order_numbers = [
        'DPK202580385165', 'DPK202580340237', 'DPK202579994323', 'DPK202579941633',
        'DPK202579931694', 'DPK202579913553', 'DPK202579712495', 'DPK202572917023',
        'DPK202572911053', 'DPK202572900214', 'DPK202572776229', 'DPK202571899463',
        'DPK202571736764'
    ]
    
    try:
        # 连接主数据库
        main_db_config = DB_CONFIG.copy()
        main_db_config['database'] = 'go_kuaidi'
        
        conn = psycopg2.connect(**main_db_config)
        cursor = conn.cursor()
        
        print("\n🔍 检查主数据库中的订单状态")
        print("=" * 50)
        
        for order_no in order_numbers:
            # 按运单号查询
            query = """
            SELECT customer_order_no, order_no, tracking_no, provider, status, created_at, updated_at 
            FROM order_records 
            WHERE tracking_no = %s OR customer_order_no LIKE %s
            """
            
            cursor.execute(query, (order_no, f'%{order_no}%'))
            orders = cursor.fetchall()
            
            if orders:
                for order in orders:
                    customer_order_no, order_no_db, tracking_no, provider, status, created_at, updated_at = order
                    print(f"📦 订单: {customer_order_no}")
                    print(f"   - 平台订单号: {order_no_db}")
                    print(f"   - 运单号: {tracking_no}")
                    print(f"   - 供应商: {provider}")
                    print(f"   - 状态: {status}")
                    print(f"   - 创建时间: {created_at}")
                    print(f"   - 更新时间: {updated_at}")
                    print()
            else:
                print(f"❌ 主数据库中没有找到运单号 {order_no} 的订单")
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"❌ 主数据库查询失败: {e}")

if __name__ == "__main__":
    print("🚀 开始分析订单状态问题...")
    analyze_order_callbacks()
    check_main_database_orders()
    print("\n🏁 分析完成！")
