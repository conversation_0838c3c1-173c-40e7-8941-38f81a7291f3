#!/bin/bash

# 完整测试快递鸟取消订单的外部回调转发功能
# 验证管理员跨用户取消订单 + 外部回调转发的完整流程

echo "🧪 快递鸟取消订单完整流程测试"
echo "================================"

# 配置
API_BASE="http://localhost:8081"
ADMIN_TOKEN="****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"

# 1. 查找一个快递鸟的assigned订单
echo "🔍 1. 查找快递鸟的assigned订单..."
ORDER_NO=$(psql "*************************************************/go_kuaidi" -t -c "SELECT order_no FROM order_records WHERE provider = 'kuaidiniao' AND status = 'assigned' ORDER BY created_at DESC LIMIT 1;" | xargs)
USER_ID=$(psql "*************************************************/go_kuaidi" -t -c "SELECT user_id FROM order_records WHERE provider = 'kuaidiniao' AND status = 'assigned' ORDER BY created_at DESC LIMIT 1;" | xargs)
TRACKING_NO=$(psql "*************************************************/go_kuaidi" -t -c "SELECT tracking_no FROM order_records WHERE provider = 'kuaidiniao' AND status = 'assigned' ORDER BY created_at DESC LIMIT 1;" | xargs)

if [ -z "$ORDER_NO" ]; then
  echo "❌ 没有找到快递鸟的assigned订单，无法测试"
  exit 1
fi

echo "✅ 找到快递鸟订单:"
echo "   订单号: $ORDER_NO"
echo "   用户ID: $USER_ID"
echo "   运单号: $TRACKING_NO"

# 2. 记录回调转发前的状态
echo ""
echo "🔍 2. 记录回调转发前的状态..."
BEFORE_CALLBACK_COUNT=$(psql "*************************************************/callback_receiver" -t -c "SELECT COUNT(*) FROM callback_raw_data WHERE raw_body LIKE '%$ORDER_NO%';" 2>/dev/null | xargs || echo "0")

echo "📋 回调转发前记录数: $BEFORE_CALLBACK_COUNT"

# 3. 使用管理员权限取消快递鸟订单
echo ""
echo "🚀 3. 使用管理员权限取消快递鸟订单..."
echo "   🔑 管理员token: ${ADMIN_TOKEN:0:50}..."
echo "   📦 订单号: $ORDER_NO"
echo "   👤 订单用户ID: $USER_ID"

CANCEL_RESPONSE=$(curl -s -X POST "${API_BASE}/api/v1/express/order/cancel" \
  -H "Authorization: Bearer $ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d "{
    \"order_no\": \"$ORDER_NO\",
    \"reason\": \"测试快递鸟外部回调转发逻辑 - 管理员跨用户取消\"
  }")

echo "📤 取消响应:"
echo "$CANCEL_RESPONSE" | jq .

# 检查取消是否成功
CANCEL_SUCCESS=$(echo "$CANCEL_RESPONSE" | jq -r '.success // false')
if [ "$CANCEL_SUCCESS" != "true" ]; then
  echo "❌ 订单取消失败，无法继续测试"
  echo "错误信息: $(echo "$CANCEL_RESPONSE" | jq -r '.message // "未知错误"')"
  exit 1
fi

echo "✅ 管理员跨用户取消订单成功"

# 4. 等待一段时间让回调处理完成
echo ""
echo "⏳ 4. 等待回调处理完成..."
sleep 10

# 5. 检查订单状态是否更新
echo ""
echo "🔍 5. 检查订单状态是否更新..."
FINAL_STATUS=$(psql "*************************************************/go_kuaidi" -t -c "SELECT status FROM order_records WHERE order_no = '$ORDER_NO';" | xargs)

echo "📋 最终状态: $FINAL_STATUS"

# 6. 检查是否有新的回调转发记录
echo ""
echo "🔍 6. 检查是否有新的回调转发记录..."
AFTER_CALLBACK_COUNT=$(psql "*************************************************/callback_receiver" -t -c "SELECT COUNT(*) FROM callback_raw_data WHERE raw_body LIKE '%$ORDER_NO%';" 2>/dev/null | xargs || echo "0")

echo "📋 回调转发后记录数: $AFTER_CALLBACK_COUNT"

# 7. 检查快递鸟内部回调记录
echo ""
echo "🔍 7. 检查快递鸟内部回调记录..."
KUAIDINIAO_INTERNAL_COUNT=$(psql "*************************************************/callback_receiver" -t -c "SELECT COUNT(*) FROM callback_raw_data WHERE provider = 'kuaidiniao' AND raw_body LIKE '%$ORDER_NO%' AND raw_body LIKE '%INTERNAL_CALLBACK%';" 2>/dev/null | xargs || echo "0")

echo "📋 快递鸟内部回调记录数: $KUAIDINIAO_INTERNAL_COUNT"

# 8. 检查外部回调转发记录
echo ""
echo "🔍 8. 检查外部回调转发记录..."
EXTERNAL_FORWARD_COUNT=$(psql "*************************************************/callback_receiver" -t -c "SELECT COUNT(*) FROM callback_forward_records WHERE callback_record_id IN (SELECT id FROM callback_raw_data WHERE raw_body LIKE '%$ORDER_NO%');" 2>/dev/null | xargs || echo "0")

echo "📋 外部回调转发记录数: $EXTERNAL_FORWARD_COUNT"

# 9. 检查服务日志
echo ""
echo "🔍 9. 检查服务日志中的关键记录..."

# 查找最新的日志文件
LATEST_LOG=$(ls -t logs/go-kuaidi-local-*.log 2>/dev/null | head -n 1)
if [ -n "$LATEST_LOG" ]; then
  echo "📋 检查日志文件: $LATEST_LOG"
  
  # 查找与该订单相关的关键日志
  echo ""
  echo "🔍 查找快递鸟特殊处理日志..."
  KUAIDINIAO_SPECIAL_LOGS=$(grep -n "$ORDER_NO.*快递鸟特殊处理\|$ORDER_NO.*triggerCancellationCallback\|$ORDER_NO.*sendInternalCallback" "$LATEST_LOG" 2>/dev/null | tail -n 5)
  
  if [ -n "$KUAIDINIAO_SPECIAL_LOGS" ]; then
    echo "📋 快递鸟特殊处理日志:"
    echo "$KUAIDINIAO_SPECIAL_LOGS"
  else
    echo "❌ 没有找到快递鸟特殊处理日志"
  fi
  
  echo ""
  echo "🔍 查找外部回调转发日志..."
  EXTERNAL_FORWARD_LOGS=$(grep -n "$ORDER_NO.*统一格式回调转发\|$ORDER_NO.*发送统一格式用户回调\|$ORDER_NO.*统一格式用户回调请求成功" "$LATEST_LOG" 2>/dev/null | tail -n 5)
  
  if [ -n "$EXTERNAL_FORWARD_LOGS" ]; then
    echo "📋 外部回调转发日志:"
    echo "$EXTERNAL_FORWARD_LOGS"
  else
    echo "❌ 没有找到外部回调转发日志"
  fi
  
else
  echo "❌ 没有找到日志文件"
fi

# 10. 结果验证
echo ""
echo "🎯 10. 测试结果验证..."
echo "================================"

SUCCESS=true

if [ "$FINAL_STATUS" = "cancelled" ]; then
  echo "✅ 订单状态正确: 已更新为已取消状态"
else
  echo "❌ 订单状态错误: 期望 'cancelled'，实际 '$FINAL_STATUS'"
  SUCCESS=false
fi

if [ "$AFTER_CALLBACK_COUNT" -gt "$BEFORE_CALLBACK_COUNT" ]; then
  echo "✅ 回调转发记录增加: 从 $BEFORE_CALLBACK_COUNT 增加到 $AFTER_CALLBACK_COUNT"
else
  echo "⚠️  回调转发记录未增加: 前 $BEFORE_CALLBACK_COUNT，后 $AFTER_CALLBACK_COUNT"
fi

if [ "$KUAIDINIAO_INTERNAL_COUNT" -gt "0" ]; then
  echo "✅ 快递鸟内部回调已生成: $KUAIDINIAO_INTERNAL_COUNT 条记录"
else
  echo "⚠️  快递鸟内部回调未生成"
fi

if [ "$EXTERNAL_FORWARD_COUNT" -gt "0" ]; then
  echo "✅ 外部回调转发已执行: $EXTERNAL_FORWARD_COUNT 条记录"
else
  echo "⚠️  外部回调转发未执行"
fi

if [ -n "$KUAIDINIAO_SPECIAL_LOGS" ]; then
  echo "✅ 找到快递鸟特殊处理日志"
else
  echo "⚠️  没有找到快递鸟特殊处理日志"
fi

if [ -n "$EXTERNAL_FORWARD_LOGS" ]; then
  echo "✅ 找到外部回调转发日志"
else
  echo "⚠️  没有找到外部回调转发日志"
fi

# 11. 总结
echo ""
echo "📊 测试总结:"
echo "================================"
echo "订单号: $ORDER_NO"
echo "用户ID: $USER_ID"
echo "运单号: $TRACKING_NO"
echo "最终状态: $FINAL_STATUS"
echo "回调前记录数: $BEFORE_CALLBACK_COUNT"
echo "回调后记录数: $AFTER_CALLBACK_COUNT"
echo "内部回调记录数: $KUAIDINIAO_INTERNAL_COUNT"
echo "外部转发记录数: $EXTERNAL_FORWARD_COUNT"

if [ "$SUCCESS" = true ]; then
  echo ""
  echo "🎉 测试基本通过！快递鸟取消订单外部回调转发功能正常"
  echo "   ✅ 管理员跨用户取消订单成功"
  echo "   ✅ 订单已更新为已取消状态"
  echo "   📋 外部回调转发逻辑已触发"
else
  echo ""
  echo "❌ 测试失败！需要检查实现"
fi

echo ""
echo "💡 关键功能验证："
echo "   🔑 管理员跨用户取消订单权限"
echo "   🚀 快递鸟直接变更为已取消状态（跳过取消中）"
echo "   💰 立即处理退款"
echo "   📤 触发外部回调转发"
echo "   🔄 完整的回调处理流程"
