package adapter

import (
	"net/http"
	"sync"
	"time"

	"github.com/go-playground/validator/v10"
	"github.com/your-org/go-kuaidi/internal/model"
	"go.uber.org/zap"
	"golang.org/x/time/rate"
)

// KuaidiNiaoConfig 快递鸟配置
type KuaidiNiaoConfig struct {
	EBusinessID string `json:"e_business_id" validate:"required"`               // 用户ID
	ApiKey      string `json:"api_key" validate:"required"`                     // API密钥
	BaseURL     string `json:"base_url" validate:"required,url"`                // API基础URL
	Environment string `json:"environment" validate:"oneof=sandbox production"` // 环境
	Timeout     int    `json:"timeout" validate:"min=1,max=60"`                 // 超时时间（秒）
}

// KuaidiNiaoAdapter 快递鸟适配器
type KuaidiNiaoAdapter struct {
	config             KuaidiNiaoConfig
	client             *http.Client
	logger             *zap.Logger
	validator          *validator.Validate
	rateLimiter        *rate.Limiter
	mappingService     interface{}
	expressCompanyRepo interface{}

	// 智能缓存相关字段
	priceCache    map[string]*KuaidiNiaoCachedPrices // 路线价格缓存
	cacheExpiry   time.Duration                      // 缓存过期时间
	lastCacheTime map[string]time.Time               // 每个路线的最后缓存时间
	cacheMutex    sync.RWMutex                       // 缓存读写锁
}

// KuaidiNiaoCachedPrices 快递鸟缓存的价格数据
type KuaidiNiaoCachedPrices struct {
	Route       string                    `json:"route"`        // 路线key
	Prices      []model.StandardizedPrice `json:"prices"`       // 所有快递公司价格
	CachedAt    time.Time                 `json:"cached_at"`    // 缓存时间
	RequestHash string                    `json:"request_hash"` // 请求参数hash
}

// ==== 工单相关结构 ====

// KuaidiNiaoWorkOrderRequest 快递鸟工单提交请求（接口指令1807）
type KuaidiNiaoWorkOrderRequest struct {
	MemberID         string                       `json:"MemberId,omitempty"`     // 平台唯一ID
	Mobile           string                       `json:"Mobile"`                 // 反馈人手机号
	Name             string                       `json:"Name"`                   // 反馈人姓名
	OrderCode        string                       `json:"OrderCode"`              // 商家订单号
	LogisticCode     string                       `json:"LogisticCode,omitempty"` // 物流运单号
	ComplaintType    int                          `json:"ComplaintType"`          // 工单类型
	ComplaintContent string                       `json:"ComplaintContent"`       // 投诉内容
	Source           int                          `json:"Source"`                 // 工单数据来源 1：客户 4：云工单
	PicList          []KuaidiNiaoWorkOrderPicture `json:"PicList,omitempty"`      // 投诉图片列表
}

// KuaidiNiaoWorkOrderPicture 工单图片
type KuaidiNiaoWorkOrderPicture struct {
	PictureItem string `json:"PictureItem"` // 图片URL或base64编码
}

// KuaidiNiaoWorkOrderResponse 快递鸟工单提交响应
type KuaidiNiaoWorkOrderResponse struct {
	EBusinessID     string `json:"EBusinessID"`     // 用户ID
	ComplaintNumber string `json:"ComplaintNumber"` // 工单处理单号
	Success         bool   `json:"Success"`         // 成功与否
	ResultCode      string `json:"ResultCode"`      // 返回编号
	Reason          string `json:"Reason"`          // 描述
}

// KuaidiNiaoWorkOrderType 快递鸟工单类型
type KuaidiNiaoWorkOrderType struct {
	TicketTypeCode int    `json:"ticketTypeCode"` // 工单类型编码
	TicketTypeName string `json:"ticketTypeName"` // 工单类型名称
}

// KuaidiNiaoWorkOrderTypesResponse 快递鸟工单类型查询响应
type KuaidiNiaoWorkOrderTypesResponse struct {
	EBusinessID string                    `json:"EBusinessID"` // 用户ID
	Data        []KuaidiNiaoWorkOrderType `json:"Data"`        // 工单类型列表
	Success     bool                      `json:"Success"`     // 成功与否
	ResultCode  string                    `json:"ResultCode"`  // 返回编号
	Reason      string                    `json:"Reason"`      // 描述
}

// KuaidiNiaoWorkOrderQueryRequest 快递鸟工单详情查询请求（接口指令1818）
type KuaidiNiaoWorkOrderQueryRequest struct {
	PageIndex     int      `json:"pageIndex,omitempty"`   // 起始页，>=1,默认1
	SizePerPage   int      `json:"sizePerPage,omitempty"` // 每页数，<=20,默认20
	KdnOrderCodes []string `json:"kdnOrderCodes"`         // 快递鸟订单号集合
	Source        int      `json:"source,omitempty"`      // 数据来源
}

// KuaidiNiaoWorkOrderQueryResponse 快递鸟工单详情查询响应
type KuaidiNiaoWorkOrderQueryResponse struct {
	EBusinessID string                       `json:"EBusinessID"` // 用户ID
	Success     bool                         `json:"Success"`     // 成功与否
	ResultCode  string                       `json:"ResultCode"`  // 返回编号
	Reason      string                       `json:"Reason"`      // 描述
	Data        KuaidiNiaoWorkOrderQueryData `json:"Data"`        // 数据
}

// KuaidiNiaoWorkOrderQueryData 工单查询数据
type KuaidiNiaoWorkOrderQueryData struct {
	PageIndex   int                         `json:"pageIndex"`   // 开始页
	SizePerPage int                         `json:"sizePerPage"` // 每页数
	TotalCount  int                         `json:"totalCount"`  // 总条数
	Rows        []KuaidiNiaoWorkOrderDetail `json:"rows"`        // 工单详情列表
}

// KuaidiNiaoWorkOrderDetail 工单详情
type KuaidiNiaoWorkOrderDetail struct {
	CustomerID         string                     `json:"customerId"`         // 用户ID
	KdnOrderCode       string                     `json:"kdnOrderCode"`       // 快递鸟订单号
	TicketType         int                        `json:"ticketType"`         // 工单类型编码
	TicketTypeName     string                     `json:"ticketTypeName"`     // 工单类型名称
	TicketNumber       string                     `json:"ticketNumber"`       // 工单处理单号
	CreateTime         string                     `json:"createTime"`         // 工单创建时间
	Status             int                        `json:"status"`             // 工单状态 0:待处理 1:处理中 2:已处理
	DealResult         string                     `json:"dealResult"`         // 工单处理结果
	DealResultFiles    string                     `json:"dealResultFiles"`    // 处理结果附件URL
	TicketPic          string                     `json:"ticketPic"`          // 工单提交附件URL
	TicketDetailTracks []KuaidiNiaoWorkOrderTrack `json:"ticketDetailTracks"` // 工单处理日志记录
	Source             int                        `json:"source"`             // 数据来源
}

// KuaidiNiaoWorkOrderTrack 工单处理日志记录
type KuaidiNiaoWorkOrderTrack struct {
	KdnOrderCode      string `json:"kdnOrderCode"`      // 快递鸟订单号
	TicketNumber      string `json:"ticketNumber"`      // 工单处理单号
	TrackResult       string `json:"trackResult"`       // 工单处理结果
	TrackResultFiles  string `json:"trackResultFiles"`  // 处理结果附件URL
	TrackTime         string `json:"trackTime"`         // 处理时间
	TrackOperatorName string `json:"trackOperatorName"` // 操作人
}

// KuaidiNiaoWorkOrderTypeQueryResponse 快递鸟工单类型查询响应（接口指令1817）
type KuaidiNiaoWorkOrderTypeQueryResponse struct {
	EBusinessID string                    `json:"EBusinessID"` // 用户ID
	Success     bool                      `json:"Success"`     // 成功与否
	ResultCode  string                    `json:"ResultCode"`  // 返回编号
	Reason      string                    `json:"Reason"`      // 描述
	Data        []KuaidiNiaoWorkOrderType `json:"Data"`        // 工单类型列表
}

// 系统级参数结构
type KuaidiNiaoSystemParams struct {
	RequestType string `form:"RequestType"` // 请求接口指令
	EBusinessID string `form:"EBusinessID"` // 用户ID
	RequestData string `form:"RequestData"` // 应用级参数(JSON格式)
	DataSign    string `form:"DataSign"`    // 数据内容签名
	DataType    string `form:"DataType"`    // 数据格式(固定为2)
}

// ==== 价格查询相关结构 ====

// KuaidiNiaoPriceRequest 价格查询请求
type KuaidiNiaoPriceRequest struct {
	TransportType int                   `json:"TransportType"`          // 运力类型 1=快递类
	ShipperType   int                   `json:"ShipperType"`            // 产品类型 3=2小时收 4=半日收 5=当日收
	Weight        float64               `json:"Weight"`                 // 包裹总重量(KG)
	InsureAmount  float64               `json:"InsureAmount,omitempty"` // 声明价值
	Receiver      KuaidiNiaoAddressInfo `json:"Receiver"`               // 收件人信息
	Sender        KuaidiNiaoAddressInfo `json:"Sender"`                 // 发件人信息
}

// ==== 超区校验相关结构 ====

// KuaidiNiaoRegionCheckRequest 快递鸟超区校验请求（接口指令1814）
type KuaidiNiaoRegionCheckRequest struct {
	TransportType int                   `json:"TransportType"` // 运力类型：1=快递类
	ShipperType   int                   `json:"ShipperType"`   // 产品类型：3=2小时收，4=半日收，5=当日收
	ShipperCode   string                `json:"ShipperCode"`   // 快递公司编码（可选）
	Receiver      KuaidiNiaoAddressInfo `json:"Receiver"`      // 收件人信息
	Sender        KuaidiNiaoAddressInfo `json:"Sender"`        // 寄件人信息
	Weight        float64               `json:"Weight"`        // 包裹总重量
}

// KuaidiNiaoRegionCheckResponse 快递鸟超区校验响应
type KuaidiNiaoRegionCheckResponse struct {
	EBusinessID          string                      `json:"EBusinessID"`
	Success              bool                        `json:"Success"`
	ResultCode           string                      `json:"ResultCode"`
	Reason               string                      `json:"Reason"`
	UniquerRequestNumber string                      `json:"UniquerRequestNumber"`
	Data                 []KuaidiNiaoRegionCheckData `json:"Data"`
}

// KuaidiNiaoRegionCheckData 快递鸟超区校验数据
type KuaidiNiaoRegionCheckData struct {
	ShipperCode        string               `json:"shipperCode"`        // 快递公司编码
	ShipperName        string               `json:"shipperName"`        // 快递公司名称
	IsSupport          bool                 `json:"isSupport"`          // 是否支持
	SupportReason      string               `json:"supportReason"`      // 支持原因
	AvailableTimeSlots []KuaidiNiaoTimeSlot `json:"availableTimeSlots"` // 可用时间段
}

// KuaidiNiaoTimeSlot 快递鸟时间段
type KuaidiNiaoTimeSlot struct {
	SlotID      string `json:"slotId"`      // 时间段ID
	SlotName    string `json:"slotName"`    // 时间段名称（如：明天 09:00-23:59）
	StartTime   string `json:"startTime"`   // 开始时间 (YYYY-MM-DD HH:mm:ss)
	EndTime     string `json:"endTime"`     // 结束时间 (YYYY-MM-DD HH:mm:ss)
	Available   bool   `json:"available"`   // 是否可用
	Description string `json:"description"` // 描述信息
}

// KuaidiNiaoAddressInfo 地址信息
type KuaidiNiaoAddressInfo struct {
	ProvinceName string `json:"ProvinceName"` // 省/直辖市/自治区名称
	CityName     string `json:"CityName"`     // 城市名称
	ExpAreaName  string `json:"ExpAreaName"`  // 区/县名称
}

// KuaidiNiaoPriceResponse 价格查询响应
type KuaidiNiaoPriceResponse struct {
	EBusinessID          string                `json:"EBusinessID"`          // 用户ID
	Success              bool                  `json:"Success"`              // 成功与否
	ResultCode           string                `json:"ResultCode"`           // 返回编号
	Data                 []KuaidiNiaoPriceData `json:"Data"`                 // 价格数据列表
	Reason               string                `json:"Reason"`               // 描述
	UniquerRequestNumber string                `json:"UniquerRequestNumber"` // 唯一标识
}

// KuaidiNiaoPriceData 价格数据
type KuaidiNiaoPriceData struct {
	ShipperCode            string  `json:"shipperCode"`            // 快递公司编码
	Weight                 string  `json:"weight"`                 // 总重量(KG)
	Cost                   string  `json:"cost"`                   // 基础运费
	FirstWeight            float64 `json:"firstWeight"`            // 首重重量(KG)
	FirstWeightAmount      float64 `json:"firstWeightAmount"`      // 首重金额
	ContinuousWeight       float64 `json:"continuousWeight"`       // 续重重量(KG)
	ContinuousWeightPrice  float64 `json:"continuousWeightPrice"`  // 续重单价(元/KG)
	ContinuousWeightAmount float64 `json:"continuousWeightAmount"` // 续重总金额
	InsureAmount           float64 `json:"insureAmount"`           // 声明价值
	PremiumFee             float64 `json:"premiumFee"`             // 保费
	TotalFee               float64 `json:"totalFee"`               // 总费用
}

// ==== 下单相关结构 ====

// KuaidiNiaoOrderRequest 下单请求
type KuaidiNiaoOrderRequest struct {
	TransportType       int                       `json:"TransportType"`                 // 运力类型 1=快递类
	ShipperType         int                       `json:"ShipperType"`                   // 快递类型
	ShipperCode         string                    `json:"ShipperCode"`                   // 快递公司编码
	OrderCode           string                    `json:"OrderCode"`                     // 商家订单编号
	ExpType             int                       `json:"ExpType"`                       // 快递类型
	PayType             int                       `json:"PayType"`                       // 支付类型
	Receiver            KuaidiNiaoReceiverInfo    `json:"Receiver"`                      // 收件人信息
	Sender              KuaidiNiaoSenderInfo      `json:"Sender"`                        // 发件人信息
	StartDate           string                    `json:"StartDate,omitempty"`           // 取件开始时间
	EndDate             string                    `json:"EndDate,omitempty"`             // 取件结束时间
	Weight              float64                   `json:"Weight"`                        // 包裹总重量
	Quantity            int                       `json:"Quantity"`                      // 包裹数量
	Volume              float64                   `json:"Volume,omitempty"`              // 包裹总体积
	Remark              string                    `json:"Remark,omitempty"`              // 备注
	InsureAmount        float64                   `json:"InsureAmount,omitempty"`        // 声明价值
	ActualPaymentAmount float64                   `json:"ActualPaymentAmount,omitempty"` // 实际付款金额
	NotifyUrl           string                    `json:"NotifyUrl,omitempty"`           // 订单回调通知URL
	Commodity           []KuaidiNiaoCommodityInfo `json:"Commodity"`                     // 商品信息
}

// KuaidiNiaoReceiverInfo 收件人信息
type KuaidiNiaoReceiverInfo struct {
	ProvinceName string `json:"ProvinceName"` // 收件省
	CityName     string `json:"CityName"`     // 收件市
	ExpAreaName  string `json:"ExpAreaName"`  // 收件区/县
	Address      string `json:"Address"`      // 收件详细地址
	Name         string `json:"Name"`         // 收件人姓名
	Mobile       string `json:"Mobile"`       // 收件人手机号
	Tel          string `json:"Tel"`          // 收件人固定电话（区号-尾数）
}

// KuaidiNiaoSenderInfo 发件人信息
type KuaidiNiaoSenderInfo struct {
	ProvinceName string `json:"ProvinceName"` // 发件省
	CityName     string `json:"CityName"`     // 发件市
	ExpAreaName  string `json:"ExpAreaName"`  // 发件区/县
	Address      string `json:"Address"`      // 发件详细地址
	Name         string `json:"Name"`         // 发件人姓名
	Mobile       string `json:"Mobile"`       // 发件人手机号
	Tel          string `json:"Tel"`          // 发件人固定电话（区号-尾数）
}

// KuaidiNiaoCommodityInfo 商品信息
type KuaidiNiaoCommodityInfo struct {
	GoodsName     string  `json:"GoodsName"`     // 商品名称
	GoodsCode     string  `json:"GoodsCode"`     // 商品编码
	GoodsPrice    float64 `json:"GoodsPrice"`    // 商品价格
	GoodsQuantity int     `json:"GoodsQuantity"` // 商品数量
}

// KuaidiNiaoOrderResponse 下单响应 - 根据官方API文档修正
type KuaidiNiaoOrderResponse struct {
	EBusinessID          string                `json:"EBusinessID"`          // 用户ID
	Success              bool                  `json:"Success"`              // 成功与否
	ResultCode           string                `json:"ResultCode"`           // 返回编号
	Reason               string                `json:"Reason"`               // 描述
	UniquerRequestNumber string                `json:"UniquerRequestNumber"` // 唯一标识
	StartDate            string                `json:"StartDate"`            // 预约开始时间
	EndDate              string                `json:"EndDate"`              // 预约结束时间
	Order                KuaidiNiaoOrderResult `json:"Order"`                // 订单数据
}

// KuaidiNiaoOrderResult 订单结果 - 根据官方API文档修正
type KuaidiNiaoOrderResult struct {
	OrderCode    string `json:"OrderCode"`    // 商家订单号
	KDNOrderCode string `json:"KDNOrderCode"` // 快递鸟订单号
}

// ==== 回调相关结构 ====

// KuaidiNiaoCallbackRequest 回调请求
type KuaidiNiaoCallbackRequest struct {
	RequestType string `form:"RequestType"` // 请求类型(固定为103)
	EBusinessID string `form:"EBusinessID"` // 用户ID
	RequestData string `form:"RequestData"` // 回调数据(JSON格式)
	DataSign    string `form:"DataSign"`    // 数据签名
	DataType    string `form:"DataType"`    // 数据格式(固定为2)
}

// KuaidiNiaoCallbackData 回调数据
type KuaidiNiaoCallbackData struct {
	PushTime    string                   `json:"PushTime"`    // 推送时间
	EBusinessID string                   `json:"EBusinessID"` // 用户ID
	Data        []KuaidiNiaoCallbackItem `json:"Data"`        // 回调数据列表
	Count       int                      `json:"Count"`       // 推送个数
}

// KuaidiNiaoCallbackItem 回调数据项
type KuaidiNiaoCallbackItem struct {
	KDNOrderCode string `json:"KDNOrderCode"` // 快递鸟单号
	OrderCode    string `json:"OrderCode"`    // 商家订单号
	LogisticCode string `json:"LogisticCode"` // 物流运单号
	ShipperCode  string `json:"ShipperCode"`  // 快递公司编码
	State        string `json:"State"`        // 状态码
	Reason       string `json:"Reason"`       // 描述
	CreateTime   string `json:"CreateTime"`   // 状态推送时间
	OperateType  int    `json:"OperateType"`  // 操作人 1:快递鸟 2:物流公司
}

// ==== 订单信息查询相关结构 ====

// KuaidiNiaoOrderQueryRequest 订单信息查询请求（接口指令1804）
type KuaidiNiaoOrderQueryRequest struct {
	ShipperCode string `json:"ShipperCode,omitempty"` // 快递公司编码（可选）
	OrderCode   string `json:"OrderCode"`             // 商家订单编号（必需）
}

// KuaidiNiaoOrderQueryResponse 订单信息查询响应
type KuaidiNiaoOrderQueryResponse struct {
	EBusinessID            string  `json:"EBusinessID"`                      // 用户ID
	Success                bool    `json:"Success"`                          // 成功与否
	ResultCode             string  `json:"ResultCode"`                       // 返回编号
	Reason                 string  `json:"Reason"`                           // 描述
	KDNOrderCode           string  `json:"KDNOrderCode"`                     // KDN订单号
	OrderCode              string  `json:"OrderCode"`                        // 商家订单编号
	ShipperCode            string  `json:"ShipperCode"`                      // 快递公司编码
	LogisticCode           string  `json:"LogisticCode,omitempty"`           // 物流运单号
	State                  string  `json:"State"`                            // 订单状态码
	SignType               string  `json:"SignType,omitempty"`               // 签收类型编码
	CreateTime             string  `json:"CreateTime"`                       // 下单时间
	PersonName             string  `json:"PersonName,omitempty"`             // 取件快递员姓名
	PersonTel              string  `json:"PersonTel,omitempty"`              // 取件快递员电话
	PersonCode             string  `json:"PersonCode,omitempty"`             // 取件快递员工号
	StationName            string  `json:"StationName,omitempty"`            // 取件网点名称
	StationCode            string  `json:"StationCode,omitempty"`            // 取件网点编号
	StationAddress         string  `json:"StationAddress,omitempty"`         // 取件网点地址
	StationTel             string  `json:"StationTel,omitempty"`             // 取件网点电话
	PickupCode             string  `json:"PickupCode,omitempty"`             // 取件码
	Weight                 float64 `json:"Weight,omitempty"`                 // 总重量(KG)
	Cost                   float64 `json:"Cost,omitempty"`                   // 基础运费
	FirstWeightAmount      float64 `json:"FirstWeightAmount,omitempty"`      // 首重金额
	ContinuousWeightAmount float64 `json:"ContinuousWeightAmount,omitempty"` // 续重总金额
	InsureAmount           float64 `json:"InsureAmount,omitempty"`           // 保价费
	PackageFee             float64 `json:"PackageFee,omitempty"`             // 包装费
	OverFee                float64 `json:"OverFee,omitempty"`                // 超长超重费
	OtherFee               float64 `json:"OtherFee,omitempty"`               // 其他费
	OtherFeeDetail         string  `json:"OtherFeeDetail,omitempty"`         // 其他费明细
	TotalFee               float64 `json:"TotalFee,omitempty"`               // 总费用
	Volume                 float64 `json:"Volume,omitempty"`                 // 体积
	VolumeWeight           float64 `json:"VolumeWeight,omitempty"`           // 体积重
	ActualWeight           float64 `json:"ActualWeight,omitempty"`           // 称重重量
}

// KuaidiNiaoPickerInfo 取件信息
type KuaidiNiaoPickerInfo struct {
	PickupCode     string `json:"PickupCode,omitempty"`     // 取件码
	StationCode    string `json:"StationCode,omitempty"`    // 网点编码
	StationName    string `json:"StationName,omitempty"`    // 网点名称
	StationTel     string `json:"StationTel,omitempty"`     // 网点电话
	StationAddress string `json:"StationAddress,omitempty"` // 网点地址
	PersonName     string `json:"PersonName,omitempty"`     // 快递员姓名
	PersonTel      string `json:"PersonTel,omitempty"`      // 快递员手机号
	PersonCode     string `json:"PersonCode,omitempty"`     // 快递员编号
}

// KuaidiNiaoCallbackResponse 回调响应
type KuaidiNiaoCallbackResponse struct {
	EBusinessID string `json:"EBusinessID"` // 用户ID
	Success     bool   `json:"Success"`     // 成功标识
	UpdateTime  string `json:"UpdateTime"`  // 处理完结时间
	Reason      string `json:"Reason"`      // 描述
}

// ==== 订单取消相关结构 ====

// KuaidiNiaoCancelRequest 订单取消请求
type KuaidiNiaoCancelRequest struct {
	ShipperCode  string `json:"ShipperCode,omitempty"`  // 快递公司编码
	OrderCode    string `json:"OrderCode"`              // 商家订单编号
	LogisticCode string `json:"LogisticCode,omitempty"` // 快递单号
	CancelType   int    `json:"CancelType,omitempty"`   // 取消类型
	CancelMsg    string `json:"CancelMsg,omitempty"`    // 取消原因
}

// KuaidiNiaoCancelResponse 订单取消响应
type KuaidiNiaoCancelResponse struct {
	EBusinessID string `json:"EBusinessID"`
	Success     bool   `json:"Success"`
	ResultCode  string `json:"ResultCode"`
	Reason      string `json:"Reason"`
}

// ==== 物流跟踪相关结构 ====

// KuaidiNiaoTrackRequest 物流跟踪请求
type KuaidiNiaoTrackRequest struct {
	ShipperCode  string `json:"ShipperCode"`            // 快递公司编码
	LogisticCode string `json:"LogisticCode"`           // 快递单号
	CustomerName string `json:"CustomerName,omitempty"` // 寄/收件人手机后四位
	Sort         int    `json:"Sort,omitempty"`         // 轨迹排序 0-升序 1-降序
	OrderCode    string `json:"OrderCode,omitempty"`    // 订单编号
}

// KuaidiNiaoTrackResponse 物流跟踪响应
type KuaidiNiaoTrackResponse struct {
	EBusinessID    string                `json:"EBusinessID"`
	ShipperCode    string                `json:"ShipperCode"`
	LogisticCode   string                `json:"LogisticCode"`
	Success        bool                  `json:"Success"`
	Reason         string                `json:"Reason"`
	State          string                `json:"State"`    // 物流状态
	StateEx        string                `json:"StateEx"`  // 详细物流状态
	Location       string                `json:"Location"` // 当前所在城市
	Traces         []KuaidiNiaoTrackItem `json:"Traces"`   // 轨迹列表
	OrderCode      string                `json:"OrderCode"`
	Station        string                `json:"Station"`        // 派件网点名称
	StationTel     string                `json:"StationTel"`     // 派件网点电话
	StationAdd     string                `json:"StationAdd"`     // 派件网点地址
	DeliveryMan    string                `json:"DeliveryMan"`    // 派件快递员
	DeliveryManTel string                `json:"DeliveryManTel"` // 派件快递员手机号
	NextCity       string                `json:"NextCity"`       // 下一站城市
}

// KuaidiNiaoTrackItem 物流轨迹项
type KuaidiNiaoTrackItem struct {
	AcceptTime    string `json:"AcceptTime"`    // 轨迹发生时间
	AcceptStation string `json:"AcceptStation"` // 轨迹描述
	Location      string `json:"Location"`      // 所在城市
	Action        string `json:"Action"`        // 状态码
	Remark        string `json:"Remark"`        // 备注
}

// 快递鸟状态码映射
var KuaidiNiaoStatusMapping = map[string]string{
	"99":  "dispatch_failed",   // 调度失败
	"203": "cancelled",         // 取消状态
	"102": "outlet_assigned",   // 推送网点信息
	"103": "courier_assigned",  // 推送快递员信息
	"104": "picked_up",         // 推送取件状态
	"301": "collected",         // 推送揽件状态
	"2":   "in_transit",        // 推送在途中状态
	"3":   "delivered",         // 推送签收状态
	"402": "order_modified",    // 推送修改订单结果
	"109": "reassigned",        // 推送调派通知
	"110": "time_changed",      // 推送预约时间变更
	"302": "tracking_updated",  // 推送更换运单号
	"206": "fake_pickup",       // 推送虚假揽件状态
	"207": "offline_charge",    // 推送线下收费状态
	"208": "weight_corrected",  // 推送重量修正结果
	"601": "billing_updated",   // 推送订单费用状态(申通)
	"401": "ticket_processed",  // 推送工单处理结果
	"501": "compensation_paid", // 推送工单赔付结果
	"502": "subpackage_info",   // 推送子母件信息
	"701": "payment_result",    // 获取平台支付结果
	"209": "unpaid_cancelled",  // 揽件未支付取消状态
}

// 快递公司编码映射
var KuaidiNiaoExpressMapping = map[string]string{
	"SF":      "顺丰速运",
	"JD":      "京东物流",
	"ZTO":     "中通快递",
	"YTO":     "圆通速递",
	"YD":      "韵达速递",
	"DBL":     "德邦快递",
	"STO":     "申通快递",
	"JTSD":    "极兔速递",
	"EMS":     "邮政速递",
	"CNSD":    "菜鸟速递",
	"CAINIAO": "菜鸟裹裹", // 🔧 修改：统一使用CAINIAO代码
}
