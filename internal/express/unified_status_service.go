package express

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"go.uber.org/zap"
)

// EntityType 实体类型
type EntityType string

const (
	EntityTypeProvider EntityType = "provider"
	EntityTypeCompany  EntityType = "company"
	EntityTypeMapping  EntityType = "mapping"
)

// StatusAction 状态操作类型
type StatusAction string

const (
	StatusActionEnable      StatusAction = "enable"
	StatusActionDisable     StatusAction = "disable"
	StatusActionMaintenance StatusAction = "maintenance"
)

// UnifiedStatus 统一状态模型
type UnifiedStatus struct {
	EntityType    EntityType `json:"entity_type"`
	EntityID      string     `json:"entity_id"`
	EntityCode    string     `json:"entity_code"`
	EntityName    string     `json:"entity_name"`
	IsActive      bool       `json:"is_active"`
	IsMaintenance bool       `json:"is_maintenance"`
	LastChanged   time.Time  `json:"last_changed"`
	ChangedBy     string     `json:"changed_by"`
	Reason        string     `json:"reason"`
}

// StatusChangeEvent 状态变更事件
type StatusChangeEvent struct {
	EventID    string                 `json:"event_id"`
	EventType  StatusAction           `json:"event_type"`
	EntityType EntityType             `json:"entity_type"`
	EntityID   string                 `json:"entity_id"`
	OldStatus  UnifiedStatus          `json:"old_status"`
	NewStatus  UnifiedStatus          `json:"new_status"`
	Metadata   map[string]interface{} `json:"metadata"`
	Timestamp  time.Time              `json:"timestamp"`
	OperatorID string                 `json:"operator_id"`
}

// BatchStatusRequest 批量状态操作请求
type BatchStatusRequest struct {
	EntityType EntityType   `json:"entity_type"`
	EntityIDs  []string     `json:"entity_ids"`
	Action     StatusAction `json:"action"`
	Reason     string       `json:"reason"`
}

// BatchStatusResponse 批量状态操作响应
type BatchStatusResponse struct {
	TotalCount   int                    `json:"total_count"`
	SuccessCount int                    `json:"success_count"`
	FailedCount  int                    `json:"failed_count"`
	Results      []BatchOperationResult `json:"results"`
}

// BatchOperationResult 批量操作结果
type BatchOperationResult struct {
	EntityID string `json:"entity_id"`
	Success  bool   `json:"success"`
	Error    string `json:"error,omitempty"`
}

// StatusOverview 状态总览
type StatusOverview struct {
	Providers struct {
		Total    int `json:"total"`
		Active   int `json:"active"`
		Inactive int `json:"inactive"`
	} `json:"providers"`
	Companies struct {
		Total    int `json:"total"`
		Active   int `json:"active"`
		Inactive int `json:"inactive"`
	} `json:"companies"`
	Mappings struct {
		Total       int `json:"total"`
		Supported   int `json:"supported"`
		Unsupported int `json:"unsupported"`
	} `json:"mappings"`
	LastUpdated time.Time `json:"last_updated"`
}

// UnifiedStatusService 统一状态管理服务接口
type UnifiedStatusService interface {
	// 状态查询
	GetStatusOverview(ctx context.Context) (*StatusOverview, error)
	GetProviderStatuses(ctx context.Context) ([]*UnifiedStatus, error)
	GetCompanyStatuses(ctx context.Context) ([]*UnifiedStatus, error)
	GetMappingStatuses(ctx context.Context) ([]*UnifiedStatus, error)
	GetEntityStatus(ctx context.Context, entityType EntityType, entityID string) (*UnifiedStatus, error)

	// 单个状态控制
	EnableProvider(ctx context.Context, providerCode, operatorID, reason string) error
	DisableProvider(ctx context.Context, providerCode, operatorID, reason string) error
	EnableCompany(ctx context.Context, companyCode, operatorID, reason string) error
	DisableCompany(ctx context.Context, companyCode, operatorID, reason string) error
	EnableMapping(ctx context.Context, mappingID, operatorID, reason string) error
	DisableMapping(ctx context.Context, mappingID, operatorID, reason string) error

	// 批量状态控制
	BatchUpdateStatus(ctx context.Context, req BatchStatusRequest, operatorID string) (*BatchStatusResponse, error)

	// 全局控制
	EnableAllServices(ctx context.Context, operatorID, reason string) error
	DisableAllServices(ctx context.Context, operatorID, reason string) error
	SetMaintenanceMode(ctx context.Context, enabled bool, operatorID, reason string) error

	// 状态检查
	IsProviderEnabled(ctx context.Context, providerCode string) (bool, error)
	IsCompanyEnabled(ctx context.Context, companyCode string) (bool, error)
	IsMappingEnabled(ctx context.Context, companyCode, providerCode string) (bool, error)
	IsServiceAvailable(ctx context.Context, companyCode, providerCode string) (bool, string, error)
}

// StatusEventPublisher 状态事件发布器接口
type StatusEventPublisher interface {
	PublishStatusChangeEvent(event *StatusChangeEvent) error
}

// CacheSyncManager 缓存同步管理器接口
type CacheSyncManager interface {
	SyncProviderStatus(providerCode string, isActive bool) error
	SyncCompanyStatus(companyCode string, isActive bool) error
	SyncMappingStatus(companyCode, providerCode string, isSupported bool) error
	InvalidateAllCaches() error
}

// SystemConfigService 系统配置服务接口（简化版）
type SystemConfigService interface {
	GetConfig(ctx context.Context, group, key string) (string, error)
	SetConfig(ctx context.Context, key, value, operatorID string) error
	GetBoolConfig(ctx context.Context, group, key string, defaultValue bool) (bool, error)
}

// DefaultUnifiedStatusService 默认统一状态管理服务实现
type DefaultUnifiedStatusService struct {
	repository    ExpressCompanyRepository
	configService SystemConfigService
	eventBus      StatusEventPublisher
	cacheManager  CacheSyncManager
	logger        *zap.Logger
}

// NewUnifiedStatusService 创建统一状态管理服务
func NewUnifiedStatusService(
	repository ExpressCompanyRepository,
	configService SystemConfigService,
	eventBus StatusEventPublisher,
	cacheManager CacheSyncManager,
	logger *zap.Logger,
) UnifiedStatusService {
	return &DefaultUnifiedStatusService{
		repository:    repository,
		configService: configService,
		eventBus:      eventBus,
		cacheManager:  cacheManager,
		logger:        logger,
	}
}

// GetStatusOverview 获取状态总览
func (s *DefaultUnifiedStatusService) GetStatusOverview(ctx context.Context) (*StatusOverview, error) {
	overview := &StatusOverview{
		LastUpdated: time.Now(),
	}

	// 获取供应商状态统计
	providers, err := s.repository.GetProviders(ProviderFilter{}, Pagination{Page: 1, PageSize: 100})
	if err != nil {
		return nil, fmt.Errorf("获取供应商列表失败: %w", err)
	}
	overview.Providers.Total = len(providers.Providers)

	// 统计供应商状态（从system_configs获取）
	for _, provider := range providers.Providers {
		isEnabled, _ := s.IsProviderEnabled(ctx, provider.Code)
		if isEnabled {
			overview.Providers.Active++
		} else {
			overview.Providers.Inactive++
		}
	}

	// 获取快递公司状态统计
	companies, err := s.repository.GetCompanies(CompanyFilter{}, Pagination{Page: 1, PageSize: 100})
	if err != nil {
		return nil, fmt.Errorf("获取快递公司列表失败: %w", err)
	}
	overview.Companies.Total = len(companies.Companies)
	for _, company := range companies.Companies {
		if company.IsActive {
			overview.Companies.Active++
		} else {
			overview.Companies.Inactive++
		}
	}

	// 获取映射关系状态统计
	mappings, err := s.repository.GetMappings(MappingFilter{}, Pagination{Page: 1, PageSize: 1000})
	if err != nil {
		return nil, fmt.Errorf("获取映射关系列表失败: %w", err)
	}
	overview.Mappings.Total = len(mappings.Mappings)
	for _, mapping := range mappings.Mappings {
		if mapping.IsSupported {
			overview.Mappings.Supported++
		} else {
			overview.Mappings.Unsupported++
		}
	}

	return overview, nil
}

// GetProviderStatuses 获取供应商状态列表
func (s *DefaultUnifiedStatusService) GetProviderStatuses(ctx context.Context) ([]*UnifiedStatus, error) {
	providers, err := s.repository.GetProviders(ProviderFilter{}, Pagination{Page: 1, PageSize: 100})
	if err != nil {
		return nil, fmt.Errorf("获取供应商列表失败: %w", err)
	}

	var statuses []*UnifiedStatus
	for _, provider := range providers.Providers {
		isEnabled, _ := s.IsProviderEnabled(ctx, provider.Code)

		status := &UnifiedStatus{
			EntityType:    EntityTypeProvider,
			EntityID:      provider.ID,
			EntityCode:    provider.Code,
			EntityName:    provider.Name,
			IsActive:      isEnabled,
			IsMaintenance: false, // TODO: 从配置中获取维护模式状态
			LastChanged:   provider.UpdatedAt,
		}

		if provider.UpdatedBy != nil {
			status.ChangedBy = *provider.UpdatedBy
		}

		statuses = append(statuses, status)
	}

	return statuses, nil
}

// GetCompanyStatuses 获取快递公司状态列表
func (s *DefaultUnifiedStatusService) GetCompanyStatuses(ctx context.Context) ([]*UnifiedStatus, error) {
	companies, err := s.repository.GetCompanies(CompanyFilter{}, Pagination{Page: 1, PageSize: 100})
	if err != nil {
		return nil, fmt.Errorf("获取快递公司列表失败: %w", err)
	}

	var statuses []*UnifiedStatus
	for _, company := range companies.Companies {
		status := &UnifiedStatus{
			EntityType:    EntityTypeCompany,
			EntityID:      company.ID,
			EntityCode:    company.Code,
			EntityName:    company.Name,
			IsActive:      company.IsActive,
			IsMaintenance: false, // TODO: 从配置中获取维护模式状态
			LastChanged:   company.UpdatedAt,
		}

		if company.UpdatedBy != nil {
			status.ChangedBy = *company.UpdatedBy
		}

		statuses = append(statuses, status)
	}

	return statuses, nil
}

// GetMappingStatuses 获取映射关系状态列表
func (s *DefaultUnifiedStatusService) GetMappingStatuses(ctx context.Context) ([]*UnifiedStatus, error) {
	mappings, err := s.repository.GetMappings(MappingFilter{}, Pagination{Page: 1, PageSize: 1000})
	if err != nil {
		return nil, fmt.Errorf("获取映射关系列表失败: %w", err)
	}

	var statuses []*UnifiedStatus
	for _, mapping := range mappings.Mappings {
		// 获取关联的快递公司和供应商信息
		company, err := s.repository.GetCompanyByID(mapping.CompanyID)
		if err != nil {
			s.logger.Warn("获取快递公司信息失败", zap.String("company_id", mapping.CompanyID), zap.Error(err))
			continue
		}

		provider, err := s.repository.GetProviderByID(mapping.ProviderID)
		if err != nil {
			s.logger.Warn("获取供应商信息失败", zap.String("provider_id", mapping.ProviderID), zap.Error(err))
			continue
		}

		status := &UnifiedStatus{
			EntityType:    EntityTypeMapping,
			EntityID:      mapping.ID,
			EntityCode:    fmt.Sprintf("%s-%s", company.Code, provider.Code),
			EntityName:    fmt.Sprintf("%s ↔ %s", company.Name, provider.Name),
			IsActive:      mapping.IsSupported,
			IsMaintenance: false,
			LastChanged:   mapping.UpdatedAt,
		}

		if mapping.UpdatedBy != nil {
			status.ChangedBy = *mapping.UpdatedBy
		}

		statuses = append(statuses, status)
	}

	return statuses, nil
}

// EnableProvider 启用供应商
func (s *DefaultUnifiedStatusService) EnableProvider(ctx context.Context, providerCode, operatorID, reason string) error {
	return s.updateProviderStatus(ctx, providerCode, true, operatorID, reason)
}

// DisableProvider 禁用供应商
func (s *DefaultUnifiedStatusService) DisableProvider(ctx context.Context, providerCode, operatorID, reason string) error {
	return s.updateProviderStatus(ctx, providerCode, false, operatorID, reason)
}

// updateProviderStatus 更新供应商状态
func (s *DefaultUnifiedStatusService) updateProviderStatus(ctx context.Context, providerCode string, isActive bool, operatorID, reason string) error {
	// 获取供应商信息
	provider, err := s.repository.GetProviderByCode(providerCode)
	if err != nil {
		return fmt.Errorf("供应商不存在: %w", err)
	}

	// 获取当前状态
	currentStatus, _ := s.IsProviderEnabled(ctx, providerCode)

	// 如果状态没有变化，直接返回
	if currentStatus == isActive {
		s.logger.Info("供应商状态无变化，跳过更新",
			zap.String("provider_code", providerCode),
			zap.Bool("is_active", isActive))
		return nil
	}

	// 更新system_configs中的供应商状态
	configKey := fmt.Sprintf("provider_%s_enabled", providerCode)
	configValue := "false"
	if isActive {
		configValue = "true"
	}

	err = s.configService.SetConfig(ctx, configKey, configValue, operatorID)
	if err != nil {
		return fmt.Errorf("更新供应商状态配置失败: %w", err)
	}

	// 发布状态变更事件
	event := &StatusChangeEvent{
		EventID:    uuid.New().String(),
		EventType:  StatusActionEnable,
		EntityType: EntityTypeProvider,
		EntityID:   provider.ID,
		OldStatus: UnifiedStatus{
			EntityType: EntityTypeProvider,
			EntityID:   provider.ID,
			EntityCode: provider.Code,
			EntityName: provider.Name,
			IsActive:   currentStatus,
		},
		NewStatus: UnifiedStatus{
			EntityType: EntityTypeProvider,
			EntityID:   provider.ID,
			EntityCode: provider.Code,
			EntityName: provider.Name,
			IsActive:   isActive,
		},
		Metadata: map[string]interface{}{
			"reason": reason,
		},
		Timestamp:  time.Now(),
		OperatorID: operatorID,
	}

	if !isActive {
		event.EventType = StatusActionDisable
	}

	if s.eventBus != nil {
		if err := s.eventBus.PublishStatusChangeEvent(event); err != nil {
			s.logger.Warn("发布状态变更事件失败", zap.Error(err))
		}
	}

	// 同步缓存
	if s.cacheManager != nil {
		if err := s.cacheManager.SyncProviderStatus(providerCode, isActive); err != nil {
			s.logger.Warn("同步供应商状态缓存失败", zap.Error(err))
		}
	}

	s.logger.Info("供应商状态更新成功",
		zap.String("provider_code", providerCode),
		zap.Bool("is_active", isActive),
		zap.String("operator_id", operatorID),
		zap.String("reason", reason))

	return nil
}

// EnableCompany 启用快递公司
func (s *DefaultUnifiedStatusService) EnableCompany(ctx context.Context, companyCode, operatorID, reason string) error {
	return s.updateCompanyStatus(ctx, companyCode, true, operatorID, reason)
}

// DisableCompany 禁用快递公司
func (s *DefaultUnifiedStatusService) DisableCompany(ctx context.Context, companyCode, operatorID, reason string) error {
	return s.updateCompanyStatus(ctx, companyCode, false, operatorID, reason)
}

// updateCompanyStatus 更新快递公司状态
func (s *DefaultUnifiedStatusService) updateCompanyStatus(ctx context.Context, companyCode string, isActive bool, operatorID, reason string) error {
	// 获取快递公司信息
	company, err := s.repository.GetCompanyByCode(companyCode)
	if err != nil {
		return fmt.Errorf("快递公司不存在: %w", err)
	}

	// 如果状态没有变化，直接返回
	if company.IsActive == isActive {
		s.logger.Info("快递公司状态无变化，跳过更新",
			zap.String("company_code", companyCode),
			zap.Bool("is_active", isActive))
		return nil
	}

	// 更新快递公司状态
	company.IsActive = isActive
	company.UpdatedBy = &operatorID

	err = s.repository.UpdateCompany(company)
	if err != nil {
		return fmt.Errorf("更新快递公司状态失败: %w", err)
	}

	// 发布状态变更事件
	event := &StatusChangeEvent{
		EventID:    uuid.New().String(),
		EventType:  StatusActionEnable,
		EntityType: EntityTypeCompany,
		EntityID:   company.ID,
		OldStatus: UnifiedStatus{
			EntityType: EntityTypeCompany,
			EntityID:   company.ID,
			EntityCode: company.Code,
			EntityName: company.Name,
			IsActive:   !isActive,
		},
		NewStatus: UnifiedStatus{
			EntityType: EntityTypeCompany,
			EntityID:   company.ID,
			EntityCode: company.Code,
			EntityName: company.Name,
			IsActive:   isActive,
		},
		Metadata: map[string]interface{}{
			"reason": reason,
		},
		Timestamp:  time.Now(),
		OperatorID: operatorID,
	}

	if !isActive {
		event.EventType = StatusActionDisable
	}

	if s.eventBus != nil {
		if err := s.eventBus.PublishStatusChangeEvent(event); err != nil {
			s.logger.Warn("发布状态变更事件失败", zap.Error(err))
		}
	}

	// 同步缓存
	if s.cacheManager != nil {
		if err := s.cacheManager.SyncCompanyStatus(companyCode, isActive); err != nil {
			s.logger.Warn("同步快递公司状态缓存失败", zap.Error(err))
		}
	}

	s.logger.Info("快递公司状态更新成功",
		zap.String("company_code", companyCode),
		zap.Bool("is_active", isActive),
		zap.String("operator_id", operatorID),
		zap.String("reason", reason))

	return nil
}

// EnableMapping 启用映射关系
func (s *DefaultUnifiedStatusService) EnableMapping(ctx context.Context, mappingID, operatorID, reason string) error {
	return s.updateMappingStatus(ctx, mappingID, true, operatorID, reason)
}

// DisableMapping 禁用映射关系
func (s *DefaultUnifiedStatusService) DisableMapping(ctx context.Context, mappingID, operatorID, reason string) error {
	return s.updateMappingStatus(ctx, mappingID, false, operatorID, reason)
}

// updateMappingStatus 更新映射关系状态
func (s *DefaultUnifiedStatusService) updateMappingStatus(ctx context.Context, mappingID string, isSupported bool, operatorID, reason string) error {
	// 获取映射关系信息
	mapping, err := s.repository.GetMappingByID(mappingID)
	if err != nil {
		return fmt.Errorf("映射关系不存在: %w", err)
	}

	// 如果状态没有变化，直接返回
	if mapping.IsSupported == isSupported {
		s.logger.Info("映射关系状态无变化，跳过更新",
			zap.String("mapping_id", mappingID),
			zap.Bool("is_supported", isSupported))
		return nil
	}

	// 更新映射关系状态
	mapping.IsSupported = isSupported
	mapping.UpdatedBy = &operatorID

	err = s.repository.UpdateMapping(mapping)
	if err != nil {
		return fmt.Errorf("更新映射关系状态失败: %w", err)
	}

	// 获取关联的快递公司和供应商信息用于事件
	company, _ := s.repository.GetCompanyByID(mapping.CompanyID)
	provider, _ := s.repository.GetProviderByID(mapping.ProviderID)

	// 发布状态变更事件
	event := &StatusChangeEvent{
		EventID:    uuid.New().String(),
		EventType:  StatusActionEnable,
		EntityType: EntityTypeMapping,
		EntityID:   mapping.ID,
		OldStatus: UnifiedStatus{
			EntityType: EntityTypeMapping,
			EntityID:   mapping.ID,
			IsActive:   !isSupported,
		},
		NewStatus: UnifiedStatus{
			EntityType: EntityTypeMapping,
			EntityID:   mapping.ID,
			IsActive:   isSupported,
		},
		Metadata: map[string]interface{}{
			"reason": reason,
		},
		Timestamp:  time.Now(),
		OperatorID: operatorID,
	}

	if company != nil && provider != nil {
		event.OldStatus.EntityCode = fmt.Sprintf("%s-%s", company.Code, provider.Code)
		event.OldStatus.EntityName = fmt.Sprintf("%s ↔ %s", company.Name, provider.Name)
		event.NewStatus.EntityCode = fmt.Sprintf("%s-%s", company.Code, provider.Code)
		event.NewStatus.EntityName = fmt.Sprintf("%s ↔ %s", company.Name, provider.Name)
	}

	if !isSupported {
		event.EventType = StatusActionDisable
	}

	if s.eventBus != nil {
		if err := s.eventBus.PublishStatusChangeEvent(event); err != nil {
			s.logger.Warn("发布状态变更事件失败", zap.Error(err))
		}
	}

	// 同步缓存
	if s.cacheManager != nil && company != nil && provider != nil {
		if err := s.cacheManager.SyncMappingStatus(company.Code, provider.Code, isSupported); err != nil {
			s.logger.Warn("同步映射关系状态缓存失败", zap.Error(err))
		}
	}

	s.logger.Info("映射关系状态更新成功",
		zap.String("mapping_id", mappingID),
		zap.Bool("is_supported", isSupported),
		zap.String("operator_id", operatorID),
		zap.String("reason", reason))

	return nil
}

// IsProviderEnabled 检查供应商是否启用
func (s *DefaultUnifiedStatusService) IsProviderEnabled(ctx context.Context, providerCode string) (bool, error) {
	// 从system_configs获取供应商状态
	configKey := fmt.Sprintf("provider_%s_enabled", providerCode)
	return s.configService.GetBoolConfig(ctx, "provider", configKey, true) // 默认启用
}

// IsCompanyEnabled 检查快递公司是否启用
func (s *DefaultUnifiedStatusService) IsCompanyEnabled(ctx context.Context, companyCode string) (bool, error) {
	company, err := s.repository.GetCompanyByCode(companyCode)
	if err != nil {
		return false, fmt.Errorf("快递公司不存在: %w", err)
	}
	return company.IsActive, nil
}

// IsMappingEnabled 检查映射关系是否启用
func (s *DefaultUnifiedStatusService) IsMappingEnabled(ctx context.Context, companyCode, providerCode string) (bool, error) {
	// 获取快递公司
	company, err := s.repository.GetCompanyByCode(companyCode)
	if err != nil {
		return false, fmt.Errorf("快递公司不存在: %w", err)
	}

	// 获取供应商
	provider, err := s.repository.GetProviderByCode(providerCode)
	if err != nil {
		return false, fmt.Errorf("供应商不存在: %w", err)
	}

	// 获取映射关系
	mapping, err := s.repository.GetMapping(company.ID, provider.ID)
	if err != nil {
		return false, fmt.Errorf("映射关系不存在: %w", err)
	}

	return mapping.IsSupported, nil
}

// IsServiceAvailable 检查服务是否可用（综合检查）
func (s *DefaultUnifiedStatusService) IsServiceAvailable(ctx context.Context, companyCode, providerCode string) (bool, string, error) {
	// 1. 检查供应商状态
	providerEnabled, err := s.IsProviderEnabled(ctx, providerCode)
	if err != nil {
		return false, fmt.Sprintf("供应商状态检查失败: %v", err), err
	}
	if !providerEnabled {
		return false, fmt.Sprintf("供应商 %s 已禁用", providerCode), nil
	}

	// 2. 检查快递公司状态
	companyEnabled, err := s.IsCompanyEnabled(ctx, companyCode)
	if err != nil {
		return false, fmt.Sprintf("快递公司状态检查失败: %v", err), err
	}
	if !companyEnabled {
		return false, fmt.Sprintf("快递公司 %s 已禁用", companyCode), nil
	}

	// 3. 检查映射关系状态
	mappingEnabled, err := s.IsMappingEnabled(ctx, companyCode, providerCode)
	if err != nil {
		return false, fmt.Sprintf("映射关系检查失败: %v", err), err
	}
	if !mappingEnabled {
		return false, fmt.Sprintf("供应商 %s 不支持快递公司 %s", providerCode, companyCode), nil
	}

	return true, "服务可用", nil
}

// BatchUpdateStatus 批量更新状态
func (s *DefaultUnifiedStatusService) BatchUpdateStatus(ctx context.Context, req BatchStatusRequest, operatorID string) (*BatchStatusResponse, error) {
	response := &BatchStatusResponse{
		TotalCount: len(req.EntityIDs),
		Results:    make([]BatchOperationResult, 0, len(req.EntityIDs)),
	}

	for _, entityID := range req.EntityIDs {
		result := BatchOperationResult{
			EntityID: entityID,
			Success:  true,
		}

		var err error
		switch req.EntityType {
		case EntityTypeProvider:
			// 对于供应商，entityID实际是providerCode
			if req.Action == StatusActionEnable {
				err = s.EnableProvider(ctx, entityID, operatorID, req.Reason)
			} else if req.Action == StatusActionDisable {
				err = s.DisableProvider(ctx, entityID, operatorID, req.Reason)
			} else {
				err = fmt.Errorf("不支持的操作: %s", req.Action)
			}

		case EntityTypeCompany:
			// 对于快递公司，entityID实际是companyCode
			if req.Action == StatusActionEnable {
				err = s.EnableCompany(ctx, entityID, operatorID, req.Reason)
			} else if req.Action == StatusActionDisable {
				err = s.DisableCompany(ctx, entityID, operatorID, req.Reason)
			} else {
				err = fmt.Errorf("不支持的操作: %s", req.Action)
			}

		case EntityTypeMapping:
			// 对于映射关系，entityID是mappingID
			if req.Action == StatusActionEnable {
				err = s.EnableMapping(ctx, entityID, operatorID, req.Reason)
			} else if req.Action == StatusActionDisable {
				err = s.DisableMapping(ctx, entityID, operatorID, req.Reason)
			} else {
				err = fmt.Errorf("不支持的操作: %s", req.Action)
			}

		default:
			err = fmt.Errorf("不支持的实体类型: %s", req.EntityType)
		}

		if err != nil {
			result.Success = false
			result.Error = err.Error()
			response.FailedCount++
		} else {
			response.SuccessCount++
		}

		response.Results = append(response.Results, result)
	}

	s.logger.Info("批量状态更新完成",
		zap.String("entity_type", string(req.EntityType)),
		zap.String("action", string(req.Action)),
		zap.Int("total", response.TotalCount),
		zap.Int("success", response.SuccessCount),
		zap.Int("failed", response.FailedCount),
		zap.String("operator_id", operatorID))

	return response, nil
}

// GetEntityStatus 获取实体状态
func (s *DefaultUnifiedStatusService) GetEntityStatus(ctx context.Context, entityType EntityType, entityID string) (*UnifiedStatus, error) {
	switch entityType {
	case EntityTypeProvider:
		provider, err := s.repository.GetProviderByCode(entityID)
		if err != nil {
			return nil, fmt.Errorf("供应商不存在: %w", err)
		}

		isEnabled, _ := s.IsProviderEnabled(ctx, provider.Code)

		status := &UnifiedStatus{
			EntityType:  EntityTypeProvider,
			EntityID:    provider.ID,
			EntityCode:  provider.Code,
			EntityName:  provider.Name,
			IsActive:    isEnabled,
			LastChanged: provider.UpdatedAt,
		}

		if provider.UpdatedBy != nil {
			status.ChangedBy = *provider.UpdatedBy
		}

		return status, nil

	case EntityTypeCompany:
		company, err := s.repository.GetCompanyByCode(entityID)
		if err != nil {
			return nil, fmt.Errorf("快递公司不存在: %w", err)
		}

		status := &UnifiedStatus{
			EntityType:  EntityTypeCompany,
			EntityID:    company.ID,
			EntityCode:  company.Code,
			EntityName:  company.Name,
			IsActive:    company.IsActive,
			LastChanged: company.UpdatedAt,
		}

		if company.UpdatedBy != nil {
			status.ChangedBy = *company.UpdatedBy
		}

		return status, nil

	case EntityTypeMapping:
		mapping, err := s.repository.GetMappingByID(entityID)
		if err != nil {
			return nil, fmt.Errorf("映射关系不存在: %w", err)
		}

		// 获取关联的快递公司和供应商信息
		company, _ := s.repository.GetCompanyByID(mapping.CompanyID)
		provider, _ := s.repository.GetProviderByID(mapping.ProviderID)

		status := &UnifiedStatus{
			EntityType:  EntityTypeMapping,
			EntityID:    mapping.ID,
			IsActive:    mapping.IsSupported,
			LastChanged: mapping.UpdatedAt,
		}

		if company != nil && provider != nil {
			status.EntityCode = fmt.Sprintf("%s-%s", company.Code, provider.Code)
			status.EntityName = fmt.Sprintf("%s ↔ %s", company.Name, provider.Name)
		}

		if mapping.UpdatedBy != nil {
			status.ChangedBy = *mapping.UpdatedBy
		}

		return status, nil

	default:
		return nil, fmt.Errorf("不支持的实体类型: %s", entityType)
	}
}

// EnableAllServices 启用所有服务
func (s *DefaultUnifiedStatusService) EnableAllServices(ctx context.Context, operatorID, reason string) error {
	s.logger.Info("开始启用所有服务", zap.String("operator_id", operatorID), zap.String("reason", reason))

	// 1. 启用所有供应商
	providers, err := s.repository.GetProviders(ProviderFilter{}, Pagination{Page: 1, PageSize: 100})
	if err != nil {
		return fmt.Errorf("获取供应商列表失败: %w", err)
	}

	for _, provider := range providers.Providers {
		if err := s.EnableProvider(ctx, provider.Code, operatorID, reason); err != nil {
			s.logger.Warn("启用供应商失败", zap.String("provider_code", provider.Code), zap.Error(err))
		}
	}

	// 2. 启用所有快递公司
	companies, err := s.repository.GetCompanies(CompanyFilter{}, Pagination{Page: 1, PageSize: 100})
	if err != nil {
		return fmt.Errorf("获取快递公司列表失败: %w", err)
	}

	for _, company := range companies.Companies {
		if err := s.EnableCompany(ctx, company.Code, operatorID, reason); err != nil {
			s.logger.Warn("启用快递公司失败", zap.String("company_code", company.Code), zap.Error(err))
		}
	}

	// 3. 启用所有映射关系
	mappings, err := s.repository.GetMappings(MappingFilter{}, Pagination{Page: 1, PageSize: 1000})
	if err != nil {
		return fmt.Errorf("获取映射关系列表失败: %w", err)
	}

	for _, mapping := range mappings.Mappings {
		if err := s.EnableMapping(ctx, mapping.ID, operatorID, reason); err != nil {
			s.logger.Warn("启用映射关系失败", zap.String("mapping_id", mapping.ID), zap.Error(err))
		}
	}

	s.logger.Info("启用所有服务完成", zap.String("operator_id", operatorID))
	return nil
}

// DisableAllServices 禁用所有服务
func (s *DefaultUnifiedStatusService) DisableAllServices(ctx context.Context, operatorID, reason string) error {
	s.logger.Info("开始禁用所有服务", zap.String("operator_id", operatorID), zap.String("reason", reason))

	// 1. 禁用所有供应商
	providers, err := s.repository.GetProviders(ProviderFilter{}, Pagination{Page: 1, PageSize: 100})
	if err != nil {
		return fmt.Errorf("获取供应商列表失败: %w", err)
	}

	for _, provider := range providers.Providers {
		if err := s.DisableProvider(ctx, provider.Code, operatorID, reason); err != nil {
			s.logger.Warn("禁用供应商失败", zap.String("provider_code", provider.Code), zap.Error(err))
		}
	}

	// 2. 禁用所有快递公司
	companies, err := s.repository.GetCompanies(CompanyFilter{}, Pagination{Page: 1, PageSize: 100})
	if err != nil {
		return fmt.Errorf("获取快递公司列表失败: %w", err)
	}

	for _, company := range companies.Companies {
		if err := s.DisableCompany(ctx, company.Code, operatorID, reason); err != nil {
			s.logger.Warn("禁用快递公司失败", zap.String("company_code", company.Code), zap.Error(err))
		}
	}

	s.logger.Info("禁用所有服务完成", zap.String("operator_id", operatorID))
	return nil
}

// SetMaintenanceMode 设置维护模式
func (s *DefaultUnifiedStatusService) SetMaintenanceMode(ctx context.Context, enabled bool, operatorID, reason string) error {
	s.logger.Info("设置维护模式",
		zap.Bool("enabled", enabled),
		zap.String("operator_id", operatorID),
		zap.String("reason", reason))

	// 设置全局维护模式配置
	configValue := "false"
	if enabled {
		configValue = "true"
	}

	err := s.configService.SetConfig(ctx, "system.maintenance_mode", configValue, operatorID)
	if err != nil {
		return fmt.Errorf("设置维护模式配置失败: %w", err)
	}

	// 如果启用维护模式，禁用所有服务
	if enabled {
		return s.DisableAllServices(ctx, operatorID, fmt.Sprintf("维护模式: %s", reason))
	}

	s.logger.Info("维护模式设置完成", zap.Bool("enabled", enabled))
	return nil
}
