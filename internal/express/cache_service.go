package express

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"sync"
	"time"

	"go.uber.org/zap"
)

// ExpressMappingCache 快递公司映射缓存
type ExpressMappingCache struct {
	CompanyCode         string            `json:"company_code"`
	CompanyName         string            `json:"company_name"`
	ProviderCode        string            `json:"provider_code"`
	ProviderCompanyCode string            `json:"provider_company_code"`
	IsSupported         bool              `json:"is_supported"`
	IsPreferred         bool              `json:"is_preferred"`
	VolumeWeightRatio   int               `json:"volume_weight_ratio"`
	MaxWeightKg         *float64          `json:"max_weight_kg,omitempty"`
	ProductTypeMappings map[string]string `json:"product_type_mappings,omitempty"`
	PayMethodMappings   map[string]string `json:"pay_method_mappings,omitempty"`
	StatusCodeMappings  map[string]string `json:"status_code_mappings,omitempty"`
	SupportedServices   []string          `json:"supported_services,omitempty"`
	CacheExpiresAt      time.Time         `json:"cache_expires_at"`
}

// ExpressMappingCacheService 快递公司映射缓存服务
type ExpressMappingCacheService struct {
	repository ExpressCompanyRepository
	logger     *zap.Logger

	// 内存缓存
	memoryCache map[string]*ExpressMappingCache
	cacheMutex  sync.RWMutex

	// 🔧 修复：添加快递公司接口类型缓存，避免重复查询
	interfaceTypeCache map[string]bool
	interfaceTypeMutex sync.RWMutex

	// 🚀 新增：供应商支持的快递公司缓存，避免重复数据库查询
	providerCompaniesCache map[string][]*ExpressMappingCache
	providerCachesMutex    sync.RWMutex

	// 🚀 新增：供应商映射缓存过期时间跟踪
	providerCacheExpiry map[string]time.Time

	// 🚀 新增：全局映射缓存，用于快速预过滤
	globalMappingCache map[string]map[string]bool // provider -> expressCode -> isSupported
	globalCacheMutex   sync.RWMutex
	globalCacheExpiry  time.Time

	// 缓存配置
	cacheExpiration         time.Duration
	refreshInterval         time.Duration
	providerCacheExpiration time.Duration // 供应商缓存过期时间

	// 停止信号
	stopChan chan struct{}
	wg       sync.WaitGroup
}

// NewExpressMappingCacheService 创建快递公司映射缓存服务
func NewExpressMappingCacheService(repository ExpressCompanyRepository, logger *zap.Logger) *ExpressMappingCacheService {
	service := &ExpressMappingCacheService{
		repository:              repository,
		logger:                  logger,
		memoryCache:             make(map[string]*ExpressMappingCache),
		interfaceTypeCache:      make(map[string]bool),                   // 🔧 修复：初始化接口类型缓存
		providerCompaniesCache:  make(map[string][]*ExpressMappingCache), // 🚀 新增：初始化供应商公司缓存
		providerCacheExpiry:     make(map[string]time.Time),              // 🚀 新增：初始化过期时间跟踪
		globalMappingCache:      make(map[string]map[string]bool),        // 🚀 新增：初始化全局映射缓存
		cacheExpiration:         time.Hour,                               // 缓存1小时
		refreshInterval:         time.Minute * 30,                        // 30分钟刷新一次
		providerCacheExpiration: time.Minute * 10,                        // 供应商缓存10分钟
		stopChan:                make(chan struct{}),
	}

	// 启动后台刷新任务
	service.startBackgroundRefresh()

	return service
}

// GetProviderCompanyCode 获取供应商特定的快递公司代码
func (s *ExpressMappingCacheService) GetProviderCompanyCode(ctx context.Context, companyCode, providerCode string) (string, error) {
	cacheKey := fmt.Sprintf("%s:%s", providerCode, companyCode)

	// 🔥 高性能：先从内存缓存获取
	if mapping := s.getFromMemoryCache(cacheKey); mapping != nil {
		if mapping.IsSupported {
			return mapping.ProviderCompanyCode, nil
		}
		return "", fmt.Errorf("供应商 %s 不支持快递公司 %s", providerCode, companyCode)
	}

	// 缓存未命中，从数据库获取并缓存
	mapping, err := s.loadAndCacheMapping(ctx, companyCode, providerCode)
	if err != nil {
		return "", err
	}

	if !mapping.IsSupported {
		return "", fmt.Errorf("供应商 %s 不支持快递公司 %s", providerCode, companyCode)
	}

	return mapping.ProviderCompanyCode, nil
}

// GetCompanyMapping 获取快递公司映射信息
func (s *ExpressMappingCacheService) GetCompanyMapping(ctx context.Context, companyCode, providerCode string) (*ExpressMappingCache, error) {
	cacheKey := fmt.Sprintf("%s:%s", providerCode, companyCode)

	// 先从内存缓存获取
	if mapping := s.getFromMemoryCache(cacheKey); mapping != nil {
		return mapping, nil
	}

	// 从数据库获取并缓存
	return s.loadAndCacheMapping(ctx, companyCode, providerCode)
}

// GetSupportedCompanies 获取供应商支持的快递公司列表
func (s *ExpressMappingCacheService) GetSupportedCompanies(ctx context.Context, providerCode string) ([]*ExpressMappingCache, error) {
	// 🚀 实时更新：检查缓存是否过期，确保映射变更能立即生效
	cacheKey := fmt.Sprintf("provider_companies:%s", providerCode)

	// 先检查内存缓存和过期时间
	s.providerCachesMutex.RLock()
	cachedCompanies, exists := s.providerCompaniesCache[cacheKey]
	expiry, hasExpiry := s.providerCacheExpiry[cacheKey]
	s.providerCachesMutex.RUnlock()

	// 如果缓存存在且未过期，直接返回
	if exists && hasExpiry && time.Now().Before(expiry) {
		s.logger.Info("🔍 [DEBUG] 供应商支持的快递公司缓存命中",
			zap.String("provider_code", providerCode),
			zap.Int("count", len(cachedCompanies)),
			zap.Duration("remaining_ttl", time.Until(expiry)))

		// 🔥 调试：输出缓存中的详细信息
		s.logger.Info("🔍 [DEBUG] 缓存中的快递公司详情",
			zap.String("provider_code", providerCode))
		for i, company := range cachedCompanies {
			s.logger.Info("🔍 [DEBUG] 缓存快递公司",
				zap.Int("index", i+1),
				zap.String("company_code", company.CompanyCode),
				zap.String("company_name", company.CompanyName),
				zap.Bool("is_supported", company.IsSupported))
		}

		return cachedCompanies, nil
	}

	// 缓存未命中或已过期，需要重新查询
	if exists {
		s.logger.Debug("供应商支持的快递公司缓存已过期，重新查询",
			zap.String("provider_code", providerCode))
	} else {
		s.logger.Debug("供应商支持的快递公司缓存未命中，查询数据库",
			zap.String("provider_code", providerCode))
	}

	// 缓存未命中，从数据库查询
	s.logger.Debug("供应商支持的快递公司缓存未命中，查询数据库",
		zap.String("provider_code", providerCode))

	// 获取供应商信息
	providers, err := s.repository.GetProviders(ProviderFilter{}, Pagination{Page: 1, PageSize: 100})
	if err != nil {
		return nil, fmt.Errorf("获取供应商列表失败: %w", err)
	}

	var targetProvider *ExpressProvider
	for _, provider := range providers.Providers {
		if provider.Code == providerCode {
			targetProvider = provider
			break
		}
	}

	if targetProvider == nil {
		return nil, fmt.Errorf("未找到供应商: %s", providerCode)
	}

	// 获取该供应商的所有映射关系
	mappings, err := s.repository.GetMappingsByProvider(targetProvider.ID)
	if err != nil {
		return nil, fmt.Errorf("获取供应商映射失败: %w", err)
	}

	var companies []*ExpressMappingCache
	for _, mapping := range mappings {
		// 获取快递公司信息
		company, err := s.repository.GetCompanyByID(mapping.CompanyID)
		if err != nil {
			s.logger.Warn("获取快递公司信息失败",
				zap.String("company_id", mapping.CompanyID),
				zap.Error(err))
			continue
		}

		// 🔥 性能优化：移除数据库查询瓶颈，直接跳过接口类型检查
		if mapping.IsSupported && company.IsActive {
			// 🚀 跳过接口类型检查，提升响应速度到几百毫秒
			// 原来的 shouldUseUnifiedInterface 检查是性能杀手，直接移除
			cacheMapping := &ExpressMappingCache{
				CompanyCode:         company.Code,
				CompanyName:         company.Name,
				ProviderCode:        providerCode,
				ProviderCompanyCode: mapping.ProviderCompanyCode,
				IsSupported:         mapping.IsSupported,
				IsPreferred:         mapping.IsPreferred,
				VolumeWeightRatio:   company.VolumeWeightRatio,
				MaxWeightKg:         mapping.WeightLimitKg,
				SupportedServices:   mapping.SupportedServices,
			}

			companies = append(companies, cacheMapping)
		}
	}

	// 🚀 缓存查询结果，设置过期时间，确保实时更新
	s.providerCachesMutex.Lock()
	s.providerCompaniesCache[cacheKey] = companies
	s.providerCacheExpiry[cacheKey] = time.Now().Add(s.providerCacheExpiration)
	s.providerCachesMutex.Unlock()

	s.logger.Debug("获取供应商支持的快递公司",
		zap.String("provider_code", providerCode),
		zap.Int("count", len(companies)),
		zap.Bool("excluded_jd", true),
		zap.Bool("excluded_dbl", true),
		zap.Bool("cached", true),
		zap.Duration("cache_ttl", s.providerCacheExpiration))

	return companies, nil
}

// 🚀 新增：批量获取所有供应商的映射关系，用于高性能预过滤
func (s *ExpressMappingCacheService) GetAllProviderMappings(ctx context.Context, providers []string) (map[string]map[string]bool, error) {
	// 检查全局缓存是否有效
	s.globalCacheMutex.RLock()
	if time.Now().Before(s.globalCacheExpiry) && len(s.globalMappingCache) > 0 {
		// 返回缓存副本
		result := make(map[string]map[string]bool)
		for provider, mapping := range s.globalMappingCache {
			if contains(providers, provider) {
				result[provider] = mapping
			}
		}
		s.globalCacheMutex.RUnlock()

		s.logger.Debug("全局映射缓存命中",
			zap.Int("provider_count", len(result)),
			zap.Duration("remaining_ttl", time.Until(s.globalCacheExpiry)))
		return result, nil
	}
	s.globalCacheMutex.RUnlock()

	// 缓存未命中或过期，重新构建
	s.logger.Debug("全局映射缓存未命中，重新构建")

	result := make(map[string]map[string]bool)
	var mu sync.Mutex
	var wg sync.WaitGroup

	// 并发获取所有供应商的映射
	for _, provider := range providers {
		wg.Add(1)
		go func(providerCode string) {
			defer wg.Done()

			companies, err := s.GetSupportedCompanies(ctx, providerCode)
			if err != nil {
				s.logger.Warn("获取供应商映射失败",
					zap.String("provider", providerCode),
					zap.Error(err))
				return
			}

			// 构建映射
			mapping := make(map[string]bool)
			for _, company := range companies {
				mapping[company.CompanyCode] = company.IsSupported
			}

			mu.Lock()
			result[providerCode] = mapping
			mu.Unlock()
		}(provider)
	}

	wg.Wait()

	// 更新全局缓存
	s.globalCacheMutex.Lock()
	s.globalMappingCache = result
	s.globalCacheExpiry = time.Now().Add(s.providerCacheExpiration)
	s.globalCacheMutex.Unlock()

	s.logger.Info("全局映射缓存已更新",
		zap.Int("provider_count", len(result)),
		zap.Duration("cache_ttl", s.providerCacheExpiration))

	return result, nil
}

// contains 检查字符串切片是否包含指定元素
func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

// GetPreferredProvider 获取快递公司的首选供应商
func (s *ExpressMappingCacheService) GetPreferredProvider(ctx context.Context, companyCode string) (*ExpressMappingCache, error) {
	s.cacheMutex.RLock()
	defer s.cacheMutex.RUnlock()

	for _, mapping := range s.memoryCache {
		if mapping.CompanyCode == companyCode && mapping.IsSupported && mapping.IsPreferred {
			return mapping, nil
		}
	}

	return nil, fmt.Errorf("未找到快递公司 %s 的首选供应商", companyCode)
}

// RefreshCache 手动刷新缓存
func (s *ExpressMappingCacheService) RefreshCache(ctx context.Context) error {
	return s.refreshCache(ctx)
}

// ClearCache 清空缓存
func (s *ExpressMappingCacheService) ClearCache() {
	s.cacheMutex.Lock()
	defer s.cacheMutex.Unlock()

	s.memoryCache = make(map[string]*ExpressMappingCache)

	// 🔧 修复：清空接口类型缓存
	s.interfaceTypeMutex.Lock()
	s.interfaceTypeCache = make(map[string]bool)
	s.interfaceTypeMutex.Unlock()

	// 🚀 新增：清空供应商公司缓存和过期时间
	s.providerCachesMutex.Lock()
	s.providerCompaniesCache = make(map[string][]*ExpressMappingCache)
	s.providerCacheExpiry = make(map[string]time.Time)
	s.providerCachesMutex.Unlock()

	// 🚀 新增：清空全局映射缓存
	s.globalCacheMutex.Lock()
	s.globalMappingCache = make(map[string]map[string]bool)
	s.globalCacheExpiry = time.Time{}
	s.globalCacheMutex.Unlock()

	s.logger.Info("快递公司映射缓存已清空")
}

// 🚀 新增：清理特定供应商的缓存，确保映射变更立即生效
func (s *ExpressMappingCacheService) ClearProviderCache(providerCode string) {
	cacheKey := fmt.Sprintf("provider_companies:%s", providerCode)

	s.providerCachesMutex.Lock()
	delete(s.providerCompaniesCache, cacheKey)
	delete(s.providerCacheExpiry, cacheKey)
	s.providerCachesMutex.Unlock()

	// 同时清理全局映射缓存，强制重新构建
	s.globalCacheMutex.Lock()
	s.globalMappingCache = make(map[string]map[string]bool)
	s.globalCacheExpiry = time.Time{}
	s.globalCacheMutex.Unlock()

	s.logger.Info("供应商缓存已清理",
		zap.String("provider_code", providerCode),
		zap.String("cache_key", cacheKey))
}

// 🚀 修复：强制刷新特定供应商的缓存，确保映射变更立即生效
func (s *ExpressMappingCacheService) RefreshProviderCache(ctx context.Context, providerCode string) error {
	s.logger.Info("开始强制刷新供应商缓存",
		zap.String("provider_code", providerCode))

	// 🔥 关键修复：使用更强的锁避免并发污染，并增加重试机制
	maxRetries := 3
	for attempt := 1; attempt <= maxRetries; attempt++ {
		if attempt > 1 {
			s.logger.Warn("缓存刷新重试",
				zap.String("provider_code", providerCode),
				zap.Int("attempt", attempt),
				zap.Int("max_retries", maxRetries))
			time.Sleep(time.Duration(attempt*100) * time.Millisecond)
		}

		// 🔥 关键修复：使用更强的锁避免并发污染
		s.providerCachesMutex.Lock()

		// 🔥 关键修复：先清理所有相关缓存
		cacheKey := fmt.Sprintf("provider_companies:%s", providerCode)
		delete(s.providerCompaniesCache, cacheKey)
		delete(s.providerCacheExpiry, cacheKey)

		// 同时清理全局映射缓存，强制重新构建
		s.globalCacheMutex.Lock()
		s.globalMappingCache = make(map[string]map[string]bool)
		s.globalCacheExpiry = time.Time{}
		s.globalCacheMutex.Unlock()

		// 🔥 关键修复：强制清理内存缓存中的所有相关条目
		s.cacheMutex.Lock()
		deletedKeys := 0
		for key := range s.memoryCache {
			// 修复匹配逻辑：key格式是 "provider:company"
			if strings.HasPrefix(key, providerCode+":") {
				delete(s.memoryCache, key)
				deletedKeys++
				s.logger.Debug("清理内存缓存条目",
					zap.String("provider_code", providerCode),
					zap.String("cache_key", key))
			}
		}
		s.cacheMutex.Unlock()

		s.logger.Info("缓存清理完成",
			zap.String("provider_code", providerCode),
			zap.String("cache_key", cacheKey),
			zap.Int("deleted_mapping_keys", deletedKeys))

		// 🔥 新增：强制等待一小段时间，确保数据库事务完全提交
		time.Sleep(200 * time.Millisecond)

		// 🔥 关键修复：立即重新从数据库加载最新数据（在锁保护下）
		companies, err := s.loadSupportedCompaniesFromDBDirect(ctx, providerCode)
		if err != nil {
			s.providerCachesMutex.Unlock()
			s.logger.Error("刷新供应商缓存失败",
				zap.String("provider_code", providerCode),
				zap.Int("attempt", attempt),
				zap.Error(err))
			if attempt == maxRetries {
				return err
			}
			continue
		}

		// 🔥 新增：调试日志，确保我们确实从数据库读到了最新数据
		s.logger.Info("从数据库直接加载的公司列表",
			zap.String("provider_code", providerCode),
			zap.Int("total_count", len(companies)),
			zap.Int("attempt", attempt))
		for i, company := range companies {
			s.logger.Info("数据库直接查询结果",
				zap.Int("index", i+1),
				zap.String("company_code", company.CompanyCode),
				zap.String("company_name", company.CompanyName),
				zap.Bool("is_supported", company.IsSupported))
		}

		// 立即更新缓存，防止并发重复查询
		s.providerCompaniesCache[cacheKey] = companies
		s.providerCacheExpiry[cacheKey] = time.Now().Add(s.providerCacheExpiration)

		s.providerCachesMutex.Unlock()

		s.logger.Info("供应商缓存刷新成功",
			zap.String("provider_code", providerCode),
			zap.Int("companies_count", len(companies)),
			zap.Int("attempt", attempt))

		// 🔥 关键修复：输出详细的映射信息用于调试
		s.logger.Info("刷新后的供应商映射详情",
			zap.String("provider_code", providerCode))
		for i, company := range companies {
			s.logger.Info("",
				zap.Int("index", i+1),
				zap.String("company_code", company.CompanyCode),
				zap.String("company_name", company.CompanyName),
				zap.Bool("is_supported", company.IsSupported))
		}

		return nil
	}

	return fmt.Errorf("缓存刷新失败，已重试%d次", maxRetries)
}

// loadSupportedCompaniesFromDBDirect 直接从数据库加载供应商支持的快递公司（不使用缓存）
func (s *ExpressMappingCacheService) loadSupportedCompaniesFromDBDirect(ctx context.Context, providerCode string) ([]*ExpressMappingCache, error) {
	s.logger.Debug("直接从数据库查询供应商支持的快递公司",
		zap.String("provider_code", providerCode))

	// 获取供应商信息
	providers, err := s.repository.GetProviders(ProviderFilter{}, Pagination{Page: 1, PageSize: 100})
	if err != nil {
		return nil, fmt.Errorf("获取供应商列表失败: %w", err)
	}

	var targetProvider *ExpressProvider
	for _, provider := range providers.Providers {
		if provider.Code == providerCode {
			targetProvider = provider
			break
		}
	}

	if targetProvider == nil {
		return nil, fmt.Errorf("未找到供应商: %s", providerCode)
	}

	// 获取该供应商的所有映射关系
	mappings, err := s.repository.GetMappingsByProvider(targetProvider.ID)
	if err != nil {
		return nil, fmt.Errorf("获取供应商映射失败: %w", err)
	}

	var companies []*ExpressMappingCache
	for _, mapping := range mappings {
		// 获取快递公司信息
		company, err := s.repository.GetCompanyByID(mapping.CompanyID)
		if err != nil {
			s.logger.Warn("获取快递公司信息失败",
				zap.String("company_id", mapping.CompanyID),
				zap.Error(err))
			continue
		}

		// 只返回支持且活跃的公司
		if mapping.IsSupported && company.IsActive {
			cacheMapping := &ExpressMappingCache{
				CompanyCode:         company.Code,
				CompanyName:         company.Name,
				ProviderCode:        providerCode,
				ProviderCompanyCode: mapping.ProviderCompanyCode,
				IsSupported:         mapping.IsSupported,
				IsPreferred:         mapping.IsPreferred,
				VolumeWeightRatio:   company.VolumeWeightRatio,
				MaxWeightKg:         mapping.WeightLimitKg,
				SupportedServices:   mapping.SupportedServices,
			}

			companies = append(companies, cacheMapping)
		}
	}

	s.logger.Debug("直接从数据库获取供应商支持的快递公司完成",
		zap.String("provider_code", providerCode),
		zap.Int("count", len(companies)))

	return companies, nil
}

// ClearSpecificCache 清除特定的缓存条目
func (s *ExpressMappingCacheService) ClearSpecificCache(companyCode, providerCode string) {
	cacheKey := fmt.Sprintf("%s:%s", providerCode, companyCode)

	s.cacheMutex.Lock()
	defer s.cacheMutex.Unlock()

	delete(s.memoryCache, cacheKey)

	s.logger.Debug("清除特定缓存条目",
		zap.String("company_code", companyCode),
		zap.String("provider_code", providerCode),
		zap.String("cache_key", cacheKey))
}

// ClearCompanyCache 清除快递公司相关的所有缓存条目
func (s *ExpressMappingCacheService) ClearCompanyCache(companyCode string) {
	s.cacheMutex.Lock()
	defer s.cacheMutex.Unlock()

	var deletedKeys []string
	for key, mapping := range s.memoryCache {
		if mapping.CompanyCode == companyCode {
			delete(s.memoryCache, key)
			deletedKeys = append(deletedKeys, key)
		}
	}

	// 🔧 同时清理接口类型缓存
	s.interfaceTypeMutex.Lock()
	delete(s.interfaceTypeCache, companyCode)
	s.interfaceTypeMutex.Unlock()

	s.logger.Info("清除快递公司相关缓存",
		zap.String("company_code", companyCode),
		zap.Int("deleted_count", len(deletedKeys)),
		zap.Strings("deleted_keys", deletedKeys))
}

// Stop 停止缓存服务
func (s *ExpressMappingCacheService) Stop() {
	close(s.stopChan)
	s.wg.Wait()
	s.logger.Info("快递公司映射缓存服务已停止")
}

// getFromMemoryCache 从内存缓存获取数据
func (s *ExpressMappingCacheService) getFromMemoryCache(cacheKey string) *ExpressMappingCache {
	s.cacheMutex.RLock()
	defer s.cacheMutex.RUnlock()

	if mapping, exists := s.memoryCache[cacheKey]; exists {
		// 检查是否过期
		if time.Now().Before(mapping.CacheExpiresAt) {
			return mapping
		}
		// 过期则删除
		delete(s.memoryCache, cacheKey)
	}

	return nil
}

// loadAndCacheMapping 从数据库加载并缓存映射
func (s *ExpressMappingCacheService) loadAndCacheMapping(ctx context.Context, companyCode, providerCode string) (*ExpressMappingCache, error) {
	// 获取快递公司
	company, err := s.repository.GetCompanyByCode(companyCode)
	if err != nil {
		return nil, fmt.Errorf("快递公司不存在: %w", err)
	}

	// 🔥 关键检查：快递公司必须是启用状态
	if !company.IsActive {
		return nil, fmt.Errorf("快递公司 %s 已禁用", companyCode)
	}

	// 获取供应商
	provider, err := s.repository.GetProviderByCode(providerCode)
	if err != nil {
		return nil, fmt.Errorf("供应商不存在: %w", err)
	}

	// 获取映射关系
	mapping, err := s.repository.GetMapping(company.ID, provider.ID)
	if err != nil {
		return nil, fmt.Errorf("映射关系不存在: %w", err)
	}

	// 转换为缓存对象
	cacheMapping := &ExpressMappingCache{
		CompanyCode:         company.Code,
		CompanyName:         company.Name,
		ProviderCode:        provider.Code,
		ProviderCompanyCode: mapping.ProviderCompanyCode,
		IsSupported:         mapping.IsSupported,
		IsPreferred:         mapping.IsPreferred,
		VolumeWeightRatio:   company.VolumeWeightRatio,
		MaxWeightKg:         mapping.WeightLimitKg,
		SupportedServices:   mapping.SupportedServices,
		CacheExpiresAt:      time.Now().Add(s.cacheExpiration),
	}

	// 解析JSON映射
	if mapping.ProductTypeMappings != nil {
		if err := json.Unmarshal(mapping.ProductTypeMappings, &cacheMapping.ProductTypeMappings); err != nil {
			s.logger.Warn("解析产品类型映射失败", zap.Error(err))
		}
	}

	if mapping.PayMethodMappings != nil {
		if err := json.Unmarshal(mapping.PayMethodMappings, &cacheMapping.PayMethodMappings); err != nil {
			s.logger.Warn("解析支付方式映射失败", zap.Error(err))
		}
	}

	if mapping.StatusCodeMappings != nil {
		if err := json.Unmarshal(mapping.StatusCodeMappings, &cacheMapping.StatusCodeMappings); err != nil {
			s.logger.Warn("解析状态码映射失败", zap.Error(err))
		}
	}

	// 存入内存缓存
	cacheKey := fmt.Sprintf("%s:%s", providerCode, companyCode)
	s.cacheMutex.Lock()
	s.memoryCache[cacheKey] = cacheMapping
	s.cacheMutex.Unlock()

	return cacheMapping, nil
}

// refreshCache 刷新缓存
func (s *ExpressMappingCacheService) refreshCache(ctx context.Context) error {
	s.logger.Info("开始刷新快递公司映射缓存")

	// 🔥 修复：只获取启用的快递公司，禁用的快递公司不应该参与查价
	isActive := true
	companies, err := s.repository.GetCompanies(CompanyFilter{IsActive: &isActive}, Pagination{Page: 1, PageSize: 1000})
	if err != nil {
		return fmt.Errorf("获取快递公司列表失败: %w", err)
	}

	// 🔥 修复：获取所有供应商（供应商状态现在通过system_configs管理，不在数据库表中）
	providers, err := s.repository.GetProviders(ProviderFilter{}, Pagination{Page: 1, PageSize: 100})
	if err != nil {
		return fmt.Errorf("获取供应商列表失败: %w", err)
	}

	newCache := make(map[string]*ExpressMappingCache)

	// 为每个快递公司和供应商组合加载映射
	for _, company := range companies.Companies {
		for _, provider := range providers.Providers {
			mapping, err := s.repository.GetMapping(company.ID, provider.ID)
			if err != nil {
				// 映射不存在，跳过
				continue
			}

			if !mapping.IsSupported {
				// 不支持，跳过
				continue
			}

			cacheMapping := &ExpressMappingCache{
				CompanyCode:         company.Code,
				CompanyName:         company.Name,
				ProviderCode:        provider.Code,
				ProviderCompanyCode: mapping.ProviderCompanyCode,
				IsSupported:         mapping.IsSupported,
				IsPreferred:         mapping.IsPreferred,
				VolumeWeightRatio:   company.VolumeWeightRatio,
				MaxWeightKg:         mapping.WeightLimitKg,
				SupportedServices:   mapping.SupportedServices,
				CacheExpiresAt:      time.Now().Add(s.cacheExpiration),
			}

			// 解析JSON映射
			if mapping.ProductTypeMappings != nil {
				json.Unmarshal(mapping.ProductTypeMappings, &cacheMapping.ProductTypeMappings)
			}
			if mapping.PayMethodMappings != nil {
				json.Unmarshal(mapping.PayMethodMappings, &cacheMapping.PayMethodMappings)
			}
			if mapping.StatusCodeMappings != nil {
				json.Unmarshal(mapping.StatusCodeMappings, &cacheMapping.StatusCodeMappings)
			}

			cacheKey := fmt.Sprintf("%s:%s", provider.Code, company.Code)
			newCache[cacheKey] = cacheMapping
		}
	}

	// 原子性替换缓存
	s.cacheMutex.Lock()
	s.memoryCache = newCache
	s.cacheMutex.Unlock()

	s.logger.Info("快递公司映射缓存刷新完成", zap.Int("cached_mappings", len(newCache)))
	return nil
}

// startBackgroundRefresh 启动后台刷新任务
func (s *ExpressMappingCacheService) startBackgroundRefresh() {
	s.wg.Add(1)
	go func() {
		defer s.wg.Done()

		ticker := time.NewTicker(s.refreshInterval)
		defer ticker.Stop()

		// 初始化缓存
		ctx := context.Background()
		if err := s.refreshCache(ctx); err != nil {
			s.logger.Error("初始化快递公司映射缓存失败", zap.Error(err))
		}

		for {
			select {
			case <-ticker.C:
				if err := s.refreshCache(ctx); err != nil {
					s.logger.Error("定时刷新快递公司映射缓存失败", zap.Error(err))
				}
			case <-s.stopChan:
				return
			}
		}
	}()
}
