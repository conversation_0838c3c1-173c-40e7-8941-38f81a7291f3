#!/bin/bash

# 测试快递鸟取消订单的外部回调转发逻辑
# 通过管理员API直接更新订单状态为已取消，然后检查是否有外部回调转发

echo "🧪 快递鸟取消订单外部回调转发测试"
echo "================================"

# 配置
API_BASE="http://localhost:8081"
TOKEN="****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"

# 1. 查找一个快递鸟的assigned订单
echo "🔍 1. 查找快递鸟的assigned订单..."
KUAIDINIAO_ORDER_INFO=$(curl -s "${API_BASE}/api/v1/admin/orders?provider=kuaidiniao&status=assigned&page=1&page_size=1" \
  -H "Authorization: Bearer $TOKEN" | jq -r '.data.records[0] | {id: .id, order_no: .order_no, user_id: .user_id}')

if [ "$KUAIDINIAO_ORDER_INFO" = "null" ]; then
  echo "❌ 没有找到快递鸟的assigned订单，无法测试"
  exit 1
fi

ORDER_ID=$(echo "$KUAIDINIAO_ORDER_INFO" | jq -r '.id')
ORDER_NO=$(echo "$KUAIDINIAO_ORDER_INFO" | jq -r '.order_no')
USER_ID=$(echo "$KUAIDINIAO_ORDER_INFO" | jq -r '.user_id')

echo "✅ 找到快递鸟订单:"
echo "   订单ID: $ORDER_ID"
echo "   订单号: $ORDER_NO"
echo "   用户ID: $USER_ID"

# 2. 查看订单当前状态
echo ""
echo "🔍 2. 查看订单当前状态..."
CURRENT_STATUS=$(curl -s "${API_BASE}/api/v1/admin/orders/${ORDER_ID}" \
  -H "Authorization: Bearer $TOKEN" | jq -r '.data.status')

echo "📋 当前状态: $CURRENT_STATUS"

# 3. 记录回调转发前的状态
echo ""
echo "🔍 3. 记录回调转发前的状态..."
BEFORE_CALLBACK_COUNT=$(curl -s "*************************************************/callback_receiver" \
  -c "SELECT COUNT(*) FROM callback_logs WHERE customer_order_no = '$ORDER_NO' OR platform_order_no LIKE '%$ORDER_NO%';" 2>/dev/null | tail -n 1 || echo "0")

echo "📋 回调转发前记录数: $BEFORE_CALLBACK_COUNT"

# 4. 使用管理员API更新订单状态为已取消
echo ""
echo "🚀 4. 使用管理员API更新订单状态为已取消..."
UPDATE_RESPONSE=$(curl -s -X PUT "${API_BASE}/api/v1/admin/orders/${ORDER_ID}/status" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d "{
    \"new_status\": \"cancelled\",
    \"reason\": \"测试快递鸟外部回调转发逻辑\"
  }")

echo "📤 更新响应:"
echo "$UPDATE_RESPONSE" | jq .

# 5. 等待一段时间让回调处理完成
echo ""
echo "⏳ 5. 等待回调处理完成..."
sleep 5

# 6. 检查订单状态是否更新
echo ""
echo "🔍 6. 检查订单状态是否更新..."
FINAL_STATUS=$(curl -s "${API_BASE}/api/v1/admin/orders/${ORDER_ID}" \
  -H "Authorization: Bearer $TOKEN" | jq -r '.data.status')

echo "📋 最终状态: $FINAL_STATUS"

# 7. 检查是否有新的回调转发记录
echo ""
echo "🔍 7. 检查是否有新的回调转发记录..."
AFTER_CALLBACK_COUNT=$(curl -s "*************************************************/callback_receiver" \
  -c "SELECT COUNT(*) FROM callback_logs WHERE customer_order_no = '$ORDER_NO' OR platform_order_no LIKE '%$ORDER_NO%';" 2>/dev/null | tail -n 1 || echo "0")

echo "📋 回调转发后记录数: $AFTER_CALLBACK_COUNT"

# 8. 检查服务日志中的外部回调转发记录
echo ""
echo "🔍 8. 检查服务日志中的外部回调转发记录..."
echo "查找最近的外部回调转发日志..."

# 查找最新的日志文件
LATEST_LOG=$(ls -t logs/go-kuaidi-local-*.log 2>/dev/null | head -n 1)
if [ -n "$LATEST_LOG" ]; then
  echo "📋 检查日志文件: $LATEST_LOG"
  
  # 查找与该订单相关的回调转发日志
  CALLBACK_LOGS=$(grep -n "$ORDER_NO\|外部回调转发\|triggerCancellationCallback\|sendInternalCallback" "$LATEST_LOG" 2>/dev/null | tail -n 10)
  
  if [ -n "$CALLBACK_LOGS" ]; then
    echo "📋 找到相关回调日志:"
    echo "$CALLBACK_LOGS"
  else
    echo "❌ 没有找到相关的回调转发日志"
  fi
else
  echo "❌ 没有找到日志文件"
fi

# 9. 结果验证
echo ""
echo "🎯 9. 测试结果验证..."
echo "================================"

SUCCESS=true

if [ "$FINAL_STATUS" = "cancelled" ]; then
  echo "✅ 订单状态正确: 已更新为已取消状态"
else
  echo "❌ 订单状态错误: 期望 'cancelled'，实际 '$FINAL_STATUS'"
  SUCCESS=false
fi

# 检查是否有回调转发（这里我们主要检查日志，因为实际的外部回调可能需要真实的回调URL）
if [ -n "$CALLBACK_LOGS" ]; then
  echo "✅ 找到回调转发相关日志"
else
  echo "⚠️  没有找到回调转发日志（可能需要检查实现）"
fi

# 10. 总结
echo ""
echo "📊 测试总结:"
echo "================================"
echo "订单ID: $ORDER_ID"
echo "订单号: $ORDER_NO"
echo "用户ID: $USER_ID"
echo "初始状态: $CURRENT_STATUS"
echo "最终状态: $FINAL_STATUS"
echo "回调前记录数: $BEFORE_CALLBACK_COUNT"
echo "回调后记录数: $AFTER_CALLBACK_COUNT"

if [ "$SUCCESS" = true ]; then
  echo ""
  echo "🎉 测试基本通过！订单状态更新成功"
  echo "   ✅ 订单已更新为已取消状态"
  echo "   📋 请检查日志确认外部回调转发逻辑是否执行"
else
  echo ""
  echo "❌ 测试失败！需要检查实现"
fi

echo ""
echo "💡 注意事项："
echo "   - 外部回调转发需要真实的回调URL才能完全验证"
echo "   - 当前主要验证回调转发逻辑是否被触发"
echo "   - 检查服务日志以确认triggerCancellationCallback方法是否被调用"
