#!/bin/bash

echo "🎯 测试前端缓存失败原因功能"
echo "=================================="

echo ""
echo "📊 1. 测试后端API - 有失败记录的路线:"
echo "路线: 湖南省->陕西省, kuaidi100, STO, 2kg"

# 测试后端API
curl -s "http://localhost:8081/api/v1/weight-cache/details?provider=kuaidi100&express_code=STO&from_province=%E6%B9%96%E5%8D%97%E7%9C%81&to_province=%E9%99%95%E8%A5%BF%E7%9C%81&weight_kg=2&page=1&page_size=5" | python3 -c "
import json
import sys
try:
    data = json.load(sys.stdin)
    if data.get('success'):
        routes = data.get('data', {}).get('routes', [])
        print(f'✅ 后端API返回 {len(routes)} 条缓存记录')
        for route in routes:
            print(f'📍 路线: {route.get(\"route\", \"N/A\")}')
            print(f'❌ 失败次数: {route.get(\"failed_queries\", 0)}')
            print(f'📊 总查询次数: {route.get(\"total_queries\", 0)}')
            
            failure_reasons = route.get('failure_reasons', [])
            if failure_reasons:
                print('🚨 失败原因:')
                for i, reason in enumerate(failure_reasons, 1):
                    print(f'   {i}. {reason.get(\"error_message\", \"N/A\")}')
                    print(f'      出现次数: {reason.get(\"count\", 0)}')
                    print()
            else:
                print('✅ 无失败原因记录')
            print('---')
    else:
        print(f'❌ API调用失败: {data.get(\"message\", \"未知错误\")}')
except Exception as e:
    print(f'❌ 解析JSON失败: {e}')
"

echo ""
echo "📊 2. 测试后端API - 无失败记录的路线:"
echo "路线: 上海市->北京市, yuntong, JT, 5kg"

# 测试无失败记录的路线
curl -s "http://localhost:8081/api/v1/weight-cache/details?provider=yuntong&express_code=JT&from_province=%E4%B8%8A%E6%B5%B7%E5%B8%82&to_province=%E5%8C%97%E4%BA%AC%E5%B8%82&weight_kg=5&page=1&page_size=5" | python3 -c "
import json
import sys
try:
    data = json.load(sys.stdin)
    if data.get('success'):
        routes = data.get('data', {}).get('routes', [])
        print(f'✅ 后端API返回 {len(routes)} 条缓存记录')
        for route in routes:
            print(f'📍 路线: {route.get(\"route\", \"N/A\")}')
            print(f'❌ 失败次数: {route.get(\"failed_queries\", 0)}')
            print(f'📊 总查询次数: {route.get(\"total_queries\", 0)}')
            
            failure_reasons = route.get('failure_reasons', [])
            if failure_reasons:
                print('🚨 失败原因:')
                for i, reason in enumerate(failure_reasons, 1):
                    print(f'   {i}. {reason.get(\"error_message\", \"N/A\")}')
                    print(f'      出现次数: {reason.get(\"count\", 0)}')
                    print()
            else:
                print('✅ 无失败原因记录')
            print('---')
    else:
        print(f'❌ API调用失败: {data.get(\"message\", \"未知错误\")}')
except Exception as e:
    print(f'❌ 解析JSON失败: {e}')
"

echo ""
echo "🎯 3. 前端测试指南:"
echo "   1. 打开浏览器访问: http://localhost:3008/weight-cache"
echo "   2. 找到快递100供应商的申通快递(STO)，点击'缓存明细'"
echo "   3. 查看表格中是否显示了'失败统计'和'失败原因'列"
echo "   4. 寻找有失败记录的路线，查看失败原因是否正确显示"
echo ""
echo "✅ 测试完成！"
