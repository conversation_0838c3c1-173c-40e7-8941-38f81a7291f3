#!/bin/bash

# 快递鸟供应商迁移配置脚本
# 用于配置快递鸟供应商从标准查价接口迁移到实时查价接口后的相关设置

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 数据库配置
DB_HOST="*************"
DB_PORT="5432"
DB_NAME="go_kuaidi"
DB_USER="postgres"
DB_PASSWORD="gjx6ngf4"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# 执行SQL命令
execute_sql() {
    local sql="$1"
    local description="$2"
    
    log_info "执行: $description"
    
    PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "$sql"
    
    if [ $? -eq 0 ]; then
        log_success "$description 完成"
    else
        log_error "$description 失败"
        return 1
    fi
}

# 检查数据库连接
check_database_connection() {
    log_info "检查数据库连接..."
    
    PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "SELECT 1;" > /dev/null 2>&1
    
    if [ $? -eq 0 ]; then
        log_success "数据库连接正常"
        return 0
    else
        log_error "数据库连接失败，请检查配置"
        return 1
    fi
}

# 配置快递鸟供应商
setup_kuaidiniao_provider() {
    log_info "🚀 配置快递鸟供应商..."
    
    # 启用快递鸟供应商
    execute_sql "
        INSERT INTO system_configs (config_key, config_value, description, created_at, updated_at) 
        VALUES ('provider.kuaidiniao_enabled', 'true', '启用快递鸟供应商', NOW(), NOW())
        ON CONFLICT (config_key) DO UPDATE SET 
            config_value = EXCLUDED.config_value,
            updated_at = NOW();
    " "启用快递鸟供应商"
    
    # 配置快递鸟API参数
    execute_sql "
        INSERT INTO system_configs (config_key, config_value, description, created_at, updated_at) 
        VALUES 
            ('provider_kuaidiniao.e_business_id', '1778716', '快递鸟商户ID', NOW(), NOW()),
            ('provider_kuaidiniao.api_key', 'e7ea996e-f395-4208-b099-6513eb00eead', '快递鸟API密钥', NOW(), NOW()),
            ('provider_kuaidiniao.base_url', 'https://api.kdniao.com/api/dist', '快递鸟API地址', NOW(), NOW()),
            ('provider_kuaidiniao.environment', 'production', '快递鸟环境', NOW(), NOW()),
            ('provider_kuaidiniao.timeout', '10', '快递鸟超时时间（秒）', NOW(), NOW())
        ON CONFLICT (config_key) DO UPDATE SET 
            config_value = EXCLUDED.config_value,
            updated_at = NOW();
    " "配置快递鸟API参数"
}

# 添加快递鸟供应商到数据库
add_kuaidiniao_provider_to_db() {
    log_info "🏢 添加快递鸟供应商到数据库..."
    
    execute_sql "
        INSERT INTO express_providers (code, name, description, is_active, priority, created_at, updated_at)
        VALUES ('kuaidiniao', '快递鸟', '快递鸟物流供应商', true, 4, NOW(), NOW())
        ON CONFLICT (code) DO UPDATE SET 
            name = EXCLUDED.name,
            description = EXCLUDED.description,
            is_active = EXCLUDED.is_active,
            priority = EXCLUDED.priority,
            updated_at = NOW();
    " "添加快递鸟供应商记录"
}

# 配置快递鸟支持的快递公司映射
setup_kuaidiniao_mappings() {
    log_info "🗺️ 配置快递鸟快递公司映射..."
    
    # 获取快递鸟供应商ID
    local provider_id=$(PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "SELECT id FROM express_providers WHERE code = 'kuaidiniao';" | xargs)
    
    if [ -z "$provider_id" ]; then
        log_error "未找到快递鸟供应商记录"
        return 1
    fi
    
    log_info "快递鸟供应商ID: $provider_id"
    
    # 配置快递鸟支持的快递公司映射
    execute_sql "
        INSERT INTO express_company_mappings (company_id, provider_id, provider_company_code, is_supported, created_at, updated_at)
        SELECT 
            c.id,
            '$provider_id',
            CASE c.code
                WHEN 'SF' THEN 'SF'
                WHEN 'JD' THEN 'JD'
                WHEN 'ZTO' THEN 'ZTO'
                WHEN 'YTO' THEN 'YTO'
                WHEN 'YD' THEN 'YD'
                WHEN 'DBL' THEN 'DBL'
                WHEN 'STO' THEN 'STO'
                WHEN 'JT' THEN 'JTSD'
                WHEN 'EMS' THEN 'EMS'
                ELSE c.code
            END,
            true,
            NOW(),
            NOW()
        FROM express_companies c
        WHERE c.code IN ('SF', 'JD', 'ZTO', 'YTO', 'YD', 'DBL', 'STO', 'JT', 'EMS')
        ON CONFLICT (company_id, provider_id) DO UPDATE SET
            provider_company_code = EXCLUDED.provider_company_code,
            is_supported = EXCLUDED.is_supported,
            updated_at = NOW();
    " "配置快递鸟快递公司映射"
}

# 验证配置
verify_configuration() {
    log_info "🔍 验证配置..."
    
    # 检查快递鸟供应商是否启用
    local enabled=$(PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "SELECT config_value FROM system_configs WHERE config_key = 'provider.kuaidiniao_enabled';" | xargs)
    
    if [ "$enabled" = "true" ]; then
        log_success "✅ 快递鸟供应商已启用"
    else
        log_error "❌ 快递鸟供应商未启用"
    fi
    
    # 检查快递鸟供应商记录
    local provider_count=$(PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "SELECT COUNT(*) FROM express_providers WHERE code = 'kuaidiniao' AND is_active = true;" | xargs)
    
    if [ "$provider_count" = "1" ]; then
        log_success "✅ 快递鸟供应商记录存在且已激活"
    else
        log_error "❌ 快递鸟供应商记录不存在或未激活"
    fi
    
    # 检查快递鸟映射数量
    local mapping_count=$(PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "
        SELECT COUNT(*) 
        FROM express_company_mappings ecm
        JOIN express_providers ep ON ecm.provider_id = ep.id
        WHERE ep.code = 'kuaidiniao' AND ecm.is_supported = true;
    " | xargs)
    
    log_info "快递鸟支持的快递公司数量: $mapping_count"
    
    if [ "$mapping_count" -gt "0" ]; then
        log_success "✅ 快递鸟快递公司映射配置完成"
    else
        log_error "❌ 快递鸟快递公司映射配置失败"
    fi
}

# 显示配置摘要
show_configuration_summary() {
    log_info "📋 配置摘要"
    echo ""
    echo "快递鸟供应商迁移配置完成！"
    echo ""
    echo "✅ 已完成的配置："
    echo "  - 快递鸟供应商已启用"
    echo "  - 快递鸟API配置已设置"
    echo "  - 快递鸟供应商记录已添加到数据库"
    echo "  - 快递鸟快递公司映射已配置"
    echo ""
    echo "🎯 迁移效果："
    echo "  - 标准查价接口（QUERY_PRICE）不再包含快递鸟供应商"
    echo "  - 实时查价接口（QUERY_REALTIME_PRICE）包含快递鸟供应商"
    echo ""
    echo "🧪 测试方法："
    echo "  运行测试脚本: ./test_kuaidiniao_migration.sh"
    echo ""
}

# 主函数
main() {
    echo "🚀 快递鸟供应商迁移配置脚本"
    echo "=================================="
    echo ""
    
    # 检查数据库连接
    if ! check_database_connection; then
        exit 1
    fi
    
    echo ""
    
    # 执行配置步骤
    setup_kuaidiniao_provider
    add_kuaidiniao_provider_to_db
    setup_kuaidiniao_mappings
    
    echo ""
    
    # 验证配置
    verify_configuration
    
    echo ""
    
    # 显示配置摘要
    show_configuration_summary
}

# 执行主函数
main "$@"
