#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import psycopg2
import requests
import urllib.parse
import json
import time
from datetime import datetime

# 数据库配置
CALLBACK_DB_CONFIG = {
    'host': '*************',
    'port': 5432,
    'user': 'postgres',
    'password': 'gjx6ngf4',
    'database': 'callback_receiver'
}

MAIN_DB_CONFIG = {
    'host': '*************',
    'port': 5432,
    'user': 'postgres',
    'password': 'gjx6ngf4',
    'database': 'go_kuaidi'
}

API_BASE_URL = "http://localhost:8081"

# 问题订单列表
PROBLEM_ORDERS = [
    'DPK202580385165', 'DPK202580340237', 'DPK202579994323', 'DPK202579941633',
    'DPK202579931694', 'DPK202579913553', 'DPK202579712495', 'DPK202572917023',
    'DPK202572911053', 'DPK202572900214', 'DPK202572776229', 'DPK202571899463',
    'DPK202571736764'
]

def get_order_info(tracking_no):
    """获取订单信息"""
    try:
        conn = psycopg2.connect(**MAIN_DB_CONFIG)
        cursor = conn.cursor()
        
        query = """
        SELECT customer_order_no, order_no, provider, status, updated_at 
        FROM order_records 
        WHERE tracking_no = %s
        """
        
        cursor.execute(query, (tracking_no,))
        result = cursor.fetchone()
        
        cursor.close()
        conn.close()
        
        return result
    except Exception as e:
        print(f"   ❌ 查询主数据库失败: {e}")
        return None

def get_latest_callback(tracking_no):
    """获取最新回调"""
    try:
        conn = psycopg2.connect(**CALLBACK_DB_CONFIG)
        cursor = conn.cursor()
        
        query = """
        SELECT id, received_at, raw_body, processed
        FROM callback_raw_data 
        WHERE provider = 'kuaidi100' 
          AND raw_body LIKE %s 
        ORDER BY received_at DESC 
        LIMIT 1
        """
        
        cursor.execute(query, (f'%{tracking_no}%',))
        result = cursor.fetchone()
        
        cursor.close()
        conn.close()
        
        return result
    except Exception as e:
        print(f"   ❌ 查询回调数据库失败: {e}")
        return None

def decode_callback(raw_body):
    """解码回调数据"""
    try:
        if 'param=' in raw_body:
            param_start = raw_body.find('param=') + 6
            param_end = raw_body.find('&', param_start)
            if param_end == -1:
                param_end = len(raw_body)
            
            encoded_param = raw_body[param_start:param_end]
            decoded_param = urllib.parse.unquote(encoded_param)
            
            data = json.loads(decoded_param)
            return data
        return None
    except Exception as e:
        print(f"   ❌ 解码失败: {e}")
        return None

def repush_callback(raw_body, tracking_no):
    """重新推送回调"""
    try:
        headers = {
            'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
            'User-Agent': 'Comprehensive-Fix-Script/1.0',
            'X-Repush-Callback': 'true',
            'X-Repush-Order': tracking_no,
            'X-Fix-Timestamp': str(int(time.time()))
        }
        
        response = requests.post(
            f"{API_BASE_URL}/api/v1/callbacks/kuaidi100",
            data=raw_body,
            headers=headers,
            timeout=30
        )
        
        return response.status_code == 200, response.text
    except Exception as e:
        return False, str(e)

def main():
    """主函数"""
    print("🚀 彻底修复快递100订单状态问题")
    print("=" * 60)
    
    success_count = 0
    failed_count = 0
    
    for i, tracking_no in enumerate(PROBLEM_ORDERS, 1):
        print(f"\n🔍 处理订单 [{i}/{len(PROBLEM_ORDERS)}]: {tracking_no}")
        
        # 1. 获取当前订单状态
        order_info = get_order_info(tracking_no)
        if not order_info:
            print(f"   ❌ 主数据库中没有找到订单")
            failed_count += 1
            continue
        
        customer_order_no, order_no, provider, current_status, updated_at = order_info
        print(f"   📊 当前状态: {current_status} (更新时间: {updated_at})")
        
        # 2. 获取最新回调
        callback_info = get_latest_callback(tracking_no)
        if not callback_info:
            print(f"   ❌ 没有找到相关回调")
            failed_count += 1
            continue
        
        callback_id, received_at, raw_body, processed = callback_info
        print(f"   📤 最新回调: ID={callback_id}, 时间={received_at}, 已处理={processed}")
        
        # 3. 解码回调数据
        decoded_data = decode_callback(raw_body)
        if not decoded_data:
            print(f"   ❌ 无法解码回调数据")
            failed_count += 1
            continue
        
        data = decoded_data.get('data', {})
        callback_status = data.get('status')
        sent_status = data.get('sentStatus')
        courier_name = data.get('courierName', '')
        courier_mobile = data.get('courierMobile', '')
        
        print(f"   📊 回调状态: {callback_status}")
        print(f"   📊 发送状态: {sent_status}")
        if courier_name:
            print(f"   👤 快递员: {courier_name} ({courier_mobile})")
        
        # 4. 重新推送回调
        print(f"   🔄 重新推送回调...")
        success, response = repush_callback(raw_body, tracking_no)
        
        if success:
            print(f"   ✅ 回调重新推送成功")
            
            # 等待处理
            time.sleep(3)
            
            # 检查状态是否更新
            new_order_info = get_order_info(tracking_no)
            if new_order_info:
                new_status = new_order_info[3]
                new_updated_at = new_order_info[4]
                print(f"   📊 重新推送后状态: {new_status} (更新时间: {new_updated_at})")
                
                if new_status != current_status or new_updated_at != updated_at:
                    print(f"   🎉 状态更新成功: {current_status} -> {new_status}")
                    success_count += 1
                else:
                    print(f"   ⚠️  状态未更新，可能需要手动检查")
                    failed_count += 1
            else:
                failed_count += 1
        else:
            print(f"   ❌ 回调重新推送失败: {response}")
            failed_count += 1
        
        # 避免请求过快
        time.sleep(1)
    
    print(f"\n📊 处理结果统计:")
    print(f"   ✅ 成功处理: {success_count} 个订单")
    print(f"   ❌ 处理失败: {failed_count} 个订单")
    print(f"   📦 总计订单: {len(PROBLEM_ORDERS)} 个")
    
    # 最终状态检查
    print(f"\n📋 最终状态检查:")
    print("=" * 60)
    
    for tracking_no in PROBLEM_ORDERS:
        order_info = get_order_info(tracking_no)
        if order_info:
            customer_order_no, order_no, provider, status, updated_at = order_info
            print(f"📦 {tracking_no}: {customer_order_no} | {status} | {updated_at}")
        else:
            print(f"❌ {tracking_no}: 未找到订单")

if __name__ == "__main__":
    main()
