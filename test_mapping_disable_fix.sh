#!/bin/bash

# 快递公司映射禁用功能测试脚本
# 用于验证禁用快递公司映射后，价格查询和订单创建是否立即生效

set -e

# 配置
BASE_URL="http://localhost:8081"
ADMIN_TOKEN=""
USER_TOKEN=""

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 获取管理员Token
get_admin_token() {
    log_info "获取管理员Token..."
    
    response=$(curl -s -X POST "${BASE_URL}/api/v1/admin/auth/login" \
        -H "Content-Type: application/json" \
        -d '{
            "username": "admin",
            "password": "1104030777+.aA..@"
        }')
    
    ADMIN_TOKEN=$(echo "$response" | jq -r '.access_token // empty')

    if [ -z "$ADMIN_TOKEN" ]; then
        log_error "获取管理员Token失败: $response"
        exit 1
    fi
    
    log_success "管理员Token获取成功"
}

# 获取用户Token
get_user_token() {
    log_info "获取用户Token..."
    
    response=$(curl -s -X POST "${BASE_URL}/api/v1/auth/login" \
        -H "Content-Type: application/json" \
        -d '{
            "username": "mywl",
            "password": "NNJJ@178..n"
        }')
    
    USER_TOKEN=$(echo "$response" | jq -r '.access_token // empty')

    if [ -z "$USER_TOKEN" ]; then
        log_error "获取用户Token失败: $response"
        exit 1
    fi
    
    log_success "用户Token获取成功"
}

# 查找韵达快递的快递100映射ID
find_yunda_kuaidi100_mapping() {
    log_info "查找韵达快递的快递100映射..."
    
    # 获取映射列表
    response=$(curl -s -X GET "${BASE_URL}/api/v1/admin/express/mappings?page=1&page_size=100" \
        -H "Authorization: Bearer $ADMIN_TOKEN")
    
    # 查找韵达(YD)和快递100的映射
    MAPPING_ID=$(echo "$response" | jq -r '.data.mappings[] | select(.company_code == "YD" and .provider_code == "kuaidi100") | .id // empty')
    
    if [ -z "$MAPPING_ID" ]; then
        log_error "未找到韵达快递的快递100映射"
        exit 1
    fi
    
    log_success "找到韵达快递的快递100映射ID: $MAPPING_ID"
}

# 测试价格查询（禁用前）
test_price_query_before_disable() {
    log_info "测试禁用前的价格查询..."
    
    response=$(curl -s -X POST "${BASE_URL}/api/v1/express/price" \
        -H "Authorization: Bearer $USER_TOKEN" \
        -H "Content-Type: application/json" \
        -d '{
            "from_province": "广东省",
            "from_city": "深圳市",
            "to_province": "北京市",
            "to_city": "北京市",
            "weight": 1.0,
            "express_code": "YD"
        }')
    
    # 检查是否有快递100的价格结果
    kuaidi100_result=$(echo "$response" | jq -r '.data[] | select(.provider == "kuaidi100") | .provider // empty')
    
    if [ -n "$kuaidi100_result" ]; then
        log_success "禁用前：快递100可以查询韵达价格"
    else
        log_warning "禁用前：快递100无法查询韵达价格（可能已被禁用）"
    fi
}

# 禁用韵达快递的快递100映射
disable_yunda_kuaidi100_mapping() {
    log_info "禁用韵达快递的快递100映射..."
    
    response=$(curl -s -X PUT "${BASE_URL}/api/v1/admin/express/mappings/${MAPPING_ID}" \
        -H "Authorization: Bearer $ADMIN_TOKEN" \
        -H "Content-Type: application/json" \
        -d '{
            "is_supported": false
        }')
    
    success=$(echo "$response" | jq -r '.success // false')
    
    if [ "$success" = "true" ]; then
        log_success "韵达快递的快递100映射已禁用"
    else
        log_error "禁用映射失败: $response"
        exit 1
    fi
}

# 测试价格查询（禁用后）
test_price_query_after_disable() {
    log_info "测试禁用后的价格查询..."
    
    # 等待1秒确保缓存刷新
    sleep 1
    
    response=$(curl -s -X POST "${BASE_URL}/api/v1/express/price" \
        -H "Authorization: Bearer $USER_TOKEN" \
        -H "Content-Type: application/json" \
        -d '{
            "from_province": "广东省",
            "from_city": "深圳市",
            "to_province": "北京市",
            "to_city": "北京市",
            "weight": 1.0,
            "express_code": "YD"
        }')
    
    # 检查是否还有快递100的价格结果
    kuaidi100_result=$(echo "$response" | jq -r '.data[] | select(.provider == "kuaidi100") | .provider // empty')
    
    if [ -z "$kuaidi100_result" ]; then
        log_success "✅ 禁用后：快递100无法查询韵达价格（修复生效）"
        return 0
    else
        log_error "❌ 禁用后：快递100仍可查询韵达价格（修复未生效）"
        return 1
    fi
}

# 测试获取支持的快递公司列表
test_supported_companies() {
    log_info "测试获取快递100支持的快递公司列表..."
    
    response=$(curl -s -X GET "${BASE_URL}/api/v1/express/mapping/providers/kuaidi100/companies" \
        -H "Authorization: Bearer $USER_TOKEN")
    
    # 检查韵达是否还在支持列表中
    yunda_supported=$(echo "$response" | jq -r '.data[] | select(.company_code == "YD") | .company_code // empty')
    
    if [ -z "$yunda_supported" ]; then
        log_success "✅ 韵达已从快递100支持列表中移除"
        return 0
    else
        log_error "❌ 韵达仍在快递100支持列表中"
        return 1
    fi
}

# 恢复韵达快递的快递100映射
restore_yunda_kuaidi100_mapping() {
    log_info "恢复韵达快递的快递100映射..."
    
    response=$(curl -s -X PUT "${BASE_URL}/api/v1/admin/express/mappings/${MAPPING_ID}" \
        -H "Authorization: Bearer $ADMIN_TOKEN" \
        -H "Content-Type: application/json" \
        -d '{
            "is_supported": true
        }')
    
    success=$(echo "$response" | jq -r '.success // false')
    
    if [ "$success" = "true" ]; then
        log_success "韵达快递的快递100映射已恢复"
    else
        log_warning "恢复映射失败: $response"
    fi
}

# 手动刷新缓存测试
test_manual_cache_refresh() {
    log_info "测试手动刷新缓存..."
    
    response=$(curl -s -X POST "${BASE_URL}/api/v1/admin/express/mapping/cache/refresh" \
        -H "Authorization: Bearer $ADMIN_TOKEN")
    
    success=$(echo "$response" | jq -r '.success // false')
    
    if [ "$success" = "true" ]; then
        log_success "缓存手动刷新成功"
    else
        log_warning "缓存手动刷新失败: $response"
    fi
}

# 主测试流程
main() {
    log_info "开始快递公司映射禁用功能测试..."
    
    # 检查依赖
    if ! command -v jq &> /dev/null; then
        log_error "需要安装jq工具: brew install jq 或 apt-get install jq"
        exit 1
    fi
    
    # 获取认证Token
    get_admin_token
    get_user_token
    
    # 查找测试目标映射
    find_yunda_kuaidi100_mapping
    
    # 测试流程
    test_price_query_before_disable
    disable_yunda_kuaidi100_mapping
    
    # 测试禁用效果
    test_result=0
    test_price_query_after_disable || test_result=1
    test_supported_companies || test_result=1
    
    # 测试手动缓存刷新
    test_manual_cache_refresh
    
    # 恢复映射（清理测试环境）
    restore_yunda_kuaidi100_mapping
    
    # 输出测试结果
    echo ""
    if [ $test_result -eq 0 ]; then
        log_success "🎉 所有测试通过！快递公司映射禁用功能修复成功"
    else
        log_error "💥 测试失败！快递公司映射禁用功能仍有问题"
        exit 1
    fi
}

# 运行测试
main "$@"
