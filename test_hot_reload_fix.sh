#!/bin/bash

# 测试热重启修复效果脚本

echo "🔧 测试快递鸟申通映射热重启修复效果"
echo "=================================================="

# 数据库连接信息
DB_HOST="*************"
DB_PORT="5432"
DB_USER="postgres"
DB_NAME="go_kuaidi"
DB_PASSWORD="gjx6ngf4"

# API测试地址
API_URL="http://localhost:8081"

echo "📋 1. 检查当前申通映射状态..."
CURRENT_STATUS=$(PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -t -c "
SELECT m.is_supported 
FROM express_company_provider_mappings m
JOIN express_companies c ON m.company_id = c.id
JOIN express_providers p ON m.provider_id = p.id
WHERE c.code = 'STO' AND p.code = 'kuaidiniao';
" | tr -d ' ')

echo "   当前申通映射状态: $CURRENT_STATUS"

if [ "$CURRENT_STATUS" = "t" ]; then
    echo "📋 2. 禁用申通映射..."
    PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "
    UPDATE express_company_provider_mappings 
    SET is_supported = false, updated_at = NOW()
    FROM express_companies c, express_providers p
    WHERE express_company_provider_mappings.company_id = c.id 
      AND express_company_provider_mappings.provider_id = p.id
      AND c.code = 'STO' 
      AND p.code = 'kuaidiniao';
    "
    NEW_STATUS="disabled"
else
    echo "📋 2. 启用申通映射..."
    PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "
    UPDATE express_company_provider_mappings 
    SET is_supported = true, updated_at = NOW()
    FROM express_companies c, express_providers p
    WHERE express_company_provider_mappings.company_id = c.id 
      AND express_company_provider_mappings.provider_id = p.id
      AND c.code = 'STO' 
      AND p.code = 'kuaidiniao';
    "
    NEW_STATUS="enabled"
fi

echo "   申通映射已${NEW_STATUS}"

echo ""
echo "📋 3. 等待热重启生效（最多30秒）..."

# 测试函数
test_sto_mapping() {
    # 查看日志中的快递鸟映射信息
    tail -n 50 logs/go-kuaidi-local-*.log | grep -E "(缓存快递公司.*STO|skipped_companies.*STO|映射过滤.*STO)" | tail -5
}

# 等待并测试
for i in {1..30}; do
    echo -n "."
    sleep 1
    
    # 每5秒检查一次
    if [ $((i % 5)) -eq 0 ]; then
        echo ""
        echo "   检查第 $i 秒..."
        
        # 检查日志中的映射状态
        RECENT_LOGS=$(test_sto_mapping)
        if [ ! -z "$RECENT_LOGS" ]; then
            echo "   最新日志:"
            echo "$RECENT_LOGS"
        fi
        
        # 检查缓存状态
        CACHE_STATUS=$(tail -n 100 logs/go-kuaidi-local-*.log | grep -E "缓存快递公司.*STO" | tail -1)
        if [ ! -z "$CACHE_STATUS" ]; then
            echo "   缓存状态: $CACHE_STATUS"
        fi
    fi
done

echo ""
echo ""
echo "📋 4. 最终测试结果..."

# 检查最新的映射状态
echo "   数据库状态:"
PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "
SELECT 
    c.code as company_code,
    p.code as provider_code,
    m.is_supported,
    m.updated_at
FROM express_company_provider_mappings m
JOIN express_companies c ON m.company_id = c.id
JOIN express_providers p ON m.provider_id = p.id
WHERE c.code = 'STO' AND p.code = 'kuaidiniao';
"

echo ""
echo "   最新日志状态:"
FINAL_LOGS=$(tail -n 100 logs/go-kuaidi-local-*.log | grep -E "(缓存快递公司.*STO|skipped_companies.*STO|映射过滤.*STO)" | tail -10)
if [ ! -z "$FINAL_LOGS" ]; then
    echo "$FINAL_LOGS"
else
    echo "   未找到相关日志"
fi

echo ""
echo "📋 5. 测试建议..."
if [ "$NEW_STATUS" = "enabled" ]; then
    echo "   ✅ 申通映射已启用，查价结果应该包含申通快递"
    echo "   🔍 检查日志中是否不再有 'skipped_companies.*STO'"
else
    echo "   ❌ 申通映射已禁用，查价结果应该不包含申通快递"
    echo "   🔍 检查日志中是否有 'skipped_companies.*STO'"
fi

echo ""
echo "🎯 测试完成！请观察后续查价请求的日志变化。"
