#!/bin/bash

# =====================================================
# Go-Kuaidi 单快递公司查询测试脚本
# 测试目标：验证单快递公司查询功能是否正常工作
# =====================================================

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 配置
BASE_URL="http://localhost:8081"
LOGIN_USERNAME="admin"
LOGIN_PASSWORD="1104030777+.aA..@"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_test() {
    echo -e "${PURPLE}[TEST]${NC} $1"
}

# 分割线
print_separator() {
    echo -e "${CYAN}=================================================${NC}"
}

print_section() {
    echo -e "${CYAN}=============== $1 ===============${NC}"
}

# 获取JWT Token
get_jwt_token() {
    log_info "正在获取JWT Token..."
    
    TOKEN_RESPONSE=$(curl -s -X POST "${BASE_URL}/api/v1/auth/login" \
        -H "Content-Type: application/json" \
        -d "{
            \"username\": \"${LOGIN_USERNAME}\",
            \"password\": \"${LOGIN_PASSWORD}\"
        }")

    TOKEN=$(echo $TOKEN_RESPONSE | jq -r '.access_token')

    if [ "$TOKEN" = "null" ] || [ -z "$TOKEN" ]; then
        log_error "获取Token失败: $TOKEN_RESPONSE"
        exit 1
    fi

    log_success "Token获取成功"
}

# 测试单快递公司查询
test_single_express_query() {
    local express_code=$1
    local express_name=$2
    
    log_test "🔍 测试单快递公司查询: ${express_name} (${express_code})"
    
    local response=$(curl -s -X POST "${BASE_URL}/api/gateway/execute" \
        -H "Content-Type: application/json" \
        -d "{
            \"apiMethod\": \"QUERY_PRICE\",
            \"clientType\": \"web\",
            \"accessToken\": \"$TOKEN\",
            \"businessParams\": {
                \"express_code\": \"${express_code}\",
                \"from_province\": \"广东省\",
                \"from_city\": \"深圳市\",
                \"from_district\": \"南山区\",
                \"to_province\": \"北京市\",
                \"to_city\": \"北京市\",
                \"to_district\": \"朝阳区\",
                \"weight\": 1.5,
                \"goods_name\": \"测试商品\"
            }
        }")
    
    echo "完整响应:"
    echo "$response" | jq '.'
    echo ""
    
    local success=$(echo "$response" | jq -r '.success')
    local data_length=$(echo "$response" | jq -r '.data | length')
    
    if [ "$success" = "true" ] && [ "$data_length" != "null" ]; then
        log_success "✅ 单快递查询成功 - 返回 ${data_length} 个结果"
        
        # 验证返回的快递公司是否匹配
        local returned_codes=$(echo "$response" | jq -r '.data[].express_code' | sort | uniq)
        log_info "返回的快递代码: ${returned_codes}"
        
        # 检查是否只返回了指定的快递公司
        local unique_codes=$(echo "$returned_codes" | wc -l | tr -d ' ')
        if [ "$unique_codes" = "1" ] && echo "$returned_codes" | grep -q "$express_code"; then
            log_success "✅ 单快递查询验证通过 - 只返回指定快递公司"
        elif [ "$unique_codes" = "1" ]; then
            log_warning "⚠️ 返回了单个快递公司，但代码不匹配 - 预期: ${express_code}, 实际: ${returned_codes}"
        else
            log_error "❌ 单快递查询验证失败 - 返回了多个快递公司: ${returned_codes}"
        fi
        
        # 显示价格信息
        echo "$response" | jq -r '.data[] | "  - \(.express_name): ¥\(.price), 订单码: \(.order_code)"'
        
    else
        log_error "❌ 单快递查询失败"
        local error_msg=$(echo "$response" | jq -r '.message // .error // "未知错误"')
        log_error "错误信息: ${error_msg}"
    fi
    
    echo ""
}

# 测试全部快递公司查询
test_all_express_query() {
    log_test "🔍 测试全部快递公司查询 (不指定express_code)"
    
    local response=$(curl -s -X POST "${BASE_URL}/api/gateway/execute" \
        -H "Content-Type: application/json" \
        -d "{
            \"apiMethod\": \"QUERY_PRICE\",
            \"clientType\": \"web\",
            \"accessToken\": \"$TOKEN\",
            \"businessParams\": {
                \"from_province\": \"广东省\",
                \"from_city\": \"深圳市\",
                \"from_district\": \"南山区\",
                \"to_province\": \"北京市\",
                \"to_city\": \"北京市\",
                \"to_district\": \"朝阳区\",
                \"weight\": 1.5,
                \"goods_name\": \"测试商品\"
            }
        }")
    
    local success=$(echo "$response" | jq -r '.success')
    local data_length=$(echo "$response" | jq -r '.data | length')
    
    if [ "$success" = "true" ] && [ "$data_length" != "null" ]; then
        log_success "✅ 全部快递查询成功 - 返回 ${data_length} 个结果"
        
        # 显示返回的快递公司代码
        local returned_codes=$(echo "$response" | jq -r '.data[].express_code' | sort | uniq | tr '\n' ' ')
        log_info "返回的快递公司: ${returned_codes}"
        
        # 验证是否返回了多个快递公司
        local unique_codes=$(echo "$returned_codes" | wc -w)
        if [ "$unique_codes" -gt 1 ]; then
            log_success "✅ 全部快递查询验证通过 - 返回了 ${unique_codes} 个不同的快递公司"
        else
            log_warning "⚠️ 全部快递查询只返回了 ${unique_codes} 个快递公司"
        fi
        
        # 显示前5个价格最优的选项
        echo "前5个价格最优选项:"
        echo "$response" | jq -r '.data[:5] | .[] | "  - \(.express_name): ¥\(.price), 订单码: \(.order_code)"'
        
    else
        log_error "❌ 全部快递查询失败"
        local error_msg=$(echo "$response" | jq -r '.message // .error // "未知错误"')
        log_error "错误信息: ${error_msg}"
    fi
    
    echo ""
}

# 测试不同接口的单快递查询
test_legacy_api_single_express() {
    local express_code=$1
    local express_name=$2
    
    log_test "🔍 测试传统API单快递查询: ${express_name} (${express_code})"
    
    local response=$(curl -s -X POST "${BASE_URL}/api/v1/express/price" \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer $TOKEN" \
        -d "{
            \"express_code\": \"${express_code}\",
            \"from_province\": \"广东省\",
            \"from_city\": \"深圳市\",
            \"from_district\": \"南山区\",
            \"to_province\": \"北京市\",
            \"to_city\": \"北京市\",
            \"to_district\": \"朝阳区\",
            \"weight\": 1.5,
            \"goods_name\": \"测试商品\"
        }")
    
    local success=$(echo "$response" | jq -r '.success')
    local data_length=$(echo "$response" | jq -r '.data | length')
    
    if [ "$success" = "true" ] && [ "$data_length" != "null" ]; then
        log_success "✅ 传统API单快递查询成功 - 返回 ${data_length} 个结果"
        
        # 验证返回的快递公司是否匹配
        local returned_codes=$(echo "$response" | jq -r '.data[].express_code' | sort | uniq)
        log_info "返回的快递代码: ${returned_codes}"
        
        # 显示价格信息
        echo "$response" | jq -r '.data[] | "  - \(.express_name): ¥\(.price), 订单码: \(.order_code)"'
        
    else
        log_error "❌ 传统API单快递查询失败"
        local error_msg=$(echo "$response" | jq -r '.message // .error // "未知错误"')
        log_error "错误信息: ${error_msg}"
    fi
    
    echo ""
}

# 主函数
main() {
    print_separator
    log_info "🚀 Go-Kuaidi 单快递公司查询功能测试"
    print_separator
    
    # 检查依赖
    for cmd in curl jq; do
        if ! command -v $cmd &> /dev/null; then
            log_error "$cmd 命令未找到，请安装 $cmd"
            exit 1
        fi
    done
    
    # 获取认证Token
    get_jwt_token
    
    print_section "统一网关API测试"
    
    # 测试常见快递公司的单快递查询
    test_single_express_query "SF" "顺丰速运"
    test_single_express_query "ZTO" "中通快递"
    test_single_express_query "YTO" "圆通速递"
    test_single_express_query "STO" "申通快递"
    test_single_express_query "YD" "韵达速递"
    
    print_section "全部快递查询对比测试"
    
    # 测试全部快递查询
    test_all_express_query
    
    print_section "传统API测试"
    
    # 测试传统API的单快递查询
    test_legacy_api_single_express "SF" "顺丰速运"
    test_legacy_api_single_express "ZTO" "中通快递"
    
    print_separator
    log_success "✅ 单快递公司查询功能测试完成！"
    print_separator
}

# 执行主函数
main