#!/bin/bash

echo "🧪 测试失败原因功能"
echo "========================"

# 测试1: 查看数据库中的失败记录
echo "📊 查看数据库中的失败记录:"
PGPASSWORD=gjx6ngf4 psql -h 8.138.252.193 -p 5432 -U postgres -d go_kuaidi -c "
SELECT from_province, to_province, provider, express_code, weight_kg, 
       error_message, COUNT(*) as count
FROM weight_tier_query_logs 
WHERE success = false 
  AND created_at >= NOW() - INTERVAL '30 days'
  AND error_message IS NOT NULL 
  AND error_message != ''
GROUP BY from_province, to_province, provider, express_code, weight_kg, error_message
ORDER BY count DESC
LIMIT 5;
"

echo ""
echo "🔍 测试API返回失败原因:"

# 测试2: 调用正确的API端点查看是否返回失败原因
echo "测试缓存详情API (有缓存记录的路线):"
curl -s "http://localhost:8081/api/v1/weight-cache/details?provider=yuntong&express_code=JT&from_province=上海市&to_province=北京市&weight_kg=5&page=1&page_size=5" | python3 -c "
import json
import sys
try:
    data = json.load(sys.stdin)
    if data.get('success'):
        routes = data.get('data', {}).get('routes', [])
        print(f'找到 {len(routes)} 条缓存记录')
        for route in routes:
            print(f'路线: {route.get(\"route\", \"N/A\")}')
            print(f'重量: {route.get(\"weight_kg\", \"N/A\")}kg')
            print(f'失败次数: {route.get(\"failed_queries\", 0)}')
            print(f'总查询次数: {route.get(\"total_queries\", 0)}')
            failure_reasons = route.get('failure_reasons', [])
            if failure_reasons:
                print('失败原因:')
                for reason in failure_reasons:
                    print(f'  - {reason.get(\"error_message\", \"N/A\")} (出现{reason.get(\"count\", 0)}次)')
                    print(f'    最后出现: {reason.get(\"last_occurred\", \"N/A\")}')
                    print(f'    来源: {reason.get(\"source\", \"N/A\")}')
            else:
                print('无失败原因记录')
            print('---')
    else:
        print(f'API调用失败: {data.get(\"message\", \"未知错误\")}')
except Exception as e:
    print(f'解析JSON失败: {e}')
"

echo ""
echo "测试另一个有缓存记录的路线:"
curl -s "http://localhost:8081/api/v1/weight-cache/details?provider=yuntong&express_code=STO&from_province=江苏省&to_province=江苏省&weight_kg=2&page=1&page_size=5" | python3 -c "
import json
import sys
try:
    data = json.load(sys.stdin)
    if data.get('success'):
        routes = data.get('data', {}).get('routes', [])
        print(f'找到 {len(routes)} 条缓存记录')
        for route in routes:
            print(f'路线: {route.get(\"route\", \"N/A\")}')
            print(f'重量: {route.get(\"weight_kg\", \"N/A\")}kg')
            print(f'失败次数: {route.get(\"failed_queries\", 0)}')
            print(f'总查询次数: {route.get(\"total_queries\", 0)}')
            failure_reasons = route.get('failure_reasons', [])
            if failure_reasons:
                print('失败原因:')
                for reason in failure_reasons:
                    print(f'  - {reason.get(\"error_message\", \"N/A\")} (出现{reason.get(\"count\", 0)}次)')
                    print(f'    最后出现: {reason.get(\"last_occurred\", \"N/A\")}')
                    print(f'    来源: {reason.get(\"source\", \"N/A\")}')
            else:
                print('无失败原因记录')
            print('---')
    else:
        print(f'API调用失败: {data.get(\"message\", \"未知错误\")}')
except Exception as e:
    print(f'解析JSON失败: {e}')
"

echo ""
echo "✅ 测试完成"
