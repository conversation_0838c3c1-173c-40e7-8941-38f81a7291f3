#!/bin/bash

# 批量重新推送快递100回调脚本

set -e

echo "🔄 批量重新推送快递100回调"
echo "================================"

# 数据库连接信息
DB_HOST="*************"
DB_PORT="5432"
DB_USER="postgres"
DB_PASSWORD="gjx6ngf4"
CALLBACK_DB="callback_receiver"
MAIN_DB="go_kuaidi"

# API配置
API_BASE_URL="http://localhost:8081"

# 问题订单列表
ORDERS=(
    "DPK202580385165"
    "DPK202580340237"
    "DPK202579994323"
    "DPK202579941633"
    "DPK202579931694"
    "DPK202579913553"
    "DPK202579712495"
    "DPK202572917023"
    "DPK202572911053"
    "DPK202572900214"
    "DPK202572776229"
    "DPK202571899463"
    "DPK202571736764"
)

success_count=0
failed_count=0

echo "📊 开始处理 ${#ORDERS[@]} 个订单..."
echo ""

for tracking_no in "${ORDERS[@]}"; do
    echo "🔍 处理订单: $tracking_no"
    
    # 1. 检查主数据库中的当前状态
    CURRENT_STATUS=$(PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $MAIN_DB -t -c "
    SELECT status FROM order_records WHERE tracking_no = '$tracking_no';
    " | tr -d ' ')
    
    if [ -z "$CURRENT_STATUS" ]; then
        echo "   ❌ 主数据库中没有找到订单"
        ((failed_count++))
        continue
    fi
    
    echo "   📊 当前状态: $CURRENT_STATUS"
    
    # 2. 获取最新的回调数据
    CALLBACK_INFO=$(PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $CALLBACK_DB -t -c "
    SELECT id, raw_body 
    FROM callback_raw_data 
    WHERE provider = 'kuaidi100' 
      AND raw_body LIKE '%$tracking_no%' 
      AND processed = true
    ORDER BY received_at DESC 
    LIMIT 1;
    " | sed 's/^ *//' | grep -v '^$')
    
    if [ -z "$CALLBACK_INFO" ]; then
        echo "   ❌ 没有找到相关回调"
        ((failed_count++))
        continue
    fi
    
    # 提取回调ID和数据
    CALLBACK_ID=$(echo "$CALLBACK_INFO" | cut -d'|' -f1 | tr -d ' ')
    CALLBACK_DATA=$(echo "$CALLBACK_INFO" | cut -d'|' -f2-)
    
    echo "   📤 找到回调ID: $CALLBACK_ID"
    
    # 3. 重新推送回调
    echo "   🔄 重新推送回调..."
    
    RESPONSE=$(curl -s -w "\nHTTP_CODE:%{http_code}" -X POST "$API_BASE_URL/api/v1/callbacks/kuaidi100" \
        -H "Content-Type: application/x-www-form-urlencoded; charset=UTF-8" \
        -H "User-Agent: Batch-Repush-Script/1.0" \
        -H "X-Repush-Callback: true" \
        -H "X-Repush-Order: $tracking_no" \
        -d "$CALLBACK_DATA" 2>/dev/null || echo "CURL_ERROR")
    
    if [ "$RESPONSE" = "CURL_ERROR" ]; then
        echo "   ❌ 网络请求失败"
        ((failed_count++))
        continue
    fi
    
    # 提取HTTP状态码
    HTTP_CODE=$(echo "$RESPONSE" | grep "HTTP_CODE:" | cut -d: -f2)
    
    if [ "$HTTP_CODE" = "200" ]; then
        echo "   ✅ 回调发送成功"
        
        # 等待系统处理
        sleep 2
        
        # 检查状态是否更新
        NEW_STATUS=$(PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $MAIN_DB -t -c "
        SELECT status FROM order_records WHERE tracking_no = '$tracking_no';
        " | tr -d ' ')
        
        echo "   📊 重新推送后状态: $NEW_STATUS"
        
        if [ "$NEW_STATUS" != "$CURRENT_STATUS" ]; then
            echo "   🎉 状态更新成功: $CURRENT_STATUS -> $NEW_STATUS"
            ((success_count++))
        else
            echo "   ⚠️  状态未更新，可能需要手动检查"
            ((failed_count++))
        fi
    else
        echo "   ❌ 回调发送失败，HTTP状态码: $HTTP_CODE"
        ((failed_count++))
    fi
    
    echo ""
    sleep 1
done

echo "📊 处理结果统计:"
echo "   ✅ 成功处理: $success_count 个订单"
echo "   ❌ 处理失败: $failed_count 个订单"
echo "   📦 总计订单: ${#ORDERS[@]} 个"

echo ""
echo "📋 最终状态检查:"
echo ""

for tracking_no in "${ORDERS[@]}"; do
    FINAL_INFO=$(PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $MAIN_DB -t -c "
    SELECT customer_order_no || ' | ' || status || ' | ' || updated_at
    FROM order_records 
    WHERE tracking_no = '$tracking_no';
    " | sed 's/^ *//' | grep -v '^$')
    
    if [ -n "$FINAL_INFO" ]; then
        echo "📦 $tracking_no: $FINAL_INFO"
    fi
done

echo ""
echo "🏁 批量重新推送完成！"
