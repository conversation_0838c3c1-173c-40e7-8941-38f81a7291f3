#!/bin/bash

# 测试单个订单重新推送脚本

echo "🔄 测试单个订单重新推送"
echo "========================"

# 测试订单
TRACKING_NO="DPK202580385165"
API_BASE_URL="http://localhost:8081"

echo "📦 测试订单: $TRACKING_NO"

# 1. 检查当前状态
echo "📊 1. 检查主数据库当前状态..."
CURRENT_STATUS=$(PGPASSWORD=gjx6ngf4 psql -h ************* -p 5432 -U postgres -d go_kuaidi -t -c "
SELECT customer_order_no || ' | ' || status || ' | ' || updated_at
FROM order_records 
WHERE tracking_no = '$TRACKING_NO';
" | sed 's/^ *//' | grep -v '^$')

echo "   当前状态: $CURRENT_STATUS"

# 2. 获取最新回调数据
echo "📤 2. 获取最新回调数据..."
CALLBACK_DATA=$(PGPASSWORD=gjx6ngf4 psql -h ************* -p 5432 -U postgres -d callback_receiver -t -c "
SELECT raw_body 
FROM callback_raw_data 
WHERE provider = 'kuaidi100' 
  AND raw_body LIKE '%$TRACKING_NO%' 
  AND processed = true
ORDER BY received_at DESC 
LIMIT 1;
" | sed 's/^ *//' | grep -v '^$')

if [ -z "$CALLBACK_DATA" ]; then
    echo "   ❌ 没有找到回调数据"
    exit 1
fi

echo "   ✅ 找到回调数据"

# 3. 发送重新推送请求
echo "🔄 3. 发送重新推送请求..."

RESPONSE=$(curl -s -w "\nHTTP_CODE:%{http_code}" -X POST "$API_BASE_URL/api/v1/callbacks/kuaidi100" \
    -H "Content-Type: application/x-www-form-urlencoded; charset=UTF-8" \
    -H "User-Agent: Test-Repush-Script/1.0" \
    -H "X-Repush-Callback: true" \
    -H "X-Repush-Order: $TRACKING_NO" \
    -d "$CALLBACK_DATA")

# 提取HTTP状态码
HTTP_CODE=$(echo "$RESPONSE" | grep "HTTP_CODE:" | cut -d: -f2)
RESPONSE_BODY=$(echo "$RESPONSE" | grep -v "HTTP_CODE:")

echo "   HTTP状态码: $HTTP_CODE"
echo "   响应内容: $RESPONSE_BODY"

if [ "$HTTP_CODE" = "200" ]; then
    echo "   ✅ 重新推送成功"
    
    echo "⏳ 4. 等待3秒让系统处理..."
    sleep 3
    
    echo "📊 5. 检查更新后的状态..."
    NEW_STATUS=$(PGPASSWORD=gjx6ngf4 psql -h ************* -p 5432 -U postgres -d go_kuaidi -t -c "
    SELECT customer_order_no || ' | ' || status || ' | ' || updated_at
    FROM order_records 
    WHERE tracking_no = '$TRACKING_NO';
    " | sed 's/^ *//' | grep -v '^$')
    
    echo "   更新后状态: $NEW_STATUS"
    
    if [ "$NEW_STATUS" != "$CURRENT_STATUS" ]; then
        echo "   🎉 状态更新成功！"
    else
        echo "   ⚠️  状态未更新，可能需要检查回调处理逻辑"
    fi
else
    echo "   ❌ 重新推送失败"
fi

echo ""
echo "🏁 测试完成！"
