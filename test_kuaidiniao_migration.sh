#!/bin/bash

# 快递鸟供应商迁移验证测试脚本
# 验证快递鸟供应商是否正确从标准查价接口迁移到实时查价接口

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 基础配置
BASE_URL="http://localhost:8080"
USERNAME="mywl"
CLIENT_TYPE="web"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# 测试标准查价接口（应该不包含快递鸟）
test_standard_price_query() {
    log_info "🔍 测试标准查价接口（QUERY_PRICE）- 应该不包含快递鸟供应商"
    
    local response=$(curl -s -X POST "${BASE_URL}/api/gateway/execute" \
        -H "Content-Type: application/json" \
        -d '{
            "apiMethod": "QUERY_PRICE",
            "clientType": "'${CLIENT_TYPE}'",
            "username": "'${USERNAME}'",
            "businessParams": {
                "sender": {
                    "province": "广东省",
                    "city": "深圳市",
                    "district": "南山区"
                },
                "receiver": {
                    "province": "北京市",
                    "city": "北京市",
                    "district": "朝阳区"
                },
                "package": {
                    "weight": 1.0,
                    "length": 10,
                    "width": 10,
                    "height": 10
                }
            }
        }')
    
    echo "标准查价接口响应："
    echo "$response" | jq '.'
    
    # 检查响应中是否包含快递鸟供应商
    local kuaidiniao_count=$(echo "$response" | jq -r '.data[]? | select(.provider == "kuaidiniao" or .provider == "快递鸟")' | wc -l)
    
    if [ "$kuaidiniao_count" -eq 0 ]; then
        log_success "✅ 标准查价接口正确排除了快递鸟供应商"
    else
        log_error "❌ 标准查价接口仍然包含快递鸟供应商（发现 $kuaidiniao_count 条记录）"
    fi
    
    echo ""
}

# 测试实时查价接口（应该包含快递鸟）
test_realtime_price_query() {
    log_info "🔍 测试实时查价接口（QUERY_REALTIME_PRICE）- 应该包含快递鸟供应商"
    
    local response=$(curl -s -X POST "${BASE_URL}/api/gateway/execute" \
        -H "Content-Type: application/json" \
        -d '{
            "apiMethod": "QUERY_REALTIME_PRICE",
            "clientType": "'${CLIENT_TYPE}'",
            "username": "'${USERNAME}'",
            "businessParams": {
                "sender": {
                    "name": "张三",
                    "mobile": "***********",
                    "province": "广东省",
                    "city": "深圳市",
                    "district": "南山区",
                    "address": "科技园南区"
                },
                "receiver": {
                    "name": "李四",
                    "mobile": "***********",
                    "province": "北京市",
                    "city": "北京市",
                    "district": "朝阳区",
                    "address": "国贸CBD"
                },
                "package": {
                    "weight": 1.0,
                    "length": 10,
                    "width": 10,
                    "height": 10
                }
            }
        }')
    
    echo "实时查价接口响应："
    echo "$response" | jq '.'
    
    # 检查响应中是否包含快递鸟供应商
    local kuaidiniao_count=$(echo "$response" | jq -r '.data[]? | select(.provider == "kuaidiniao" or .provider == "快递鸟")' | wc -l)
    
    if [ "$kuaidiniao_count" -gt 0 ]; then
        log_success "✅ 实时查价接口正确包含了快递鸟供应商（发现 $kuaidiniao_count 条记录）"
    else
        log_warning "⚠️ 实时查价接口未包含快递鸟供应商（可能是供应商未启用或无支持的快递公司）"
    fi
    
    echo ""
}

# 检查快递鸟供应商配置
check_kuaidiniao_config() {
    log_info "🔍 检查快递鸟供应商配置状态"
    
    # 这里可以添加检查数据库配置的逻辑
    log_info "请确保以下配置已正确设置："
    echo "  - provider.kuaidiniao_enabled = true"
    echo "  - provider_kuaidiniao.e_business_id = 您的商户ID"
    echo "  - provider_kuaidiniao.api_key = 您的API密钥"
    echo "  - provider_kuaidiniao.base_url = https://api.kdniao.com/api/dist"
    echo ""
}

# 主函数
main() {
    echo "🚀 快递鸟供应商迁移验证测试"
    echo "=================================="
    echo ""
    
    # 检查服务是否运行
    if ! curl -s "${BASE_URL}/health" > /dev/null; then
        log_error "服务未运行，请先启动服务"
        exit 1
    fi
    
    log_success "服务运行正常"
    echo ""
    
    # 检查配置
    check_kuaidiniao_config
    
    # 执行测试
    test_standard_price_query
    test_realtime_price_query
    
    echo "=================================="
    log_info "测试完成！"
    echo ""
    log_info "预期结果："
    echo "  ✅ 标准查价接口（QUERY_PRICE）不应包含快递鸟供应商"
    echo "  ✅ 实时查价接口（QUERY_REALTIME_PRICE）应包含快递鸟供应商"
    echo ""
    log_info "如果快递鸟供应商未在实时查价接口中出现，请检查："
    echo "  1. 快递鸟供应商是否已启用（provider.kuaidiniao_enabled = true）"
    echo "  2. 快递鸟供应商配置是否正确"
    echo "  3. 快递鸟供应商是否有支持的快递公司映射"
}

# 执行主函数
main "$@"
