#!/bin/bash

# 验证回调修复效果脚本

set -e

echo "🔍 验证回调修复效果"
echo "========================"

# API配置
API_BASE_URL="http://localhost:8081"

# 数据库连接信息
DB_HOST="*************"
DB_PORT="5432"
DB_USER="postgres"
DB_PASSWORD="gjx6ngf4"
MAIN_DB="go_kuaidi"

# 原始问题订单列表
ORIGINAL_ORDERS=(
    "DPK202580385165"
    "DPK202580340237"
    "DPK202579994323"
    "DPK202579941633"
    "DPK202579931694"
    "DPK202579913553"
    "DPK202579712495"
    "DPK202572917023"
    "DPK202572911053"
    "DPK202572900214"
    "DPK202572776229"
    "DPK202571899463"
    "DPK202571736764"
)

echo "📊 1. 检查原始问题订单的当前状态..."
echo ""

updated_count=0
still_awaiting_count=0
not_found_count=0

for tracking_no in "${ORIGINAL_ORDERS[@]}"; do
    ORDER_INFO=$(PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $MAIN_DB -t -c "
    SELECT customer_order_no || '|' || status || '|' || updated_at
    FROM order_records 
    WHERE tracking_no = '$tracking_no';
    " 2>/dev/null | sed 's/^ *//' | grep -v '^$' | head -1)
    
    if [ -z "$ORDER_INFO" ]; then
        echo "❌ $tracking_no: 订单未找到"
        ((not_found_count++))
    else
        CUSTOMER_ORDER_NO=$(echo "$ORDER_INFO" | cut -d'|' -f1)
        STATUS=$(echo "$ORDER_INFO" | cut -d'|' -f2)
        UPDATED_AT=$(echo "$ORDER_INFO" | cut -d'|' -f3)
        
        if [ "$STATUS" = "awaiting_pickup" ]; then
            echo "⚠️  $tracking_no: $CUSTOMER_ORDER_NO | $STATUS | $UPDATED_AT"
            ((still_awaiting_count++))
        else
            echo "✅ $tracking_no: $CUSTOMER_ORDER_NO | $STATUS | $UPDATED_AT"
            ((updated_count++))
        fi
    fi
done

echo ""
echo "📊 原始问题订单状态统计:"
echo "   ✅ 已更新: $updated_count 个订单"
echo "   ⚠️  仍等待揽收: $still_awaiting_count 个订单"
echo "   ❌ 未找到: $not_found_count 个订单"

echo ""
echo "🔍 2. 测试回调一致性检查API..."

# 测试快递100回调一致性
echo "   📤 检查快递100回调一致性..."
KUAIDI100_RESPONSE=$(curl -s "$API_BASE_URL/api/v1/admin/callbacks/consistency/check?provider=kuaidi100&hours=24" || echo "ERROR")

if [ "$KUAIDI100_RESPONSE" = "ERROR" ]; then
    echo "   ❌ API调用失败"
else
    INCONSISTENT_COUNT=$(echo "$KUAIDI100_RESPONSE" | grep -o '"inconsistent_count":[0-9]*' | cut -d':' -f2 || echo "0")
    echo "   📊 快递100不一致回调数量: $INCONSISTENT_COUNT"
    
    if [ "$INCONSISTENT_COUNT" -gt 0 ]; then
        echo "   ⚠️  发现不一致回调，建议进行修复"
    else
        echo "   ✅ 快递100回调一致性正常"
    fi
fi

echo ""
echo "🔍 3. 测试回调统计API..."

STATS_RESPONSE=$(curl -s "$API_BASE_URL/api/v1/admin/callbacks/statistics?provider=kuaidi100&hours=24" || echo "ERROR")

if [ "$STATS_RESPONSE" = "ERROR" ]; then
    echo "   ❌ 统计API调用失败"
else
    echo "   ✅ 回调统计API正常"
    # 可以进一步解析统计数据
fi

echo ""
echo "🔍 4. 检查系统健康状态..."

HEALTH_RESPONSE=$(curl -s "$API_BASE_URL/health" || echo "ERROR")

if [ "$HEALTH_RESPONSE" = "ERROR" ]; then
    echo "   ❌ 健康检查失败"
else
    echo "   ✅ 系统健康状态正常"
fi

echo ""
echo "🔍 5. 检查最近的回调处理日志..."

if [ -f "logs/app.log" ]; then
    echo "   📋 最近的回调处理日志:"
    tail -20 logs/app.log | grep -E "(回调|callback|一致性|consistency)" | tail -5 || echo "   ℹ️  没有找到相关日志"
else
    echo "   ⚠️  日志文件不存在"
fi

echo ""
echo "🔍 6. 验证定时任务是否正常运行..."

# 检查进程是否包含定时任务相关的日志
SCHEDULED_TASK_LOG=$(tail -50 logs/app.log 2>/dev/null | grep -c "定时任务\|scheduled\|超时订单检查\|回调一致性检查" || echo "0")

if [ "$SCHEDULED_TASK_LOG" -gt 0 ]; then
    echo "   ✅ 定时任务正在运行"
else
    echo "   ⚠️  定时任务可能未启动或日志不足"
fi

echo ""
echo "📊 7. 综合评估..."

# 计算修复成功率
if [ ${#ORIGINAL_ORDERS[@]} -gt 0 ]; then
    SUCCESS_RATE=$(echo "scale=1; $updated_count * 100 / ${#ORIGINAL_ORDERS[@]}" | bc -l 2>/dev/null || echo "0")
    echo "   📈 订单修复成功率: $SUCCESS_RATE% ($updated_count/${#ORIGINAL_ORDERS[@]})"
fi

# 评估系统状态
SYSTEM_SCORE=0

if [ "$updated_count" -gt 6 ]; then
    ((SYSTEM_SCORE += 3))
    echo "   ✅ 订单修复效果良好 (+3分)"
elif [ "$updated_count" -gt 3 ]; then
    ((SYSTEM_SCORE += 2))
    echo "   ⚠️  订单修复效果一般 (+2分)"
else
    ((SYSTEM_SCORE += 1))
    echo "   ❌ 订单修复效果较差 (+1分)"
fi

if [ "$KUAIDI100_RESPONSE" != "ERROR" ]; then
    ((SYSTEM_SCORE += 2))
    echo "   ✅ 回调一致性API正常 (+2分)"
fi

if [ "$HEALTH_RESPONSE" != "ERROR" ]; then
    ((SYSTEM_SCORE += 2))
    echo "   ✅ 系统健康状态正常 (+2分)"
fi

if [ "$SCHEDULED_TASK_LOG" -gt 0 ]; then
    ((SYSTEM_SCORE += 2))
    echo "   ✅ 定时任务正常运行 (+2分)"
fi

if [ -f "logs/app.log" ]; then
    ((SYSTEM_SCORE += 1))
    echo "   ✅ 日志记录正常 (+1分)"
fi

echo ""
echo "📊 系统评分: $SYSTEM_SCORE/10"

if [ "$SYSTEM_SCORE" -ge 8 ]; then
    echo "🎉 系统状态优秀！回调修复功能运行良好"
elif [ "$SYSTEM_SCORE" -ge 6 ]; then
    echo "✅ 系统状态良好，回调修复功能基本正常"
elif [ "$SYSTEM_SCORE" -ge 4 ]; then
    echo "⚠️  系统状态一般，建议检查和优化"
else
    echo "❌ 系统状态较差，需要立即检查和修复"
fi

echo ""
echo "💡 建议操作:"

if [ "$still_awaiting_count" -gt 0 ]; then
    echo "   - 对仍处于awaiting_pickup状态的 $still_awaiting_count 个订单进行手动检查"
    echo "   - 这些订单的状态可能是正确的（确实还在等待揽收）"
fi

if [ "$INCONSISTENT_COUNT" -gt 0 ]; then
    echo "   - 使用管理员API修复不一致的回调"
    echo "   - 命令: curl -X POST '$API_BASE_URL/api/v1/admin/callbacks/consistency/batch-fix' -H 'Content-Type: application/json' -d '{\"provider\":\"kuaidi100\",\"hours\":24,\"max_count\":50}'"
fi

echo "   - 定期监控回调一致性状态"
echo "   - 关注系统日志中的错误信息"

echo ""
echo "🏁 验证完成！"
