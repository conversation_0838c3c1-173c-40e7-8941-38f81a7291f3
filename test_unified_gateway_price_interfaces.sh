#!/bin/bash

# =====================================================
# Go-Kuaidi 统一网关查价接口完整测试脚本
# 测试内容：
# 1. 标准查价接口 (QUERY_PRICE) - 支持所有快递公司，使用缓存
# 2. 京东专用查价接口 (QUERY_JD_PRICE) - 专门支持京东和德邦，实时查询
# =====================================================

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 配置
BASE_URL="http://localhost:8081"
LOGIN_USERNAME="admin"
LOGIN_PASSWORD="1104030777+.aA..@"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_test() {
    echo -e "${PURPLE}[TEST]${NC} $1"
}

# 分割线
print_separator() {
    echo -e "${CYAN}=================================================${NC}"
}

# 获取JWT Token
get_jwt_token() {
    log_info "正在获取JWT Token..."
    
    TOKEN_RESPONSE=$(curl -s -X POST "${BASE_URL}/api/v1/auth/login" \
        -H "Content-Type: application/json" \
        -d "{
            \"username\": \"${LOGIN_USERNAME}\",
            \"password\": \"${LOGIN_PASSWORD}\"
        }")

    TOKEN=$(echo $TOKEN_RESPONSE | jq -r '.access_token')

    if [ "$TOKEN" = "null" ] || [ -z "$TOKEN" ]; then
        log_error "获取Token失败: $TOKEN_RESPONSE"
        exit 1
    fi

    log_success "Token获取成功"
}

# 测试标准查价接口
test_standard_price_query() {
    local test_name="$1"
    local business_params="$2"
    
    log_test "🔵 标准查价接口测试: $test_name"
    
    local response=$(curl -s -X POST "${BASE_URL}/api/gateway/execute" \
        -H "Content-Type: application/json" \
        -d "{
            \"apiMethod\": \"QUERY_PRICE\",
            \"clientType\": \"web\",
            \"accessToken\": \"$TOKEN\",
            \"businessParams\": $business_params
        }")
    
    echo "$response" | jq '.'
    
    # 简单的成功检查
    local success=$(echo "$response" | jq -r '.success')
    if [ "$success" = "true" ]; then
        log_success "✅ $test_name - 测试通过"
    else
        log_error "❌ $test_name - 测试失败"
    fi
    
    echo ""
}

# 测试实时查价接口
test_realtime_price_query() {
    local test_name="$1"
    local business_params="$2"

    log_test "🟠 实时查价接口测试: $test_name"

    local response=$(curl -s -X POST "${BASE_URL}/api/gateway/execute" \
        -H "Content-Type: application/json" \
        -d "{
            \"apiMethod\": \"QUERY_REALTIME_PRICE\",
            \"clientType\": \"web\",
            \"accessToken\": \"$TOKEN\",
            \"businessParams\": $business_params
        }")
    
    echo "$response" | jq '.'
    
    # 简单的成功检查
    local success=$(echo "$response" | jq -r '.success')
    if [ "$success" = "true" ]; then
        log_success "✅ $test_name - 测试通过"
    else
        log_error "❌ $test_name - 测试失败"
    fi
    
    echo ""
}

# 主函数
main() {
    print_separator
    log_info "🚀 开始Go-Kuaidi统一网关查价接口完整测试"
    print_separator
    
    # 获取认证Token
    get_jwt_token
    
    print_separator
    log_info "📋 第一部分：标准查价接口测试 (QUERY_PRICE)"
    log_info "特点：支持所有快递公司（除JD/DBL），使用缓存机制"
    print_separator
    
    # 标准查价接口测试用例
    
    # 测试1：简单查价 - 只提供基本信息
    test_standard_price_query "简单查价测试" '{
        "from_province": "北京市",
        "from_city": "北京市",
        "to_province": "上海市",
        "to_city": "上海市",
        "weight": 1.0,
        "goods_name": "测试物品"
    }'
    
    # 👉 仅保留一个核心场景，更多场景请根据需要手动添加
    
    print_separator
    log_info "📋 第二部分：实时查价接口测试 (QUERY_REALTIME_PRICE)"
    log_info "特点：专门支持实时查价快递，实时查询，禁用缓存"
    print_separator
    
    # 实时查价接口测试用例

    # 测试1：实时查价 - 实际重量大于体积重量
    test_realtime_price_query "实时查价实际重量大于体积重量" '{
        "sender": {
            "name": "张三",
            "mobile": "13800138000",
            "province": "北京市",
            "city": "北京市",
            "district": "朝阳区",
            "address": "三里屯街道1号"
        },
        "receiver": {
            "name": "李四",
            "mobile": "13900139000",
            "province": "上海市",
            "city": "上海市",
            "district": "浦东新区",
            "address": "陆家嘴街道2号"
        },
        "weight": 13.0,
        "length": 30.0,
        "width": 20.0,
        "height": 10.0,
        "goods_name": "重货测试",
        "quantity": 1,
        "pay_method": 0
    }'
    
    # 👉 仅保留一个核心场景，更多场景请根据需要手动添加
    
    print_separator
    log_info "📊 测试结果分析和对比"
    print_separator
    
    echo -e "${CYAN}接口对比分析：${NC}"
    echo "1. 📋 标准查价接口 (QUERY_PRICE)"
    echo "   - 支持快递公司：所有启用的快递公司（除JD/DBL）"
    echo "   - 缓存策略：启用缓存，提升查询性能"
    echo "   - 适用场景：常规业务查价，支持批量查询"
    echo "   - 响应时间：快（缓存加速）"
    echo ""
    echo "2. 🟠 京东专用查价接口 (QUERY_JD_PRICE)"
    echo "   - 支持快递公司：京东快递(JD)、德邦快递(DBL)"
    echo "   - 缓存策略：禁用缓存，强制实时查询"
    echo "   - 适用场景：高精度计费，与下单价格完全一致"
    echo "   - 响应时间：较慢（实时API调用）"
    echo "   - 特殊功能：熔断器保护、重试机制、超时控制"
    echo ""
    echo "3. 🔧 体积重量计算规则"
    echo "   - 京东快递抛比：8000（体积重量 = 长×宽×高÷8000）"
    echo "   - 德邦快递抛比：6000（体积重量 = 长×宽×高÷6000）"
    echo "   - 计费重量：取实际重量和体积重量的最大值"
    echo ""
    echo "4. 🎯 使用建议"
    echo "   - 日常查价：使用标准查价接口，性能更好"
    echo "   - 京东/德邦查价：使用京东专用接口，价格更准确"
    echo "   - 下单前验证：使用对应的专用接口确保价格一致性"
    
    print_separator
    log_success "✅ 所有测试完成！"
    print_separator
}

# 检查依赖
check_dependencies() {
    if ! command -v curl &> /dev/null; then
        log_error "curl 命令未找到，请安装 curl"
        exit 1
    fi
    
    if ! command -v jq &> /dev/null; then
        log_error "jq 命令未找到，请安装 jq"
        exit 1
    fi
}

# 脚本入口
echo -e "${CYAN}Go-Kuaidi 统一网关查价接口完整测试脚本${NC}"
echo -e "${CYAN}测试两个核心接口：标准查价 + 京东专用查价${NC}"
echo ""

# 检查依赖
check_dependencies

# 执行主函数
main 