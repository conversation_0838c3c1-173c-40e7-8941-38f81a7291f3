#!/bin/bash

# =====================================================
# Go-Kuaidi 单快递公司查询调试脚本
# 测试目标：深入验证单快递公司查询功能
# =====================================================

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 配置
BASE_URL="http://localhost:8081"
LOGIN_USERNAME="admin"
LOGIN_PASSWORD="1104030777+.aA..@"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_test() {
    echo -e "${PURPLE}[TEST]${NC} $1"
}

# 获取JWT Token
get_jwt_token() {
    log_info "正在获取JWT Token..."
    
    TOKEN_RESPONSE=$(curl -s -X POST "${BASE_URL}/api/v1/auth/login" \
        -H "Content-Type: application/json" \
        -d "{
            \"username\": \"${LOGIN_USERNAME}\",
            \"password\": \"${LOGIN_PASSWORD}\"
        }")

    TOKEN=$(echo $TOKEN_RESPONSE | jq -r '.access_token')

    if [ "$TOKEN" = "null" ] || [ -z "$TOKEN" ]; then
        log_error "获取Token失败: $TOKEN_RESPONSE"
        exit 1
    fi

    log_success "Token获取成功"
}

# 测试单快递公司查询
test_single_express_detailed() {
    local express_code=$1
    local express_name=$2
    
    log_test "🔍 详细测试单快递公司查询: ${express_name} (${express_code})"
    
    local response=$(curl -s -X POST "${BASE_URL}/api/gateway/execute" \
        -H "Content-Type: application/json" \
        -d "{
            \"apiMethod\": \"QUERY_PRICE\",
            \"clientType\": \"web\",
            \"accessToken\": \"$TOKEN\",
            \"businessParams\": {
                \"express_code\": \"${express_code}\",
                \"from_province\": \"广东省\",
                \"from_city\": \"深圳市\",
                \"from_district\": \"南山区\",
                \"to_province\": \"北京市\",
                \"to_city\": \"北京市\",
                \"to_district\": \"朝阳区\",
                \"weight\": 1.5,
                \"goods_name\": \"测试商品\"
            }
        }")
    
    echo "========== 原始响应 =========="
    echo "$response"
    echo ""
    
    # 解析嵌套的响应结构
    local outer_success=$(echo "$response" | jq -r '.success // false')
    local outer_data=$(echo "$response" | jq -r '.data // {}')
    
    echo "========== 外层响应分析 =========="
    echo "外层success: $outer_success"
    
    if [ "$outer_success" = "true" ]; then
        # 获取内层响应
        local inner_success=$(echo "$response" | jq -r '.data.success // false')
        local inner_message=$(echo "$response" | jq -r '.data.message // "无消息"')
        local inner_data=$(echo "$response" | jq -r '.data.data // null')
        
        echo "内层success: $inner_success"
        echo "内层message: $inner_message"
        
        if [ "$inner_success" = "true" ] && [ "$inner_data" != "null" ]; then
            local data_length=$(echo "$response" | jq -r '.data.data | length')
            log_success "✅ 单快递查询成功 - 返回 ${data_length} 个结果"
            
            # 分析返回的快递公司代码
            echo "========== 快递公司代码分析 =========="
            echo "$response" | jq -r '.data.data[].express_code' | sort | uniq -c
            
            # 验证是否只返回了指定的快递公司
            local unique_codes=$(echo "$response" | jq -r '.data.data[].express_code' | sort | uniq)
            local code_count=$(echo "$unique_codes" | wc -l | tr -d ' ')
            
            echo "唯一快递代码: $unique_codes"
            echo "快递公司数量: $code_count"
            
            if [ "$code_count" = "1" ] && echo "$unique_codes" | grep -q "^${express_code}$"; then
                log_success "✅ 单快递查询验证通过 - 只返回指定快递公司 ${express_code}"
            else
                log_error "❌ 单快递查询验证失败 - 预期只有 ${express_code}，实际返回: ${unique_codes}"
            fi
            
            # 显示价格详情
            echo "========== 价格选项详情 =========="
            echo "$response" | jq -r '.data.data[] | "快递: \(.express_name), 产品: \(.product_name), 价格: ¥\(.price)"'
            
        else
            log_error "❌ 内层查询失败: ${inner_message}"
        fi
    else
        log_error "❌ 外层请求失败"
    fi
    
    echo ""
}

# 测试全部快递查询
test_all_express_detailed() {
    log_test "🔍 详细测试全部快递公司查询"
    
    local response=$(curl -s -X POST "${BASE_URL}/api/gateway/execute" \
        -H "Content-Type: application/json" \
        -d "{
            \"apiMethod\": \"QUERY_PRICE\",
            \"clientType\": \"web\",
            \"accessToken\": \"$TOKEN\",
            \"businessParams\": {
                \"from_province\": \"广东省\",
                \"from_city\": \"深圳市\",
                \"from_district\": \"南山区\",
                \"to_province\": \"北京市\",
                \"to_city\": \"北京市\",
                \"to_district\": \"朝阳区\",
                \"weight\": 1.5,
                \"goods_name\": \"测试商品\"
            }
        }")
    
    echo "========== 全部快递查询原始响应 =========="
    echo "$response" | jq '.'
    echo ""
    
    local outer_success=$(echo "$response" | jq -r '.success // false')
    
    if [ "$outer_success" = "true" ]; then
        local inner_success=$(echo "$response" | jq -r '.data.success // false')
        local inner_message=$(echo "$response" | jq -r '.data.message // "无消息"')
        
        if [ "$inner_success" = "true" ]; then
            local data_length=$(echo "$response" | jq -r '.data.data | length')
            log_success "✅ 全部快递查询成功 - 返回 ${data_length} 个结果"
            
            # 统计不同快递公司
            echo "========== 快递公司统计 =========="
            echo "$response" | jq -r '.data.data[].express_code' | sort | uniq -c | sort -nr
            
            local unique_count=$(echo "$response" | jq -r '.data.data[].express_code' | sort | uniq | wc -l | tr -d ' ')
            log_info "总共包含 ${unique_count} 个不同的快递公司"
            
            # 显示每个快递公司的最低价格
            echo "========== 各快递公司最低价格 =========="
            echo "$response" | jq -r '.data.data | group_by(.express_code) | map({express_code: .[0].express_code, express_name: .[0].express_name, min_price: (map(.price) | min)}) | sort_by(.min_price) | .[] | "\(.express_name) (\(.express_code)): ¥\(.min_price)"'
            
        else
            log_error "❌ 全部快递查询失败: ${inner_message}"
        fi
    else
        log_error "❌ 请求失败"
    fi
    
    echo ""
}

# 对比单快递查询与全快递查询
compare_single_vs_all() {
    local express_code=$1
    local express_name=$2
    
    log_test "🔄 对比单快递查询 vs 全快递查询: ${express_name} (${express_code})"
    
    # 单快递查询
    local single_response=$(curl -s -X POST "${BASE_URL}/api/gateway/execute" \
        -H "Content-Type: application/json" \
        -d "{
            \"apiMethod\": \"QUERY_PRICE\",
            \"clientType\": \"web\",
            \"accessToken\": \"$TOKEN\",
            \"businessParams\": {
                \"express_code\": \"${express_code}\",
                \"from_province\": \"广东省\",
                \"from_city\": \"深圳市\",
                \"to_province\": \"北京市\",
                \"to_city\": \"北京市\",
                \"weight\": 1.0,
                \"goods_name\": \"对比测试\"
            }
        }")
    
    # 全快递查询
    local all_response=$(curl -s -X POST "${BASE_URL}/api/gateway/execute" \
        -H "Content-Type: application/json" \
        -d "{
            \"apiMethod\": \"QUERY_PRICE\",
            \"clientType\": \"web\",
            \"accessToken\": \"$TOKEN\",
            \"businessParams\": {
                \"from_province\": \"广东省\",
                \"from_city\": \"深圳市\",  
                \"to_province\": \"北京市\",
                \"to_city\": \"北京市\",
                \"weight\": 1.0,
                \"goods_name\": \"对比测试\"
            }
        }")
    
    # 分析单快递结果
    local single_success=$(echo "$single_response" | jq -r '.data.success // false')  
    local single_count=0
    if [ "$single_success" = "true" ]; then
        single_count=$(echo "$single_response" | jq -r '.data.data | length')
    fi
    
    # 分析全快递结果
    local all_success=$(echo "$all_response" | jq -r '.data.success // false')
    local all_count=0
    local target_count=0
    if [ "$all_success" = "true" ]; then
        all_count=$(echo "$all_response" | jq -r '.data.data | length')
        target_count=$(echo "$all_response" | jq -r ".data.data | map(select(.express_code == \"${express_code}\")) | length")
    fi
    
    echo "========== 对比结果 =========="
    echo "单快递查询成功: $single_success, 结果数: $single_count"
    echo "全快递查询成功: $all_success, 结果数: $all_count"
    echo "全快递查询中 ${express_code} 的数量: $target_count"
    
    # 验证单快递查询确实触发了单快递逻辑
    if [ "$single_success" = "true" ] && [ "$single_count" -gt 0 ]; then
        local single_codes=$(echo "$single_response" | jq -r '.data.data[].express_code' | sort | uniq)
        if echo "$single_codes" | grep -q "^${express_code}$" && [ "$(echo "$single_codes" | wc -l | tr -d ' ')" = "1" ]; then
            log_success "✅ 单快递查询确实只查询了指定快递公司"
        else
            log_error "❌ 单快递查询返回了其他快递公司: $single_codes"
        fi
    fi
    
    # 性能对比
    echo "========== 性能指标对比 =========="
    echo "单快递查询应该比全快递查询更快，返回结果更少"
    
    echo ""
}

# 主函数
main() {
    echo -e "${CYAN}=================================================${NC}"
    log_info "🚀 Go-Kuaidi 单快递公司查询深度调试"
    echo -e "${CYAN}=================================================${NC}"
    
    get_jwt_token
    
    echo -e "${CYAN}=============== 详细单快递测试 ===============${NC}"
    test_single_express_detailed "ZTO" "中通快递"
    test_single_express_detailed "YTO" "圆通速递"
    test_single_express_detailed "STO" "申通快递"
    
    echo -e "${CYAN}=============== 详细全快递测试 ===============${NC}"
    test_all_express_detailed
    
    echo -e "${CYAN}=============== 单快递 vs 全快递对比 ===============${NC}"
    compare_single_vs_all "ZTO" "中通快递"
    
    echo -e "${CYAN}=================================================${NC}"
    log_success "✅ 深度调试测试完成！"
    echo -e "${CYAN}=================================================${NC}"
}

# 执行主函数
main