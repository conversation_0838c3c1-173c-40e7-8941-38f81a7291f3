#!/bin/bash

# 🔍 Go代码垃圾分析脚本
# 使用多种工具全面分析代码质量和垃圾代码

set -e

echo "🚀 开始Go代码垃圾分析..."
echo "=================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 创建报告目录
REPORT_DIR="code_analysis_reports"
mkdir -p "$REPORT_DIR"
cd "$REPORT_DIR"

echo -e "${BLUE}📁 报告将保存到: $(pwd)${NC}"
echo ""

# 回到项目根目录
cd ..

# 1. 基础Go工具检查
echo -e "${BLUE}📋 1. 基础Go工具检查...${NC}"
echo "go vet 检查结果:" > "$REPORT_DIR/01_go_vet.txt"
go vet ./... 2>&1 | tee -a "$REPORT_DIR/01_go_vet.txt" || true

echo "go fmt 检查结果:" > "$REPORT_DIR/02_go_fmt.txt"
gofmt -l . 2>&1 | tee -a "$REPORT_DIR/02_go_fmt.txt" || true

echo -e "${GREEN}✅ 基础检查完成${NC}"
echo ""

# 2. staticcheck 静态分析
echo -e "${BLUE}🔬 2. staticcheck 静态分析...${NC}"
echo "staticcheck 完整分析:" > "$REPORT_DIR/03_staticcheck_full.txt"
~/go/bin/staticcheck ./... 2>&1 | tee -a "$REPORT_DIR/03_staticcheck_full.txt" || true

echo "未使用函数检查:" > "$REPORT_DIR/04_unused_functions.txt"
~/go/bin/staticcheck -checks=U1000 ./... 2>&1 | tee -a "$REPORT_DIR/04_unused_functions.txt" || true

echo "未使用变量检查:" > "$REPORT_DIR/05_unused_variables.txt"
~/go/bin/staticcheck -checks=U1001 ./... 2>&1 | tee -a "$REPORT_DIR/05_unused_variables.txt" || true

echo "未使用常量检查:" > "$REPORT_DIR/06_unused_constants.txt"
~/go/bin/staticcheck -checks=U1002 ./... 2>&1 | tee -a "$REPORT_DIR/06_unused_constants.txt" || true

echo -e "${GREEN}✅ staticcheck 分析完成${NC}"
echo ""

# 3. golangci-lint 综合分析
echo -e "${BLUE}🛠️ 3. golangci-lint 综合分析...${NC}"
echo "golangci-lint 完整分析:" > "$REPORT_DIR/07_golangci_lint_full.txt"
~/go/bin/golangci-lint run 2>&1 | tee -a "$REPORT_DIR/07_golangci_lint_full.txt" || true

echo "专门检查未使用代码:" > "$REPORT_DIR/08_golangci_unused.txt"
~/go/bin/golangci-lint run --enable=unused,deadcode,varcheck,structcheck,ineffassign 2>&1 | tee -a "$REPORT_DIR/08_golangci_unused.txt" || true

echo -e "${GREEN}✅ golangci-lint 分析完成${NC}"
echo ""

# 4. 依赖分析
echo -e "${BLUE}📦 4. 依赖关系分析...${NC}"
echo "模块依赖分析:" > "$REPORT_DIR/09_dependencies.txt"
go list -f '{{.ImportPath}}: {{.Imports}}' ./... 2>&1 | tee -a "$REPORT_DIR/09_dependencies.txt" || true

echo "go mod tidy 检查:" > "$REPORT_DIR/10_mod_tidy.txt"
go mod tidy 2>&1 | tee -a "$REPORT_DIR/10_mod_tidy.txt" || true

echo -e "${GREEN}✅ 依赖分析完成${NC}"
echo ""

# 5. 测试覆盖率分析
echo -e "${BLUE}🧪 5. 测试覆盖率分析...${NC}"
echo "测试覆盖率分析:" > "$REPORT_DIR/11_test_coverage.txt"
go test -coverprofile="$REPORT_DIR/coverage.out" ./... 2>&1 | tee -a "$REPORT_DIR/11_test_coverage.txt" || true

if [ -f "$REPORT_DIR/coverage.out" ]; then
    go tool cover -html="$REPORT_DIR/coverage.out" -o "$REPORT_DIR/coverage.html"
    echo "覆盖率HTML报告已生成: $REPORT_DIR/coverage.html"
fi

echo -e "${GREEN}✅ 测试覆盖率分析完成${NC}"
echo ""

# 6. 生成汇总报告
echo -e "${BLUE}📊 6. 生成汇总报告...${NC}"

cat > "$REPORT_DIR/00_SUMMARY.md" << 'EOF'
# 🔍 Go代码质量分析报告

## 📋 分析概览

本报告包含以下分析结果：

### 🛠️ 工具使用
- **go vet**: Go官方代码检查工具
- **staticcheck**: 专业静态分析工具
- **golangci-lint**: 集成多种检查器的综合工具
- **go test -cover**: 测试覆盖率分析

### 📁 报告文件说明

| 文件 | 说明 |
|------|------|
| `01_go_vet.txt` | Go官方vet工具检查结果 |
| `02_go_fmt.txt` | 代码格式检查结果 |
| `03_staticcheck_full.txt` | staticcheck完整分析 |
| `04_unused_functions.txt` | 未使用的函数 |
| `05_unused_variables.txt` | 未使用的变量 |
| `06_unused_constants.txt` | 未使用的常量 |
| `07_golangci_lint_full.txt` | golangci-lint完整分析 |
| `08_golangci_unused.txt` | 专门的未使用代码检查 |
| `09_dependencies.txt` | 依赖关系分析 |
| `10_mod_tidy.txt` | 模块清理结果 |
| `11_test_coverage.txt` | 测试覆盖率分析 |
| `coverage.html` | 测试覆盖率可视化报告 |

### 🎯 重点关注

1. **未使用代码**: 查看 `04_unused_functions.txt`, `05_unused_variables.txt`, `08_golangci_unused.txt`
2. **代码质量**: 查看 `03_staticcheck_full.txt`, `07_golangci_lint_full.txt`
3. **测试覆盖**: 查看 `coverage.html`

### 📊 分析建议

根据分析结果，建议按以下优先级处理：

1. **高优先级**: 删除完全未使用的函数和变量
2. **中优先级**: 修复静态分析发现的问题
3. **低优先级**: 改进代码风格和格式

EOF

echo -e "${GREEN}✅ 汇总报告生成完成${NC}"
echo ""

# 7. 显示结果统计
echo -e "${YELLOW}📊 分析结果统计:${NC}"
echo "=================================="

if [ -f "$REPORT_DIR/04_unused_functions.txt" ]; then
    UNUSED_FUNCS=$(grep -c "is unused" "$REPORT_DIR/04_unused_functions.txt" 2>/dev/null || echo "0")
    echo -e "🗑️  未使用函数: ${RED}$UNUSED_FUNCS${NC} 个"
fi

if [ -f "$REPORT_DIR/05_unused_variables.txt" ]; then
    UNUSED_VARS=$(grep -c "is unused" "$REPORT_DIR/05_unused_variables.txt" 2>/dev/null || echo "0")
    echo -e "🗑️  未使用变量: ${RED}$UNUSED_VARS${NC} 个"
fi

if [ -f "$REPORT_DIR/07_golangci_lint_full.txt" ]; then
    TOTAL_ISSUES=$(grep -c "^.*\.go:" "$REPORT_DIR/07_golangci_lint_full.txt" 2>/dev/null || echo "0")
    echo -e "⚠️  代码质量问题: ${YELLOW}$TOTAL_ISSUES${NC} 个"
fi

echo ""
echo -e "${GREEN}🎉 分析完成！${NC}"
echo -e "${BLUE}📁 所有报告已保存到: $REPORT_DIR/${NC}"
echo -e "${BLUE}🌐 查看测试覆盖率: open $REPORT_DIR/coverage.html${NC}"
echo ""
echo -e "${YELLOW}💡 下一步建议:${NC}"
echo "1. 查看 $REPORT_DIR/00_SUMMARY.md 了解分析概览"
echo "2. 重点关注未使用代码文件"
echo "3. 根据优先级逐步清理代码"
