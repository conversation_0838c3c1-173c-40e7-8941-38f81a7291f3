#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import psycopg2
import urllib.parse
import json

# 数据库连接配置
CALLBACK_DB_CONFIG = {
    'host': '*************',
    'port': 5432,
    'user': 'postgres',
    'password': 'gjx6ngf4',
    'database': 'callback_receiver'
}

MAIN_DB_CONFIG = {
    'host': '*************',
    'port': 5432,
    'user': 'postgres',
    'password': 'gjx6ngf4',
    'database': 'go_kuaidi'
}

def test_single_order(tracking_no):
    """测试单个订单的状态分析"""
    print(f"🔍 分析订单: {tracking_no}")
    
    try:
        # 1. 查询主数据库
        conn = psycopg2.connect(**MAIN_DB_CONFIG)
        cursor = conn.cursor()
        
        query = """
        SELECT customer_order_no, order_no, provider, status, updated_at 
        FROM order_records 
        WHERE tracking_no = %s
        """
        
        cursor.execute(query, (tracking_no,))
        order_info = cursor.fetchone()
        
        cursor.close()
        conn.close()
        
        if order_info:
            customer_order_no, order_no, provider, status, updated_at = order_info
            print(f"   📋 主数据库: {customer_order_no} | {status} | {updated_at}")
        else:
            print(f"   ❌ 主数据库中没有找到订单")
            return
        
        # 2. 查询回调数据库
        conn = psycopg2.connect(**CALLBACK_DB_CONFIG)
        cursor = conn.cursor()
        
        query = """
        SELECT id, received_at, raw_body, processed
        FROM callback_raw_data 
        WHERE provider = 'kuaidi100' 
          AND raw_body LIKE %s 
        ORDER BY received_at DESC 
        LIMIT 1
        """
        
        cursor.execute(query, (f'%{tracking_no}%',))
        callback_info = cursor.fetchone()
        
        cursor.close()
        conn.close()
        
        if callback_info:
            callback_id, received_at, raw_body, processed = callback_info
            print(f"   📤 最新回调: ID={callback_id}, 时间={received_at}, 已处理={processed}")
            
            # 3. 解码回调数据
            try:
                if 'param=' in raw_body:
                    param_start = raw_body.find('param=') + 6
                    param_end = raw_body.find('&', param_start)
                    if param_end == -1:
                        param_end = len(raw_body)
                    
                    encoded_param = raw_body[param_start:param_end]
                    decoded_param = urllib.parse.unquote(encoded_param)
                    
                    data = json.loads(decoded_param)
                    callback_data = data.get('data', {})
                    
                    callback_status = callback_data.get('status')
                    sent_status = callback_data.get('sentStatus')
                    cancel_msg = callback_data.get('cancelMsg', '')
                    
                    # 状态映射
                    status_mapping = {
                        0: "awaiting_pickup",  # 待处理
                        1: "assigned",         # 已接单
                        2: "picked_up",        # 已揽收
                        3: "in_transit",       # 运输中
                        4: "delivered",        # 已签收
                        5: "exception",        # 异常
                        6: "returned",         # 退回
                        7: "cancelled"         # 取消
                    }
                    
                    expected_status = status_mapping.get(callback_status, "unknown")
                    print(f"   📊 回调状态: {callback_status} -> {expected_status}")
                    print(f"   📊 发送状态: {sent_status}")
                    
                    if cancel_msg:
                        print(f"   ❌ 取消原因: {cancel_msg}")
                    
                    # 4. 状态对比
                    if status != expected_status:
                        print(f"   ⚠️  状态不同步: 主数据库={status}, 回调={expected_status}")
                        print(f"   🔄 需要重新推送回调")
                        return True  # 需要重新推送
                    else:
                        print(f"   ✅ 状态已同步")
                        return False  # 不需要重新推送
                        
            except Exception as e:
                print(f"   ❌ 解码回调数据失败: {e}")
                return False
        else:
            print(f"   ❌ 没有找到相关回调")
            return False
            
    except Exception as e:
        print(f"   ❌ 数据库查询失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 测试订单状态分析")
    print("=" * 40)
    
    # 测试几个订单
    test_orders = [
        'DPK202580385165',
        'DPK202580340237',
        'DPK202579994323'
    ]
    
    need_repush = []
    
    for tracking_no in test_orders:
        if test_single_order(tracking_no):
            need_repush.append(tracking_no)
        print()
    
    print(f"📊 需要重新推送的订单: {len(need_repush)}")
    for order in need_repush:
        print(f"   - {order}")

if __name__ == "__main__":
    main()
