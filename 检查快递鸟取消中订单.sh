#!/bin/bash

# 检查快递鸟取消中订单脚本
# 用于分析卡在cancelling状态的快递鸟订单

echo "🔍 检查快递鸟取消中订单"
echo "========================"

# 数据库连接信息
DB_HOST="*************"
DB_PORT="5432"
DB_USER="postgres"
DB_NAME="go_kuaidi"
DB_PASSWORD="gjx6ngf4"

echo "📊 1. 统计快递鸟各状态订单数量..."
echo ""
PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "
SELECT status, COUNT(*) as count 
FROM order_records 
WHERE provider = 'kuaidiniao' 
GROUP BY status 
ORDER BY count DESC;
"

echo ""
echo "📋 2. 详细查看取消中订单..."
echo ""
PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "
SELECT 
    customer_order_no,
    order_no,
    tracking_no,
    status,
    created_at,
    updated_at,
    EXTRACT(EPOCH FROM (NOW() - updated_at))/3600 as hours_since_update
FROM order_records 
WHERE provider = 'kuaidiniao' AND status = 'cancelling' 
ORDER BY updated_at DESC 
LIMIT 10;
"

echo ""
echo "📈 3. 按更新时间分组统计取消中订单..."
echo ""
PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "
SELECT
    CASE
        WHEN updated_at > NOW() - INTERVAL '1 hour' THEN '1小时内'
        WHEN updated_at > NOW() - INTERVAL '6 hours' THEN '1-6小时'
        WHEN updated_at > NOW() - INTERVAL '24 hours' THEN '6-24小时'
        ELSE '超过24小时'
    END as time_range,
    COUNT(*) as count
FROM order_records
WHERE provider = 'kuaidiniao' AND status = 'cancelling'
GROUP BY 1
ORDER BY
    CASE
        WHEN CASE
            WHEN updated_at > NOW() - INTERVAL '1 hour' THEN '1小时内'
            WHEN updated_at > NOW() - INTERVAL '6 hours' THEN '1-6小时'
            WHEN updated_at > NOW() - INTERVAL '24 hours' THEN '6-24小时'
            ELSE '超过24小时'
        END = '1小时内' THEN 1
        WHEN CASE
            WHEN updated_at > NOW() - INTERVAL '1 hour' THEN '1小时内'
            WHEN updated_at > NOW() - INTERVAL '6 hours' THEN '1-6小时'
            WHEN updated_at > NOW() - INTERVAL '24 hours' THEN '6-24小时'
            ELSE '超过24小时'
        END = '1-6小时' THEN 2
        WHEN CASE
            WHEN updated_at > NOW() - INTERVAL '1 hour' THEN '1小时内'
            WHEN updated_at > NOW() - INTERVAL '6 hours' THEN '1-6小时'
            WHEN updated_at > NOW() - INTERVAL '24 hours' THEN '6-24小时'
            ELSE '超过24小时'
        END = '6-24小时' THEN 3
        ELSE 4
    END;
"

echo ""
echo "🔧 4. 识别需要修复的订单（超过1小时）..."
echo ""
NEED_FIX_COUNT=$(PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -t -c "
SELECT COUNT(*) 
FROM order_records 
WHERE provider = 'kuaidiniao' 
  AND status = 'cancelling' 
  AND updated_at < NOW() - INTERVAL '1 hour';
" | tr -d ' ')

echo "需要修复的订单数量: $NEED_FIX_COUNT"

if [ "$NEED_FIX_COUNT" -gt 0 ]; then
    echo ""
    echo "📋 需要修复的订单列表:"
    PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "
    SELECT 
        customer_order_no,
        order_no,
        tracking_no,
        EXTRACT(EPOCH FROM (NOW() - updated_at))/3600 as hours_since_update
    FROM order_records 
    WHERE provider = 'kuaidiniao' 
      AND status = 'cancelling' 
      AND updated_at < NOW() - INTERVAL '1 hour'
    ORDER BY updated_at ASC;
    "
    
    echo ""
    echo "💡 建议执行修复脚本: ./快递鸟取消中订单诊断修复脚本.sh"
else
    echo "✅ 所有取消中订单都是1小时内的，可能正在正常处理中"
fi

echo ""
echo "🏁 检查完成！"
