#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import urllib.parse
import json

# 从数据库获取的原始回调数据
raw_body = "param=%7B%22data%22%3A%7B%22cancelMsg%22%3A%22%22%2C%22orderId%22%3A%22296416979%22%2C%22courierMobile%22%3A%2216621923715%22%2C%22freight%22%3Anull%2C%22weight%22%3Anull%2C%22pollToken%22%3A%22QDAM73ZuZLfF3Gei48aBtXMLNH7%2FPdHThINYEVanrA0%3D%22%2C%22updateTime%22%3A1754008620550%2C%22sentStatus%22%3A2%2C%22userCancel%22%3Afalse%2C%22courierName%22%3A%22%E4%B8%98%E4%BB%81%E5%AE%BD%22%2C%22defPrice%22%3Anull%2C%22thirdOrderId%22%3A%22GK20250731000003054%22%2C%22comment%22%3A%22%22%2C%22payStatus%22%3A0%2C%22status%22%3A2%7D%2C%22kuaidicom%22%3A%22debangkuaidi%22%2C%22kuaidinum%22%3A%22DPK202580385165%22%2C%22message%22%3A%22%E6%88%90%E5%8A%9F%22%2C%22status%22%3A%22200%22%7D&sign=C683267B94FA5D4DBC0102AE3DED3911&taskId=453585FE2B4E9EED71276DF380CF7EFB"

def decode_callback():
    """解码回调数据"""
    print("🔍 解码快递100回调数据")
    print("=" * 40)
    
    # 提取param参数
    if 'param=' in raw_body:
        param_start = raw_body.find('param=') + 6
        param_end = raw_body.find('&', param_start)
        if param_end == -1:
            param_end = len(raw_body)
        
        encoded_param = raw_body[param_start:param_end]
        print(f"📋 编码的参数: {encoded_param[:100]}...")
        
        # URL解码
        decoded_param = urllib.parse.unquote(encoded_param)
        print(f"📋 解码后的JSON: {decoded_param}")
        
        # 解析JSON
        try:
            data = json.loads(decoded_param)
            print(f"📋 解析后的数据:")
            print(json.dumps(data, indent=2, ensure_ascii=False))
            
            # 分析关键字段
            callback_data = data.get('data', {})
            print(f"\n📊 关键信息分析:")
            print(f"   - 订单ID: {callback_data.get('orderId')}")
            print(f"   - 第三方订单号: {callback_data.get('thirdOrderId')}")
            print(f"   - 运单号: {data.get('kuaidinum')}")
            print(f"   - 快递公司: {data.get('kuaidicom')}")
            print(f"   - 状态: {callback_data.get('status')}")
            print(f"   - 发送状态: {callback_data.get('sentStatus')}")
            print(f"   - 快递员: {callback_data.get('courierName')}")
            print(f"   - 快递员电话: {callback_data.get('courierMobile')}")
            print(f"   - 取消消息: {callback_data.get('cancelMsg')}")
            print(f"   - 用户取消: {callback_data.get('userCancel')}")
            
            # 状态映射
            status = callback_data.get('status')
            status_mapping = {
                0: "awaiting_pickup (待处理)",
                1: "assigned (已接单)",
                2: "picked_up (已揽收)",
                3: "in_transit (运输中)",
                4: "delivered (已签收)",
                5: "exception (异常)",
                6: "returned (退回)",
                7: "cancelled (取消)"
            }
            
            mapped_status = status_mapping.get(status, f"unknown ({status})")
            print(f"   - 状态映射: {status} -> {mapped_status}")
            
            sent_status = callback_data.get('sentStatus')
            sent_mapping = {
                0: "未发送",
                1: "发送中", 
                2: "已发送"
            }
            
            mapped_sent = sent_mapping.get(sent_status, f"unknown ({sent_status})")
            print(f"   - 发送状态映射: {sent_status} -> {mapped_sent}")
            
            return data
            
        except json.JSONDecodeError as e:
            print(f"❌ JSON解析失败: {e}")
            return None
    else:
        print("❌ 没有找到param参数")
        return None

if __name__ == "__main__":
    decode_callback()
